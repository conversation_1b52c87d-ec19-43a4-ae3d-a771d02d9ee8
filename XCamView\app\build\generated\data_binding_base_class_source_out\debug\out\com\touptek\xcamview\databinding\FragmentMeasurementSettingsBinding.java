// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentMeasurementSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final RadioGroup modeRadioGroup;

  @NonNull
  public final RadioButton radioA;

  @NonNull
  public final RadioButton radioB;

  private FragmentMeasurementSettingsBinding(@NonNull ScrollView rootView,
      @NonNull RadioGroup modeRadioGroup, @NonNull RadioButton radioA,
      @NonNull RadioButton radioB) {
    this.rootView = rootView;
    this.modeRadioGroup = modeRadioGroup;
    this.radioA = radioA;
    this.radioB = radioB;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentMeasurementSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentMeasurementSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_measurement_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentMeasurementSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.mode_radio_group;
      RadioGroup modeRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (modeRadioGroup == null) {
        break missingId;
      }

      id = R.id.radio_A;
      RadioButton radioA = ViewBindings.findChildViewById(rootView, id);
      if (radioA == null) {
        break missingId;
      }

      id = R.id.radio_B;
      RadioButton radioB = ViewBindings.findChildViewById(rootView, id);
      if (radioB == null) {
        break missingId;
      }

      return new FragmentMeasurementSettingsBinding((ScrollView) rootView, modeRadioGroup, radioA,
          radioB);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.touptek.video.internal.service;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;

import com.touptek.utils.TpFileManager;
import com.touptek.video.TpIspParam;
import com.touptek.video.internal.TpCaptureImage;
import com.touptek.video.internal.TpVideoEncoder;
import com.touptek.video.internal.rtsp.TpRtspManager;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 流媒体推送服务
 * 
 * <p>此服务专门用于管理和控制媒体流推送，通过心跳包检测机制自动启动：</p>
 * <ul>
 *   <li>RTSP推流服务（支持摄像头流和屏幕流切换）</li>
 *   <li>StreamingSocketService图像服务</li>
 * </ul>
 * 
 * <p>集成了RTSP管理功能，提供统一的流类型控制接口</p>
 * 
 * <p>使用方法（两步初始化）：</p>
 * <pre>{@code
 *  第一步：在Activity的onCreate()中调用，初始化不依赖其他组件的部分
 * TpStreamingService service = TpStreamingService.initCore(this, listener);
 * 
 *  第二步：在VideoEncoder和CaptureImageHelper准备好后调用，完成初始化
 * service.initStreaming(tpVideoEncoder, tpCaptureImage);
 * service.startService(); // 启动服务
 * }</pre>
 * 
 * <div style="background-color:#f8f8f8;padding:8px;margin:8px 0;">
 * <strong>注意：</strong>需要在模块的build.gradle中导入以下依赖：<br>
 * implementation("com.github.pedroSG94.RootEncoder:library:2.3.5")<br>
 * implementation("com.github.pedroSG94:RTSP-Server:1.2.1")
 * 使用屏幕推流需要在AndroidManifest中做声明
 *    要加入:service
 *       android:name="com.android.rockchip.camera2.rtsp.service.RTSPService"
 *       android:enabled="true"
 *       android:exported="false"
 *       android:foregroundServiceType="mediaProjection"
 *       android:stopWithTask="false" />
 * </div>
 */
public class TpStreamingService
{
    private static final String TAG = "TpStreamingService";
    
    /* 心跳检测超时时间（毫秒） */
    private static final long HEARTBEAT_TIMEOUT = 1500; // 1.5秒
    
    /* 心跳包监听端口号 - 专用于心跳检测 */
    private static final int SOCKET_PORT = 12346;
    
    /* ISP参数服务端口号 */
    private static final int ISP_PARAM_PORT = 12347;

    /* TpctrlSocketService使用的图像服务端口号 */
    private static final int IMAGE_PORT = 12345;

    /* 心跳包命令码 */
    private static final int CMD_HEARTBEAT = 2;
    
    /* ISP 参数配置命令码 - 发送最大值、最小值、默认值等配置信息 */
    private static final int CMD_ISP_PARAM_CONFIG = 4;
    
    /* tpctrl的IP地址 */
    private static final String TPCTRL_IP = "127.0.0.1";
    
    /* 原子状态变量 */
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean servicesStarted = new AtomicBoolean(false);
    private final AtomicBoolean tpctrlExists = new AtomicBoolean(false);
    private final AtomicBoolean socketServerRunning = new AtomicBoolean(false);
    private final AtomicLong lastHeartbeatTime = new AtomicLong(0);
    
    /* 初始化状态标记 */
    private final AtomicBoolean earlyInitialized = new AtomicBoolean(false);
    private final AtomicBoolean fullyInitialized = new AtomicBoolean(false);
    
    /* 线程池 */
    private ExecutorService executorService;
    private ExecutorService socketExecutor;
    
    /* Socket服务器相关 */
    private ServerSocket serverSocket;
    private Thread socketServerThread;
    
    /* 主线程Handler */
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    /* 应用上下文 */
    private final Context context;
    
    /* Activity引用 */
    private AppCompatActivity activity;
    
    /* RTSP管理器 */
    private TpRtspManager tpRtspManager;
    
    /* 视频编码器 */
    private TpVideoEncoder tpVideoEncoder;
    
    /* 抓图助手 */
    private TpCaptureImage tpCaptureImage;
    
    /* 图像Socket服务 */
    private StreamingSocketService streamingSocketService;
    
    /* 当前流类型 */
    private StreamType currentStreamType = StreamType.CAMERA;
    
    /* 重试计数器 */
    private int retryCount = 0;
    
    /* 心跳超时时间配置 */
    private final long heartbeatTimeout;
    
    /* 端口配置 */
    private final int socketPort;
    private final int ispPort;

    private HeartbeatListener heartbeatListener;
    
    /* 手动控制RTSP流状态 */
    private final AtomicBoolean manualRtspControl = new AtomicBoolean(true); // 默认开启手动控制
    private String selectedNetInterface = null; // 用户选择的网络接口
    
    /* 存储当前运行的tpctrl进程信息 */
    private final java.util.concurrent.ConcurrentHashMap<Integer, Process> runningTpctrlProcesses = new java.util.concurrent.ConcurrentHashMap<>();
    
    /**
     * 流类型枚举
     */
    public enum StreamType 
    {
        /** 摄像头流 */
        CAMERA,
        /** 屏幕流 */
        SCREEN
    }
    
    /**
     * 心跳监听器接口
     * <p>
     * 用于监听心跳超时事件，当客户端连接超时时会触发回调。
     * </p>
     */
    public interface HeartbeatListener 
    {
        /**
         * RTSP推流状态变化回调
         * @param isStreaming 是否正在推流
         * @param url 推流地址（如果isStreaming为false则为null）
         */
        void onStreamStatusChanged(boolean isStreaming, String url);
        
        /**
         * RTSP推流错误回调
         * @param errorMessage 错误信息
         */
        void onStreamError(String errorMessage);
    }
    
    /**
     * 私有构造函数
     */
    private TpStreamingService(AppCompatActivity activity, HeartbeatListener listener)
    {
        this.context = activity;
        this.activity = activity;
        this.heartbeatListener = listener;
        this.heartbeatTimeout = HEARTBEAT_TIMEOUT;
        this.socketPort = SOCKET_PORT;
        this.ispPort = ISP_PARAM_PORT;
    }

    /**
     * 第一步：创建早期实例（必须在Activity的onCreate()中调用）
     * 
     * <p><strong> 重要：必须在Activity的onCreate()方法中调用此方法！</strong></p>
     * <p>这是因为RTSPManager需要注册ActivityResultLauncher，而这必须在Activity进入STARTED状态之前完成。</p>
     * 
     * <p>此方法会初始化：</p>
     * <ul>
     *   <li>基本配置和Context</li>
     *   <li>TpRtspManager（需要registerForActivityResult）</li>
     *   <li>HeartbeatListener</li>
     * </ul>
     * 
     * @param activity Activity实例，用于RTSP屏幕推流权限申请
     * @param listener 心跳状态监听器
     * @return StreamingService实例（尚未完全初始化）
     * 
     * @throws IllegalStateException 如果重复调用或Activity为null
     * 
     * @see #initStreaming(TpVideoEncoder, TpCaptureImage)
     */
    public static TpStreamingService initCore(AppCompatActivity activity,
                                              HeartbeatListener listener)
    {
        if (activity == null) 
        {
            throw new IllegalArgumentException("Activity cannot be null");
        }
        if (listener == null) 
        {
            throw new IllegalArgumentException("HeartbeatListener cannot be null");
        }
        
        Log.d(TAG, "Step 1: Creating early instance of TpStreamingService...");
        
        TpStreamingService service = new TpStreamingService(activity, listener);
        
        try 
        {
            /* 早期初始化RTSPManager（必须在onCreate中完成）*/
            service.initRtspManager();
            service.earlyInitialized.set(true);
            
            Log.d(TAG, "TpStreamingService early initialization completed");
            return service;
            
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "TpStreamingService early initialization failed", e);
            throw new RuntimeException("Early initialization failed", e);
        }
    }

    /**
     * 第二步：完成初始化（必须在VideoEncoder和CaptureImageHelper准备好后调用）
     * 
     * <p><strong>⚠️ 重要：必须在VideoEncoder和CaptureImageHelper完全准备好后调用此方法！</strong></p>
     * <p>通常在视频编码器初始化完成回调中调用。</p>
     * 
     * <p>此方法会初始化：</p>
     * <ul>
     *   <li>VideoEncoder配置</li>
     *   <li>CaptureImageHelper配置</li>
     *   <li>RTSP回调设置</li>
     * </ul>
     * 
     * @param tpVideoEncoder 视频编码器实例
     * @param tpCaptureImage 抓图助手实例
     * @param streamType 初始流类型（可选，默认为CAMERA）
     * 
     * @throws IllegalStateException 如果早期初始化未完成或重复调用
     * @throws IllegalArgumentException 如果必需参数为null
     * 
     * @see #initCore(AppCompatActivity, HeartbeatListener)
     */
    public void initStreaming(TpVideoEncoder tpVideoEncoder,
                              TpCaptureImage tpCaptureImage,
                              StreamType streamType)
    {
        if (!earlyInitialized.get()) 
        {
            throw new IllegalStateException("Must call initCore() first in Activity.onCreate()");
        }
        if (tpCaptureImage == null)
        {
            throw new IllegalArgumentException("TpCaptureImage cannot be null");
        }
        
        Log.d(TAG, "Step 2: Completing TpStreamingService initialization...");
        
        this.tpVideoEncoder = tpVideoEncoder;
        this.tpCaptureImage = tpCaptureImage;
        this.currentStreamType = streamType != null ? streamType : StreamType.CAMERA;

        
        try 
        {
            /* 完成RTSP配置 */
            configureRtspCallbacks();
            
            /* 设置视频编码器 */
            configureVideoEncoder();
            
            fullyInitialized.set(true);
            
            Log.d(TAG, "TpStreamingService fully initialized, ready to call startService()");
            
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "TpStreamingService complete initialization failed", e);
            throw new RuntimeException("Complete initialization failed", e);
        }
    }
    
    /**
     * 便捷的完成初始化方法（默认使用CAMERA流类型）
     * <p>
     * 设置视频编码器和图像捕获助手，完成服务的完整初始化。
     * 此方法使用默认的CAMERA流类型。
     * </p>
     * 
     * @param tpVideoEncoder 视频编码器实例，用于处理视频编码
     * @param tpCaptureImage 图像捕获助手，用于处理图像捕获
     */
    public void initStreaming(TpVideoEncoder tpVideoEncoder,
                              TpCaptureImage tpCaptureImage)
    {
        initStreaming(tpVideoEncoder, tpCaptureImage, StreamType.CAMERA);
    }

    /**
     * 早期初始化RTSPManager（在onCreate中调用）
     */
    private void initRtspManager()
    {
        if (activity == null)
        {
            throw new IllegalStateException("Activity is null, cannot initialize TpRtspManager");
        }

        Log.d(TAG, "Early initialization of TpRtspManager...");

        tpRtspManager = TpRtspManager.getInstance().initialize(activity);

        Log.d(TAG, "TpRtspManager initialized successfully in onCreate");
    }

    /**
     * 完成RTSP配置（在第二步初始化中调用）
     */
    private void configureRtspCallbacks()
    {
        if (tpRtspManager == null)
        {
            throw new IllegalStateException("TpRtspManager not initialized, must call initCore() first");
        }
        
        Log.d(TAG, "Setting up RTSP callbacks...");
        
        tpRtspManager.onStreamStarted(url ->
            {
                if (heartbeatListener != null) 
                {
                    mainHandler.post(() -> heartbeatListener.onStreamStatusChanged(true, url));
                }
                Log.d(TAG, "RTSP stream started: " + url);
            })
            .onStreamStopped(() -> 
            {
                if (heartbeatListener != null) 
                {
                    mainHandler.post(() -> heartbeatListener.onStreamStatusChanged(false, null));
                }
                Log.d(TAG, "RTSP stream stopped");
            })
            .onStreamError(errorMessage -> 
            {
                if (heartbeatListener != null) 
                {
                    mainHandler.post(() -> heartbeatListener.onStreamError(errorMessage));
                }
                Log.e(TAG, "RTSP stream error: " + errorMessage);
            })
            .onPermissionGranted(message -> 
            {
                Log.d(TAG, "RTSP permission granted: " + message);
                /* 如果当前是屏幕流类型，且服务已启动，权限获取后开始推流 */
                if (currentStreamType == StreamType.SCREEN && servicesStarted.get()) 
                {
                    /* 权限获取成功后，延迟一小段时间再开始推流 */
                    mainHandler.postDelayed(this::startRtspService, 500);
                }
            });
    }

    /**
     * 启动流媒体客户端进程
     * 
     * @param netInterface 网络接口名称，例如"wlan0"
     * @return 是否成功启动
     */
    public boolean startTpctrlConsole(String netInterface) 
    {
        try 
        {
            /* 保存选择的网络接口 */
            this.selectedNetInterface = netInterface;
            
            /* 确保先杀死任何可能存在的tpctrl进程 */
            killTpctrlProcess();
            
            /* 构建命令 */
            String command = "/data/touptek/tpctrl/tpctrl --console --ap --netname " + netInterface;
            
            /* 使用ProcessBuilder替代Runtime.exec，可以更好地控制进程 */
            ProcessBuilder processBuilder = new ProcessBuilder();
            processBuilder.command("sh", "-c", command);
            
            /* 设置错误输出合并到标准输出 */
            processBuilder.redirectErrorStream(true);
            
            /* 启动进程 */
            Process process = processBuilder.start();
            
            /* 创建一个线程来读取进程的输出，避免缓冲区填满导致进程阻塞 */
            new Thread(() -> {
                try {
                    java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(process.getInputStream()));
                    String line;
                    while ((line = reader.readLine()) != null) {
                        Log.d(TAG, "tpctrl output: " + line);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "读取tpctrl输出失败: " + e.getMessage());
                }
            }).start();
            
            /* 记录进程PID，便于后续管理 */
            try {
                // 在某些Android版本上可以直接获取PID
                int pid = -1;
                
                // 尝试使用反射获取PID (适用于较新的Android版本)
                try {
                    java.lang.reflect.Field f = process.getClass().getDeclaredField("pid");
                    f.setAccessible(true);
                    pid = f.getInt(process);
                } catch (Exception e) {
                    Log.d(TAG, "无法通过反射获取PID: " + e.getMessage());
                }
                
                if (pid > 0) {
                    // 存储进程信息
                    runningTpctrlProcesses.put(pid, process);
                    Log.d(TAG, "Started tpctrl console with PID: " + pid + ", network interface: " + netInterface);
                } else {
                    Log.d(TAG, "Started tpctrl console with network interface: " + netInterface + " (PID unknown)");
                    
                    // 即使无法获取PID，也尝试在1秒后获取进程列表中的tpctrl进程
                    new Handler(Looper.getMainLooper()).postDelayed(() -> {
                        try {
                            Process findProcess = Runtime.getRuntime().exec("ps -ef | grep tpctrl | grep -v grep");
                            java.io.BufferedReader reader = new java.io.BufferedReader(
                                    new java.io.InputStreamReader(findProcess.getInputStream()));
                            String line;
                            while ((line = reader.readLine()) != null) {
                                Log.d(TAG, "延迟检测到tpctrl进程: " + line);
                                // 尝试解析PID
                                String[] parts = line.trim().split("\\s+");
                                if (parts.length >= 2) {
                                    try {
                                        int detectedPid = Integer.parseInt(parts[1]);
                                        // 保存这个进程信息，虽然不是直接的Process对象
                                        runningTpctrlProcesses.put(detectedPid, null);
                                        Log.d(TAG, "延迟记录tpctrl PID: " + detectedPid);
                                    } catch (NumberFormatException e) {
                                        Log.e(TAG, "解析延迟检测的PID失败: " + e.getMessage());
                                    }
                                }
                            }
                            reader.close();
                            findProcess.waitFor();
                        } catch (Exception e) {
                            Log.e(TAG, "延迟检测tpctrl进程失败: " + e.getMessage());
                        }
                    }, 1000);
                }
            } catch (Exception e) {
                Log.e(TAG, "获取进程PID失败: " + e.getMessage());
            }
            
            return true;
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error starting tpctrl: " + e.getMessage());
            return false;
        }
    }

   /**
     * 更新视频编码器并重置RTSP会话
     * 
     * 当视频编码器实例发生变化时(例如HDMI断开后重连)，
     * 使用此方法更新RTSPManager中的视频编码器引用并重置RTSP会话。
     * 
     * @param newEncoder 新的视频编码器实例
     */
    public void updateVideoEncoder(TpVideoEncoder newEncoder) {
        if (newEncoder == null) {
            Log.e(TAG, "Cannot update with null TpVideoEncoder");
            return;
        }
        
        this.tpVideoEncoder = newEncoder;
        
        // 更新RTSPManager中的VideoEncoder引用
        if (tpRtspManager != null) {
            tpRtspManager.setVideoEncoder(newEncoder);
            Log.d(TAG, "Updated TpVideoEncoder in TpRtspManager");
            
            // 请求一个初始关键帧
            newEncoder.requestKeyFrame();
            
            // 检查RTSP是否正在运行，且当前是摄像头模式(CAMERA)
            // 屏幕推流模式下不需要重置RTSP会话
            if (isStreaming() && servicesStarted.get() && currentStreamType == StreamType.CAMERA) {
                Log.d(TAG, "HDMI重连后执行RTSP会话完全重置...");
                
                // 保存当前流类型
                final StreamType currentType = getCurrentStreamType();
                
                // 完全停止当前流
                tpRtspManager.stopStreaming();
                
                // 延迟后使用相同类型重新启动流
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    if (isRunning.get() && servicesStarted.get()) {
                        // 设置新的流类型（与当前相同，只是为了触发重置）
                        TpRtspManager.StreamType rtspStreamType = currentType == StreamType.CAMERA ?
                            TpRtspManager.StreamType.CAMERA : TpRtspManager.StreamType.SCREEN;
                        tpRtspManager.setStreamType(rtspStreamType);
                        
                        // 重新启动流
                        tpRtspManager.startStreaming();
                        Log.d(TAG, "HDMI重连后RTSP会话已完全重置");
                        
                        // 再次请求关键帧确保正常启动
                        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                            newEncoder.requestKeyFrame();
                            Log.d(TAG, "RTSP重置后发送额外关键帧");
                        }, 300);
                    }
                }, 500);
            } else if (currentStreamType == StreamType.SCREEN) {
                Log.d(TAG, "当前为屏幕推流模式，HDMI重连不影响推流，跳过RTSP会话重置");
                
                // 对于屏幕模式，只需更新编码器引用，不需要重置RTSP会话
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    newEncoder.requestKeyFrame();
                    Log.d(TAG, "屏幕推流模式下请求关键帧");
                }, 500);
            } else {
                // 如果RTSP未运行，只需请求一个关键帧即可
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    newEncoder.requestKeyFrame();
                    Log.d(TAG, "Requested key frame after HDMI reconnection");
                }, 500);
            }
        }
    }

    /**
     * 设置视频编码器到RTSP管理器
     */
    private void configureVideoEncoder()
    {
        if (tpRtspManager != null && tpVideoEncoder != null)
        {
            tpRtspManager.setVideoEncoder(tpVideoEncoder);
            Log.d(TAG, "TpVideoEncoder set to TpRtspManager");
        }
    }

    /**
     * 启动心跳检测服务
     * 
     * <p><strong>重要：必须在initStreaming()之后调用！</strong></p>
     */
    public void startService()
    {
        if (!fullyInitialized.get()) 
        {
            throw new IllegalStateException("Service not fully initialized. Call initStreaming() first.");
        }
        
        if (isRunning.get()) 
        {
            Log.w(TAG, "Service is already running");
            return;
        }
        
        Log.d(TAG, "Starting tpctrl heartbeat service");
        isRunning.set(true);
        servicesStarted.set(false);
        tpctrlExists.set(false);
        lastHeartbeatTime.set(0);
        
        executorService = Executors.newSingleThreadExecutor();
        socketExecutor = Executors.newCachedThreadPool();
        
        /* 启动Socket服务器 */
        startSocketServer();
        
        /* 启动心跳检测线程 */
        executorService.submit(this::monitorHeartbeat);
        
        Log.d(TAG, "TpStreamingService started successfully");
    }

    /**
     * 设置流类型
     * <p>
     * 更改当前的视频流类型，影响视频编码和传输方式。
     * </p>
     * 
     * @param streamType 要设置的流类型，参见 {@link StreamType}
     */
    public void setStreamType(StreamType streamType) 
    {
        if (currentStreamType == streamType) 
        {
            return;
        }
        
        Log.d(TAG, "Switching stream type from " + currentStreamType + " to " + streamType);
        currentStreamType = streamType;
        
        /* 如果服务已启动，重新配置RTSP流类型 */
        if (servicesStarted.get() && tpRtspManager != null)
        {
            /* 停止当前推流 */
            if (tpRtspManager.isStreaming())
            {
                tpRtspManager.stopStreaming();
            }
            
            /* 设置新的流类型 */
            TpRtspManager.StreamType rtspStreamType = streamType == StreamType.CAMERA ?
                TpRtspManager.StreamType.CAMERA : TpRtspManager.StreamType.SCREEN;
            tpRtspManager.setStreamType(rtspStreamType);
            
            if (streamType == StreamType.SCREEN) 
            {
                /* 对于屏幕流，先请求权限 */
                Log.d(TAG, "Requesting screen capture permission");
                tpRtspManager.requestScreenPermission();
                /* 权限回调中会处理启动推流 */
            } 
            else 
            {
                /* 摄像头流直接启动 */
                startRtspService();
            }
        }
    }

    
    /**
     * 获取当前推流类型
     * 
     * @return 当前设置的流类型
     */
    public StreamType getCurrentStreamType() 
    {
        return currentStreamType;
    }
    
    /**
     * 手动切换流类型
     */
    public void switchStreamType() 
    {
        StreamType newType = currentStreamType == StreamType.CAMERA ? 
            StreamType.SCREEN : StreamType.CAMERA;
        setStreamType(newType);
    }
    
    /**
     * 停止心跳检测服务
     */
    public void stopService()
    {
        if (!isRunning.get()) 
        {
            return;
        }
        
        Log.d(TAG, "Stopping tpctrl heartbeat service");
        isRunning.set(false);
        
        /* 停止相关服务 */
        stopSubServices();
        
        /* 停止Socket服务器 */
        stopSocketServer();
        
        if (executorService != null) 
        {
            executorService.shutdown();
            executorService = null;
        }
        
        if (socketExecutor != null) 
        {
            socketExecutor.shutdown();
            socketExecutor = null;
        }
    }
    
    /**
     * 启动Socket服务器
     */
    private void startSocketServer() 
    {
        if (socketServerRunning.get()) 
        {
            return;
        }
        
        socketServerRunning.set(true);
        socketServerThread = new Thread(this::processSocketConnections);
        socketServerThread.start();
        Log.d(TAG, "Heart-beat Socket server started on port: " + socketPort);
    }
    
    /**
     * 停止Socket服务器
     */
    private void stopSocketServer() 
    {
        if (!socketServerRunning.get()) 
        {
            return;
        }
        
        socketServerRunning.set(false);
        
        if (serverSocket != null) 
        {
            try 
            {
                serverSocket.close();
                Log.d(TAG, "Socket server closed");
            } 
            catch (IOException e) 
            {
                Log.e(TAG, "Error closing socket server: " + e.getMessage());
            }
            serverSocket = null;
        }
        
        if (socketServerThread != null) 
        {
            socketServerThread.interrupt();
            socketServerThread = null;
        }
    }
    
    /**
     * Socket服务器主循环
     */
    private void processSocketConnections()
    {
        try 
        {
            serverSocket = new ServerSocket(socketPort);
            Log.d(TAG, "Socket server listening on port: " + socketPort);
            
            while (socketServerRunning.get() && !Thread.currentThread().isInterrupted()) 
            {
                try 
                {
                    Socket clientSocket = serverSocket.accept();
                    
                    /* 在新线程中处理客户端 */
                    socketExecutor.execute(() -> handleSocketClient(clientSocket));
                } 
                catch (IOException e) 
                {
                    if (socketServerRunning.get()) 
                    {
                        Log.e(TAG, "Error accepting client connection: " + e.getMessage());
                    }
                }
            }
        } 
        catch (IOException e) 
        {
            Log.e(TAG, "Error creating socket server: " + e.getMessage());
        } 
        finally 
        {
            if (serverSocket != null && !serverSocket.isClosed()) 
            {
                try 
                {
                    serverSocket.close();
                } 
                catch (IOException e) 
                {
                    Log.e(TAG, "Error closing server socket: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 处理Socket客户端连接
     */
    private void handleSocketClient(Socket clientSocket) 
    {
        try 
        {
            clientSocket.setSoTimeout(10000); // 10秒超时
            
            InputStream inputStream = clientSocket.getInputStream();
            
            /* 读取命令 */
            byte[] cmdBuffer = new byte[2];
            int bytesRead = inputStream.read(cmdBuffer);
            
            if (bytesRead != 2) 
            {
                Log.e(TAG, "Invalid command length: " + bytesRead);
                return;
            }
            
            int cmd = cmdBuffer[0];
            
            if (cmd == CMD_HEARTBEAT) 
            {
                /* 处理心跳包 */
                onHeartbeatReceived();
            }
            
        } 
        catch (IOException e) 
        {
            Log.e(TAG, "Error handling socket client: " + e.getMessage());
        } 
        finally 
        {
            try 
            {
                clientSocket.close();
            } 
            catch (IOException e) 
            {
                Log.e(TAG, "Error closing client socket: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理心跳包
     */
    private void onHeartbeatReceived()
    {
        lastHeartbeatTime.set(System.currentTimeMillis());
        
        /* 如果之前tpctrl不存在，现在检测到了心跳包，说明tpctrl出现了 */
        if (!tpctrlExists.get()) 
        {
            tpctrlExists.set(true);
            onTpctrlDetected();
            
            /* 立即发送所有ISP参数配置到tpctrl端 */
            sendAllIspConfigs();
        }
    }
    
    /**
     * 心跳检测循环
     */
    private void monitorHeartbeat()
    {
        while (isRunning.get()) 
        {
            try 
            {
                long currentTime = System.currentTimeMillis();
                long lastHeartbeat = lastHeartbeatTime.get();
                boolean currentTpctrlExists = false;
                
                if (lastHeartbeat > 0 && (currentTime - lastHeartbeat) <= heartbeatTimeout) 
                {
                    /* 心跳包在超时时间内，认为tpctrl存在 */
                    currentTpctrlExists = true;
                }
                
                boolean previousTpctrlExists = tpctrlExists.get();
                
                /* 更新tpctrl状态 */
                tpctrlExists.set(currentTpctrlExists);
                
                if (!currentTpctrlExists && previousTpctrlExists) 
                {
                    /* tpctrl进程消失了（心跳包超时） */
                    onTpctrlLost();
                } 
                else if (currentTpctrlExists && !servicesStarted.get()) 
                {
                    /* tpctrl进程存在，确保服务已启动 */
                    startSubServices();
                }
                
                Thread.sleep(1000); // 1秒检查一次
            } 
            catch (InterruptedException e) 
            {
                Thread.currentThread().interrupt();
                Log.d(TAG, "Heartbeat check loop interrupted");
                break;
            } 
            catch (Exception e) 
            {
                Log.e(TAG, "Error in heartbeat check loop", e);
                try 
                {
                    Thread.sleep(3000);
                } 
                catch (InterruptedException ie) 
                {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        Log.d(TAG, "Heartbeat check loop ended");
    }
    
    /**
     * 处理检测到tpctrl进程
     */
    private void onTpctrlDetected()
    {
        Log.d(TAG, "Tpctrl process detected via heartbeat");
        
        // 如果是手动控制模式，只启动基本服务，不启动RTSP流
        if (manualRtspControl.get()) {
            Log.d(TAG, "Manual RTSP control mode is enabled, waiting for user to start RTSP");
            // 仅启动StreamingSocketService，不启动RTSP
            startImageSocketService();
        } else {
            // 自动模式下启动所有服务
            startSubServices();
        }
    }
    
    /**
     * 只启动StreamingSocketService，不启动RTSP
     */
    private void startImageSocketService() {
        if (servicesStarted.get()) {
            return;
        }
        
        try {
            /* 启动图像Socket服务 - 专门处理图像传输 */
            if (tpCaptureImage != null) {
                String tempImagePath = TpFileManager.createImagePath(context);
                streamingSocketService = new StreamingSocketService(tpCaptureImage, tempImagePath);
                streamingSocketService.setLogListener(message ->
                    Log.d(TAG, "StreamingSocketService: " + message));
                streamingSocketService.start();
                Log.d(TAG, "StreamingSocket service started (RTSP not started due to manual control mode)");
            }
            
            servicesStarted.set(true);
        } catch (Exception e) {
            Log.e(TAG, "Error starting StreamingSocketService", e);
        }
    }
    
    /**
     * 处理tpctrl进程消失
     */
    private void onTpctrlLost()
    {
        Log.d(TAG, "Tpctrl process lost (heartbeat timeout), stopping services");
        
        stopSubServices();
    }
    
    /**
     * 启动相关服务
     */
    private void startSubServices()
    {
        if (servicesStarted.get()) 
        {
            return;
        }
        
        Log.d(TAG, "Starting RTSP and TpctrlSocket services");
        
        try 
        {
            /* 启动图像Socket服务 - 专门处理图像传输 */
            if (tpCaptureImage != null)
            {
                String tempImagePath = TpFileManager.createImagePath(context);
                streamingSocketService = new StreamingSocketService(tpCaptureImage, tempImagePath);
                streamingSocketService.setLogListener(message ->
                    Log.d(TAG, "StreamingSocketService: " + message));
                streamingSocketService.start();
                Log.d(TAG, "StreamingSocket service started");
            }
            
            /* 检查并启动RTSP服务 - 只在非手动控制模式下自动启动 */
            if (!manualRtspControl.get() && tpRtspManager != null)
            {
                // 如果有用户指定的网络接口，则使用它
                if (selectedNetInterface != null) {
                    tpRtspManager.setNetworkInterface(selectedNetInterface);
                }
                
                if (currentStreamType == StreamType.CAMERA && tpVideoEncoder != null)
                {
                    /* 摄像头流需要视频编码器 */
                    if (isVideoEncoderReady()) 
                    {
                        startRtspService();
                    } 
                    else 
                    {
                        Log.d(TAG, "TpVideoEncoder not ready, delaying RTSP start");
                        mainHandler.postDelayed(this::startRtspServiceWithRetry, 2000);
                    }
                } 
                else if (currentStreamType == StreamType.SCREEN) 
                {
                    /* 屏幕流需要先请求权限，在权限回调中会自动开始推流 */
                    if (tpRtspManager.hasScreenCapturePermission())
                    {
                        /* 如果已经有权限，直接启动 */
                        startRtspService();
                    } 
                    else 
                    {
                        /* 请求权限，onPermissionGranted回调中会启动推流 */
                        tpRtspManager.requestScreenPermission();
                    }
                }
            }
            else {
                Log.d(TAG, "RTSP not started automatically due to manual control mode");
            }
            
            servicesStarted.set(true);
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error starting services", e);
        }
    }
    
    /**
     * 启动RTSP服务
     */
    private void startRtspService() 
    {
        if (tpRtspManager == null)
        {
            Log.e(TAG, "TpRtspManager is null");
            return;
        }
        
        try 
        {
            // 如果有用户指定的网络接口，则使用它
            if (selectedNetInterface != null) {
                tpRtspManager.setNetworkInterface(selectedNetInterface);
                Log.d(TAG, "Using selected network interface for RTSP: " + selectedNetInterface);
            }
            
            /* 如果是摄像头流，确保设置了视频编码器 */
            if (currentStreamType == StreamType.CAMERA && tpVideoEncoder != null)
            {
                tpRtspManager.setVideoEncoder(tpVideoEncoder);
            }
            else if (currentStreamType == StreamType.SCREEN) 
            {
                /* 屏幕流需要检查权限 */
                if (!tpRtspManager.hasScreenCapturePermission())
                {
                    Log.w(TAG, "Screen capture permission not granted yet");
                    /* 如果权限未获取，主动请求权限 */
                    tpRtspManager.requestScreenPermission();
                    return; /* 先不启动推流，等权限回调 */
                }
            }
            
            /* 开始推流 */
            if (tpRtspManager.startStreaming())
            {
                Log.d(TAG, "RTSP streaming started successfully with type: " + currentStreamType);
            } 
            else 
            {
                Log.w(TAG, "Failed to start RTSP streaming");
            }
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error starting RTSP service", e);
        }
    }
    
    /**
     * 停止相关服务
     */
    private void stopSubServices()
    {
        if (!servicesStarted.get()) 
        {
            return;
        }
        
        Log.d(TAG, "Stopping RTSP and TpctrlSocket services");
        
        try 
        {
            /* 停止RTSP服务 */
            if (tpRtspManager != null && tpRtspManager.isStreaming())
            {
                tpRtspManager.stopStreaming();
                Log.d(TAG, "RTSP streaming stopped");
            }
            
            /* 停止ImageSocket服务 */
            if (streamingSocketService != null)
            {
                streamingSocketService.stop();
                streamingSocketService = null;
                Log.d(TAG, "TpctrlSocket service stopped");
            }
            
            servicesStarted.set(false);
        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Error stopping services", e);
        }
    }
    
    /**
     * 检查视频编码器是否准备好
     */
    private boolean isVideoEncoderReady() 
    {
        return tpVideoEncoder != null;
    }
    
    /**
     * 重试启动RTSP服务
     */
    private void startRtspServiceWithRetry()
    {
        if (!servicesStarted.get()) 
        {
            return; /* 如果服务已经被停止，不再重试 */
        }
        
        if (currentStreamType == StreamType.CAMERA && !isVideoEncoderReady()) 
        {
            /* 继续重试，最多重试3次 */
            retryCount++;
            if (retryCount < 3) 
            {
                Log.d(TAG, "TpVideoEncoder still not ready, retry " + retryCount);
                mainHandler.postDelayed(this::startRtspServiceWithRetry, 3000);
            } 
            else 
            {
                Log.w(TAG, "TpVideoEncoder not ready after 3 retries, giving up RTSP start");
                retryCount = 0;
            }
        } 
        else 
        {
            startRtspService();
            retryCount = 0;
        }
    }
    
    /**
     * 检查服务是否正在运行
     * 
     * @return 如果服务正在运行则返回true，否则返回false
     */
    public boolean isRunning()
    {
        return isRunning.get();
    }

    /**
     * 判断服务是否启动
     * @return ture已启动推流服务  false未启动推流服务
     */
    public boolean isServicesRunning()
    {
        return servicesStarted.get();
    }

    /**
     * 判断tpctrl是否已经开始运行（通过心跳包的方式）
     * @return ture已经接收到心跳包服务已经启动  false未接收到心跳包服务未启动
     */
    public boolean isTpctrlRunning()
    {
        return tpctrlExists.get();
    }

    /**
     * 是否已经开始RTSP的推流
     * @return true RTSP正在推流 false RTSP未开始推流
     */
    public boolean isStreaming()
    {
        return tpRtspManager != null && tpRtspManager.isStreaming();
    }

    /**
     * 获取RTSP推流的Uri地址
     * @return 返回string类型的Uri 例如：192.168.1.1
     */
    public String getStreamUrl()
    {
        return tpRtspManager != null ? tpRtspManager.getStreamUrl() : null;
    }

    
    /**
     * 释放资源
     */
    public void release() 
    {
        stopService();
        if (tpRtspManager != null)
        {
            tpRtspManager.release();
            tpRtspManager = null;
        }
        earlyInitialized.set(false);
        fullyInitialized.set(false);
    }

    /**
     * 发送所有ISP参数配置信息到tpctrl端
     */
    public void sendAllIspConfigs()
    {
        Log.i(TAG, "Start sending all ISP parameter configuration information to the tpctrl port....");

        int sentCount = 0;

        for (TpIspParam param : TpIspParam.values())
        {
            try 
            {
                /* 跳过版本号参数，因为它是只读的 */
                if (param == TpIspParam.TOUPTEK_PARAM_VERSION) {
                    continue;
                }
                /* 获取参数的完整配置信息 */
                TpIspParam.ParamData paramData = TpIspParam.getParamInfo(param);

                /* 发送参数配置到tpctrl端 */
                if (sendIspConfig(param, paramData))
                {
                    sentCount++;
                    Log.d(TAG, String.format("Parameter config sent to tpctrl: %s (ID: %d) - isDIsabel=%d, min=%d, max=%d, default=%d, current=%d",
                            param.name(), param.getParamId(), paramData.isDisabled?1:0, paramData.min, paramData.max, paramData.defaultValue, paramData.current));
                } 
                else 
                {
                    Log.e(TAG, "Failed to send parameter config: " + param.name());
                }

                /* 添加小延时避免过于频繁的发送 */
                Thread.sleep(10);

            } 
            catch (Exception e) 
            {
                Log.e(TAG, "Error sending parameter config " + param.name() + ": " + e.getMessage());
            }
        }

        Log.i(TAG, "ISP parameter configuration sending completed, sent " + sentCount + " parameter configs");
    }

    /**
     * 发送单个ISP参数配置信息到tpctrl端
     */
    private boolean sendIspConfig(TpIspParam param, TpIspParam.ParamData paramData)
    {
        Socket socket = null;
        try 
        {
            /* 创建socket连接到tpctrl端的ISP参数专用端口 */
            socket = new Socket();
            socket.setSoTimeout(3000); // 3秒超时
            socket.connect(new java.net.InetSocketAddress("127.0.0.1", ispPort), 3000);

            /* 获取输出流 */
            OutputStream outputStream = socket.getOutputStream();

            /* 构造数据包：[CMD_ISP_PARAM_CONFIG(1字节)] + [PARAM_ID(1字节)] +[isDisable(4个字节)]+ [MIN(4字节)] + [MAX(4字节)] + [DEFAULT(4字节)] + [CURRENT(4字节)] */
            byte[] packet = new byte[22]; // 1 + 1 + 4*5 = 22字节

            int offset = 0;

            /* 命令码 */
            packet[offset++] = (byte)CMD_ISP_PARAM_CONFIG;

            /* 参数ID */
            packet[offset++] = (byte)param.getParamId();

            /* 写入是否禁用（网络字节序） */
            writeIntToByteArray(packet, offset, paramData.isDisabled ? 1 : 0);
            offset += 4;

            /* 写入最小值（网络字节序） */
            writeIntToByteArray(packet, offset, paramData.min);
            offset += 4;

            /* 写入最大值（网络字节序） */
            writeIntToByteArray(packet, offset, paramData.max);
            offset += 4;

            /* 写入默认值（网络字节序） */
            writeIntToByteArray(packet, offset, paramData.defaultValue);
            offset += 4;

            /* 写入当前值（网络字节序） */
            writeIntToByteArray(packet, offset, paramData.current);

            /* 发送数据 */
            outputStream.write(packet);
            outputStream.flush();

            return true;

        } 
        catch (Exception e) 
        {
            Log.e(TAG, "Failed to send ISP parameter configuration to tpctrl: " + param.name() + ", error: " + e.getMessage());
            return false;
        } 
        finally 
        {
            if (socket != null) 
            {
                try 
                {
                    socket.close();
                } 
                catch (Exception e) 
                {
                    /* 忽略关闭时的错误 */
                }
            }
        }
    }

    /**
     * 将整数值写入字节数组（网络字节序/大端序）
     */
    private void writeIntToByteArray(byte[] bytes, int offset, int value)
    {
        bytes[offset] = (byte)((value >> 24) & 0xFF);
        bytes[offset + 1] = (byte)((value >> 16) & 0xFF);
        bytes[offset + 2] = (byte)((value >> 8) & 0xFF);
        bytes[offset + 3] = (byte)(value & 0xFF);
    }

    /**
     * 杀死tpctrl进程
     * 
     * @return 是否成功杀死进程
     */
    private boolean killTpctrlProcess() {
        try {
            Log.d(TAG, "尝试杀死tpctrl进程");

            // 1. 清理已跟踪的进程
            if (!runningTpctrlProcesses.isEmpty()) {
                for (Integer pid : new ArrayList<>(runningTpctrlProcesses.keySet())) {
                    Process process = runningTpctrlProcesses.get(pid);
                    try {
                        // 对有Process引用的进程使用Java API终止
                        if (process != null) {
                            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                                process.destroyForcibly();
                            } else {
                                process.destroy();
                            }
                        }

                        // 无论是否有Process引用，都使用kill命令确保终止
                        Runtime.getRuntime().exec("kill -9 " + pid);
                    } catch (Exception e) {
                        Log.e(TAG, "杀死PID " + pid + " 失败: " + e.getMessage());
                    }
                }
                runningTpctrlProcesses.clear();
            }

            // 2. 使用单一命令查找并杀死所有tpctrl进程
            Runtime.getRuntime().exec("pkill -9 tpctrl").waitFor(300, java.util.concurrent.TimeUnit.MILLISECONDS);

            // 3. 重置状态
            tpctrlExists.set(false);
            if (servicesStarted.get()) {
                stopSubServices();
            }

            return true;
        } catch (Exception e) {
            Log.e(TAG, "杀死tpctrl进程失败: " + e.getMessage());
            return false;
        }
    }
    

    /**
     * 手动启动RTSP推流服务
     * 
     * @param streamType 流类型（摄像头或屏幕）
     * @param netInterface 网络接口（如wlan0, eth0）
     * @return 是否成功启动
     */
    public boolean startRtspManually(StreamType streamType, String netInterface) {
        Log.d(TAG, "手动启动RTSP流，类型: " + streamType + ", 网络接口: " + netInterface);
        
        if (!fullyInitialized.get()) {
            Log.e(TAG, "Cannot start RTSP manually: service not fully initialized");
            return false;
        }
        
        // 确保先停止任何正在运行的RTSP流
        if (tpRtspManager != null && tpRtspManager.isStreaming()) {
            tpRtspManager.stopStreaming();
            Log.d(TAG, "停止当前正在运行的RTSP流");
        }
        
        // 强制杀死所有tpctrl进程
        Log.d(TAG, "强制杀死所有tpctrl进程后重新启动");
        boolean killSuccess = killTpctrlProcess();
        if (!killSuccess) {
            Log.w(TAG, "无法杀死所有tpctrl进程，继续尝试重新启动");
        }
        
        // 启动tpctrl控制台进程
        boolean startSuccess = startTpctrlConsole(netInterface);
        if (!startSuccess) {
            Log.e(TAG, "启动tpctrl控制台失败");
            return false;
        }

        Log.d(TAG, "Manually starting RTSP streaming with type: " + streamType + 
              " on network interface: " + netInterface);
        
        // 保存选择的网络接口
        this.selectedNetInterface = netInterface;
        
        // 设置流类型
        this.currentStreamType = streamType;
        
        // 等待tpctrl进程启动（通过心跳包检测到）
        long startTime = System.currentTimeMillis();
        boolean tpctrlStarted = false;
        while (!tpctrlStarted) {
            try {
                Thread.sleep(100);
                // 检查tpctrl是否被检测到
                tpctrlStarted = tpctrlExists.get();
                // 最多等待5秒
                if (System.currentTimeMillis() - startTime > 5000) {
                    Log.e(TAG, "等待tpctrl进程启动超时");
                    return false;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        Log.d(TAG, "tpctrl进程已检测到，继续启动RTSP");
        
        // 确保TpctrlSocketService已启动
        if (streamingSocketService == null && tpCaptureImage != null) {
            String tempImagePath = TpFileManager.createImagePath(context);
            streamingSocketService = new StreamingSocketService(tpCaptureImage, tempImagePath);
            streamingSocketService.setLogListener(message ->
                Log.d(TAG, "StreamingSocketService: " + message));
            streamingSocketService.start();
            Log.d(TAG, "TpctrlSocket service started for manual RTSP streaming");
        }
        
        // 配置并启动RTSP流
        if (tpRtspManager != null) {
            // 设置RTSPManager的网络接口
            tpRtspManager.setNetworkInterface(netInterface);
            
            // 设置流类型
            TpRtspManager.StreamType rtspStreamType = streamType == StreamType.CAMERA ?
                TpRtspManager.StreamType.CAMERA : TpRtspManager.StreamType.SCREEN;
            tpRtspManager.setStreamType(rtspStreamType);
            
            // 根据流类型启动RTSP
            if (streamType == StreamType.CAMERA) {
                if (tpVideoEncoder != null) {
                    tpRtspManager.setVideoEncoder(tpVideoEncoder);
                    if (tpRtspManager.startStreaming()) {
                        Log.d(TAG, "Manual RTSP camera streaming started successfully");
                        return true;
                    } else {
                        Log.e(TAG, "Failed to start manual RTSP camera streaming");
                        return false;
                    }
                } else {
                    Log.e(TAG, "Cannot start camera streaming: TpVideoEncoder is null");
                    return false;
                }
            } else { // SCREEN
                if (!tpRtspManager.hasScreenCapturePermission()) {
                    Log.d(TAG, "Requesting screen capture permission for manual streaming");
                    tpRtspManager.requestScreenPermission();
                    // 返回true表示已请求权限，实际推流会在权限回调中进行
                    return true;
                } else {
                    if (tpRtspManager.startStreaming()) {
                        Log.d(TAG, "Manual RTSP screen streaming started successfully");
                        return true;
                    } else {
                        Log.e(TAG, "Failed to start manual RTSP screen streaming");
                        return false;
                    }
                }
            }
        } else {
            Log.e(TAG, "Cannot start RTSP manually: TpRtspManager is null");
            return false;
        }
    }

    /**
     * 手动停止RTSP推流
     * 
     * @return 是否成功停止
     */
    public boolean stopRtspManually() {
        Log.d(TAG, "手动停止RTSP推流");
        
        // 创建一个后台线程来处理实际的停止操作
        new Thread(() -> {
            try {
                // 首先停止RTSP流
                if (tpRtspManager != null && tpRtspManager.isStreaming()) {
                    tpRtspManager.stopStreaming();
                    Log.d(TAG, "Manual RTSP streaming stopped");
                } else {
                    Log.d(TAG, "No RTSP streaming to stop or TpRtspManager is null");
                }
                
                // 重置内部服务状态
                servicesStarted.set(false);
                
                // 使用新的方法强制杀死tpctrl进程
                boolean processKilled = killTpctrlProcess();
                
                if (processKilled) {
                    Log.d(TAG, "成功强制终止所有tpctrl进程");
                } else {
                    Log.w(TAG, "无法终止所有tpctrl进程");
                }
                
                // 停止TpctrlSocketService
                if (streamingSocketService != null) {
                    streamingSocketService.stop();
                    streamingSocketService = null;
                    Log.d(TAG, "StreamingSocketService stopped");
                }
                
                // 确保tpctrl状态被重置
                tpctrlExists.set(false);
                
            } catch (Exception e) {
                Log.e(TAG, "停止RTSP推流过程中出错: " + e.getMessage(), e);
            }
        }, "StopRtspThread").start();
        
        // 立即返回true，因为UI已经更新，后台操作会继续进行
        return true;
    }

    /**
     * 设置RTSP推流的手动控制模式
     * 
     * @param manualControl true表示手动控制，false表示自动控制
     */
    public void setManualRtspControl(boolean manualControl) {
        this.manualRtspControl.set(manualControl);
        Log.d(TAG, "RTSP manual control mode set to: " + manualControl);
    }

    /**
     * 获取当前是否为手动控制RTSP推流模式
     * 
     * @return true表示手动控制，false表示自动控制
     */
    public boolean isManualRtspControl() {
        return manualRtspControl.get();
    }

    /**
     * 获取内部的RTSPManager实例
     * <p>
     * 此方法用于高级用例，允许直接访问和操作RTSP管理器。
     * 通常应避免直接使用此方法，优先使用StreamingService提供的API。
     * </p>
     * 
     * @return RTSPManager实例，如果未初始化可能返回null
     */
    public TpRtspManager getRtspManager() {
        return tpRtspManager;
    }
}
<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>类分层结构</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">所有程序包的分层结构</h1>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="com/touptek/ui/package-tree.html">com.touptek.ui</a>, </li>
<li><a href="com/touptek/utils/package-tree.html">com.touptek.utils</a>, </li>
<li><a href="com/touptek/video/package-tree.html">com.touptek.video</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpFileManager.html" class="type-name-link" title="com.touptek.utils中的类">TpFileManager</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpHdmiMonitor.html" class="type-name-link" title="com.touptek.utils中的类">TpHdmiMonitor</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpIspParam.ParamData.html" class="type-name-link" title="com.touptek.video中的类">TpIspParam.ParamData</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpIspParam.SceneInfo.html" class="type-name-link" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpNetworkMonitor.html" class="type-name-link" title="com.touptek.utils中的类">TpNetworkMonitor</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpNetworkMonitor.HotspotInfo.html" class="type-name-link" title="com.touptek.utils中的类">TpNetworkMonitor.HotspotInfo</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html" class="type-name-link" title="com.touptek.utils中的类">TpNetworkMonitor.NetworkInterfaceInfo</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpNetworkMonitor.WifiConnectionInfo.html" class="type-name-link" title="com.touptek.utils中的类">TpNetworkMonitor.WifiConnectionInfo</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpSambaClient.html" class="type-name-link" title="com.touptek.utils中的类">TpSambaClient</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpSambaClient.SMBConfig.html" class="type-name-link" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpVideoConfig.html" class="type-name-link" title="com.touptek.video中的类">TpVideoConfig</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpVideoConfig.Builder.html" class="type-name-link" title="com.touptek.video中的类">TpVideoConfig.Builder</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpVideoSystem.html" class="type-name-link" title="com.touptek.video中的类">TpVideoSystem</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpVideoSystem.TpVideoSystemAdapter.html" class="type-name-link" title="com.touptek.video中的类">TpVideoSystem.TpVideoSystemAdapter</a> (implements com.touptek.video.<a href="com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a>)</li>
<li class="circle">android.view.View (implements android.view.accessibility.AccessibilityEventSource, android.graphics.drawable.Drawable.Callback, android.view.KeyEvent.Callback)
<ul>
<li class="circle">android.widget.ImageView
<ul>
<li class="circle">androidx.appcompat.widget.AppCompatImageView (implements androidx.core.view.TintableBackgroundView, androidx.core.widget.TintableImageSourceView)
<ul>
<li class="circle">com.touptek.ui.<a href="com/touptek/ui/TpImageView.html" class="type-name-link" title="com.touptek.ui中的类">TpImageView</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">android.view.TextureView
<ul>
<li class="circle">com.touptek.ui.<a href="com/touptek/ui/TpTextureView.html" class="type-name-link" title="com.touptek.ui中的类">TpTextureView</a></li>
</ul>
</li>
<li class="circle">com.touptek.ui.<a href="com/touptek/ui/TpRoiView.html" class="type-name-link" title="com.touptek.ui中的类">TpRoiView</a> (implements com.touptek.video.<a href="com/touptek/video/TpIspParam.OnDataChangedListener.html" title="com.touptek.video中的接口">TpIspParam.OnDataChangedListener</a>)</li>
<li class="circle">android.view.ViewGroup (implements android.view.ViewManager, android.view.ViewParent)
<ul>
<li class="circle">android.widget.FrameLayout
<ul>
<li class="circle">com.touptek.ui.<a href="com/touptek/ui/TpVideoPlayerView.html" class="type-name-link" title="com.touptek.ui中的类">TpVideoPlayerView</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpFileManager.StorageListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpHdmiMonitor.HdmiListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpHdmiMonitor.HdmiListener</a></li>
<li class="circle">com.touptek.ui.<a href="com/touptek/ui/TpImageView.OnZoomChangeListener.html" class="type-name-link" title="com.touptek.ui中的接口">TpImageView.OnZoomChangeListener</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpIspParam.OnDataChangedListener.html" class="type-name-link" title="com.touptek.video中的接口">TpIspParam.OnDataChangedListener</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpIspParam.OnSerialStateChangedListener.html" class="type-name-link" title="com.touptek.video中的接口">TpIspParam.OnSerialStateChangedListener</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpNetworkMonitor.NetworkStateListener</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpSambaClient.DirectoryListListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a></li>
<li class="circle">com.touptek.utils.<a href="com/touptek/utils/TpSambaClient.UploadListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a></li>
<li class="circle">com.touptek.ui.<a href="com/touptek/ui/TpTextureView.OnZoomChangeListener.html" class="type-name-link" title="com.touptek.ui中的接口">TpTextureView.OnZoomChangeListener</a></li>
<li class="circle">com.touptek.ui.<a href="com/touptek/ui/TpTextureView.TouchEventHandler.html" class="type-name-link" title="com.touptek.ui中的接口">TpTextureView.TouchEventHandler</a></li>
<li class="circle">com.touptek.ui.<a href="com/touptek/ui/TpVideoPlayerView.VideoPlayerListener.html" class="type-name-link" title="com.touptek.ui中的接口">TpVideoPlayerView.VideoPlayerListener</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" class="type-name-link" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="java.lang中的类或接口">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Comparable.html" title="java.lang中的类或接口" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/constant/Constable.html" title="java.lang.constant中的类或接口" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a>)
<ul>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpIspParam.html" class="type-name-link" title="enum class in com.touptek.video">TpIspParam</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpVideoConfig.BitrateMode.html" class="type-name-link" title="enum class in com.touptek.video">TpVideoConfig.BitrateMode</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpVideoConfig.VideoCodec.html" class="type-name-link" title="enum class in com.touptek.video">TpVideoConfig.VideoCodec</a></li>
<li class="circle">com.touptek.video.<a href="com/touptek/video/TpVideoSystem.StreamType.html" class="type-name-link" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>

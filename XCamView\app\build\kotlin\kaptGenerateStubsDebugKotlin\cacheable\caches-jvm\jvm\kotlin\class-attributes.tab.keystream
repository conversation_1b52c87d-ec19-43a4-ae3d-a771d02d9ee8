
FolderAdapterFolderAdapter.ViewHolder#com.touptek.xcamview.util.FontUtils&com.touptek.xcamview.util.BaseActivity,com.touptek.xcamview.util.BaseDialogFragment&com.touptek.xcamview.util.BaseFragmentEcom.touptek.xcamview.activity.measurement.TpMeasurementDialogFragmentBcom.touptek.xcamview.activity.ispdialogfragment.TpAEDialogFragmentDcom.touptek.xcamview.activity.ispdialogfragment.TpFlipDialogFragmentBcom.touptek.xcamview.activity.ispdialogfragment.TpHzDialogFragmentMcom.touptek.xcamview.activity.ispdialogfragment.TpImageProcess2DialogFragmentLcom.touptek.xcamview.activity.ispdialogfragment.TpImageProcessDialogFragmentEcom.touptek.xcamview.activity.ispdialogfragment.TpSceneDialogFragmentBcom.touptek.xcamview.activity.ispdialogfragment.TpWBDialogFragment?com.touptek.xcamview.activity.settings.TpFormatSettingsFragmentIcom.touptek.xcamview.activity.settings.TpFormatSettingsFragment.CompanionDcom.touptek.xcamview.activity.settings.TpMeasurementSettingsFragment=<EMAIL>?com.touptek.xcamview.activity.settings.TpRecordSettingsFragment?<EMAIL>*com.touptek.xcamview.activity.StatusBanner*com.touptek.xcamview.activity.MainActivity&com.touptek.xcamview.activity.MainMenuDcom.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener>com.touptek.xcamview.activity.MainMenu.MenuPopupDialogFragment3com.touptek.xcamview.activity.MainMenu.ButtonAction1com.touptek.xcamview.activity.MainMenu.MenuAction-com.android.rockchip.camera2.view.OverlayView<com.touptek.xcamview.activity.browse.TpCopyDirDialogFragmentScom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListenerFcom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.Companion:com.touptek.xcamview.activity.browse.TpOperationDirAdapterEcom.touptek.xcamview.activity.browse.TpOperationDirAdapter.ViewHolder7com.touptek.xcamview.activity.browse.TpThumbGridAdapterBcom.touptek.xcamview.activity.browse.TpThumbGridAdapter.ViewHolder=<EMAIL><com.touptek.xcamview.activity.browse.TpVideoBrowse.Companion&com.touptek.measurerealize.TpImageView0com.touptek.xcamview.view.MeasurementOverlayView5com.touptek.xcamview.view.MeasurementOverlayView.Mode6com.touptek.xcamview.view.MeasurementOverlayView.Shape5com.touptek.xcamview.view.MeasurementOverlayView.Line7com.touptek.xcamview.view.MeasurementOverlayView.Circle<com.touptek.xcamview.activity.compare.TpImageCompareActivityFcom.touptek.xcamview.activity.compare.TpImageCompareActivity.CompanionAcom.touptek.xcamview.activity.compare.TpImageCompareMultiActivityJcom.touptek.xcamview.activity.compare.TpImageCompareMultiActivity.SyncModeKcom.touptek.xcamview.activity.compare.TpImageCompareMultiActivity.CompanionQcom.touptek.xcamview.activity.browse.videomanagement.TpVideoDecoderDialogFragment[com.touptek.xcamview.activity.browse.videomanagement.TpVideoDecoderDialogFragment.CompanionPcom.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragmentZcom.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment.CompanionVcom.touptek.xcamview.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView]com.touptek.xcamview.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView.Corner                                                                                                                                                                                                            
<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>All Classes and Interfaces</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html#all-classes">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="All Classes and Interfaces" class="title">All Classes and Interfaces</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="all-classes-table-tab1" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab1', 2)" class="table-tab">接口</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">类</button><button id="all-classes-table-tab3" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab3', 2)" class="table-tab">Enum Classes</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="all-classes-table-tab0">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpFileManager 类提供文件存储相关的工具方法。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/utils/TpFileManager.StorageListener.html" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">存储设备变化监听接口。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/utils/TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpHdmiMonitor 类用于检测和管理HDMI输入状态。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/utils/TpHdmiMonitor.HdmiListener.html" title="com.touptek.utils中的接口">TpHdmiMonitor.HdmiListener</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">定义 HDMI 状态变化监听接口。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpImageView - 模仿Android系统相册的可缩放ImageView</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/ui/TpImageView.OnZoomChangeListener.html" title="com.touptek.ui中的接口">TpImageView.OnZoomChangeListener</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">缩放变化监听器</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">TpIspParam 枚举类用于定义和管理摄像头ISP参数。</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/video/TpIspParam.OnDataChangedListener.html" title="com.touptek.video中的接口">TpIspParam.OnDataChangedListener</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">数据变化监听器接口</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/video/TpIspParam.OnSerialStateChangedListener.html" title="com.touptek.video中的接口">TpIspParam.OnSerialStateChangedListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">串口状态变化监听器接口</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/video/TpIspParam.ParamData.html" title="com.touptek.video中的类">TpIspParam.ParamData</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">参数数据结构</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/video/TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">场景信息数据类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpNetworkMonitor 类用于管理WiFi、以太网和热点相关操作。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/utils/TpNetworkMonitor.HotspotInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.HotspotInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">热点信息类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/utils/TpNetworkMonitor.NetworkInterfaceInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.NetworkInterfaceInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">网络接口信息类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html" title="com.touptek.utils中的接口">TpNetworkMonitor.NetworkStateListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">网络状态监听器接口</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/utils/TpNetworkMonitor.WifiConnectionInfo.html" title="com.touptek.utils中的类">TpNetworkMonitor.WifiConnectionInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">WiFi连接信息类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpRoiView - 可拖动的感兴趣区域(ROI)选择框</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">SambaUploader类用于连接Samba服务器并上传图片和视频。</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/utils/TpSambaClient.DirectoryListListener.html" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">目录列表回调接口</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/utils/TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">SMB连接配置类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/utils/TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">上传结果回调接口</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpTextureView - 支持缩放和平移的TextureView</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/ui/TpTextureView.OnZoomChangeListener.html" title="com.touptek.ui中的接口">TpTextureView.OnZoomChangeListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">缩放变化监听器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/ui/TpTextureView.TouchEventHandler.html" title="com.touptek.ui中的接口">TpTextureView.TouchEventHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">触摸事件处理策略接口</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/video/TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpVideoConfig - ToupTek视频配置类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/touptek/video/TpVideoConfig.BitrateMode.html" title="enum class in com.touptek.video">TpVideoConfig.BitrateMode</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">比特率模式</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/video/TpVideoConfig.Builder.html" title="com.touptek.video中的类">TpVideoConfig.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/touptek/video/TpVideoConfig.VideoCodec.html" title="enum class in com.touptek.video">TpVideoConfig.VideoCodec</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">视频编码格式</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpVideoPlayerView - 高层封装的视频播放组件</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/ui/TpVideoPlayerView.VideoPlayerListener.html" title="com.touptek.ui中的接口">TpVideoPlayerView.VideoPlayerListener</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">视频播放器监听器接口</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TpVideoSystem - ToupTek视频系统类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/touptek/video/TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">流类型枚举</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/touptek/video/TpVideoSystem.TpVideoSystemAdapter.html" title="com.touptek.video中的类">TpVideoSystem.TpVideoSystemAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">视频系统监听器适配器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">视频系统监听器接口</div>
</div>
</div>
</div>
</div>
</main>
</div>
</div>
</body>
</html>

<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>S - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="index: S">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:S">S</h2>
<dl class="index">
<dt><a href="../com/touptek/video/TpIspParam.html#saveAllDefaultValuesToLocal(boolean)" class="member-name-link">saveAllDefaultValuesToLocal(boolean)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">将所有参数恢复为默认值</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#saveCurrentAsScene(java.lang.String)" class="member-name-link">saveCurrentAsScene(String)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">保存当前所有ISP参数为命名场景</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.SceneInfo.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,long,boolean,int)" class="member-name-link">SceneInfo(String, String, String, long, boolean, int)</a> - 类的构造器 com.touptek.video.<a href="../com/touptek/video/TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpIspParam.SceneInfo.html#sceneName" class="member-name-link">sceneName</a> - 类中的变量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpIspParam.SceneInfo.html#sceneType" class="member-name-link">sceneType</a> - 类中的变量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpIspParam.SceneInfo.html#sceneValue" class="member-name-link">sceneValue</a> - 类中的变量 com.touptek.video.<a href="../com/touptek/video/TpIspParam.SceneInfo.html" title="com.touptek.video中的类">TpIspParam.SceneInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.StreamType.html#SCREEN" class="member-name-link">SCREEN</a> - enum class 中的枚举常量 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a></dt>
<dd>
<div class="block">屏幕流</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#seekCurrentVideoRelative(long)" class="member-name-link">seekCurrentVideoRelative(long)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">当前视频相对跳转（简化版）</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#seekRelative(long)" class="member-name-link">seekRelative(long)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">相对跳转</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#seekTo(long)" class="member-name-link">seekTo(long)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">跳转到指定位置</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#seekVideoTo(long)" class="member-name-link">seekVideoTo(long)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">跳转到指定位置（简化版）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.Builder.html#setBitRate(int)" class="member-name-link">setBitRate(int)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.Builder.html" title="com.touptek.video中的类">TpVideoConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.Builder.html#setBitrateMode(com.touptek.video.TpVideoConfig.BitrateMode)" class="member-name-link">setBitrateMode(TpVideoConfig.BitrateMode)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.Builder.html" title="com.touptek.video中的类">TpVideoConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#setCameraResolution(int,int)" class="member-name-link">setCameraResolution(int, int)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">设置相机实际分辨率</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.Builder.html#setCodec(com.touptek.video.TpVideoConfig.VideoCodec)" class="member-name-link">setCodec(TpVideoConfig.VideoCodec)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.Builder.html" title="com.touptek.video中的类">TpVideoConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.html#setConnectionParams(com.touptek.utils.TpSambaClient.SMBConfig)" class="member-name-link">setConnectionParams(TpSambaClient.SMBConfig)</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></dt>
<dd>
<div class="block">设置Samba连接参数（使用配置对象）</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.html#setConnectionParams(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)" class="member-name-link">setConnectionParams(String, String, String, String, String, boolean)</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></dt>
<dd>
<div class="block">设置Samba连接参数</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#setCurrentVideoPlaybackSpeed(float)" class="member-name-link">setCurrentVideoPlaybackSpeed(float)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">设置当前视频的播放速度（简化版）</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#setDoubleTapEnabled(boolean)" class="member-name-link">setDoubleTapEnabled(boolean)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">设置是否启用双击缩放功能</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#setDoubleTapEnabled(boolean)" class="member-name-link">setDoubleTapEnabled(boolean)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">设置是否启用双击缩放功能</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.html#setEnabled(boolean)" class="member-name-link">setEnabled(boolean)</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.html" title="com.touptek.utils中的类">TpSambaClient</a></dt>
<dd>
<div class="block">设置是否启用Samba上传</div>
</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.html#setHdmiListener(com.touptek.utils.TpHdmiMonitor.HdmiListener)" class="member-name-link">setHdmiListener(TpHdmiMonitor.HdmiListener)</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></dt>
<dd>
<div class="block">设置 HDMI 状态变化监听器。</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#setImageDrawable(android.graphics.drawable.Drawable)" class="member-name-link">setImageDrawable(Drawable)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoConfig.Builder.html#setKeyFrameInterval(int)" class="member-name-link">setKeyFrameInterval(int)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoConfig.Builder.html" title="com.touptek.video中的类">TpVideoConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#setListener(com.touptek.video.TpVideoSystem.TpVideoSystemListener)" class="member-name-link">setListener(TpVideoSystem.TpVideoSystemListener)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">设置监听器</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#setMaxScale(float)" class="member-name-link">setMaxScale(float)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">设置最大缩放倍数（推荐API）</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#setMaxScale(float)" class="member-name-link">setMaxScale(float)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">设置最大缩放倍数（推荐API）</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#setOnSerialStateChangedListener(com.touptek.video.TpIspParam.OnSerialStateChangedListener)" class="member-name-link">setOnSerialStateChangedListener(TpIspParam.OnSerialStateChangedListener)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">设置串口状态变化监听器。</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#setOnZoomChangeListener(com.touptek.ui.TpImageView.OnZoomChangeListener)" class="member-name-link">setOnZoomChangeListener(TpImageView.OnZoomChangeListener)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">设置缩放变化监听器</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#setOnZoomChangeListener(com.touptek.ui.TpTextureView.OnZoomChangeListener)" class="member-name-link">setOnZoomChangeListener(TpTextureView.OnZoomChangeListener)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">设置缩放变化监听器</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#setPanEnabled(boolean)" class="member-name-link">setPanEnabled(boolean)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">设置是否启用平移功能</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#setPanEnabled(boolean)" class="member-name-link">setPanEnabled(boolean)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">设置是否启用平移功能</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#setParamDefault(com.touptek.video.TpIspParam,int)" class="member-name-link">setParamDefault(TpIspParam, int)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">设置参数的默认值</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#setParamDisabled(com.touptek.video.TpIspParam,boolean)" class="member-name-link">setParamDisabled(TpIspParam, boolean)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">设置参数的禁用状态</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#setParamMaxValue(com.touptek.video.TpIspParam,int)" class="member-name-link">setParamMaxValue(TpIspParam, int)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">设置参数的最大值</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#setParamMinValue(com.touptek.video.TpIspParam,int)" class="member-name-link">setParamMinValue(TpIspParam, int)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">设置参数的最小值</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#setParamRange(com.touptek.video.TpIspParam,boolean,int,int,int)" class="member-name-link">setParamRange(TpIspParam, boolean, int, int, int)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">设置参数的完整范围（包含禁用状态）</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#setParamsRangeReceived(boolean)" class="member-name-link">setParamsRangeReceived(boolean)</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">设置所有参数范围已接收标志</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#setPlaybackSpeed(float)" class="member-name-link">setPlaybackSpeed(float)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">设置播放速度</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#setROIEnabled(boolean)" class="member-name-link">setROIEnabled(boolean)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">控制ROI框的显示/隐藏</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#setRoiView(com.touptek.ui.TpRoiView)" class="member-name-link">setRoiView(TpRoiView)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">设置ROI视图（用于同步变换）</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#setShowControls(boolean)" class="member-name-link">setShowControls(boolean)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">设置是否显示播放控制界面</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#setStreamType(com.touptek.video.TpVideoSystem.StreamType)" class="member-name-link">setStreamType(TpVideoSystem.StreamType)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">设置推流类型</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#setTouchEventHandler(com.touptek.ui.TpTextureView.TouchEventHandler)" class="member-name-link">setTouchEventHandler(TpTextureView.TouchEventHandler)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">设置触摸事件处理策略</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#setTvContainer(android.view.ViewGroup)" class="member-name-link">setTvContainer(ViewGroup)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">设置TV预览容器</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#setVideoPath(java.lang.String)" class="member-name-link">setVideoPath(String)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">设置视频文件路径</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#setVideoPlayerListener(com.touptek.ui.TpVideoPlayerView.VideoPlayerListener)" class="member-name-link">setVideoPlayerListener(TpVideoPlayerView.VideoPlayerListener)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">设置视频播放器监听器</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#setZoomEnabled(boolean)" class="member-name-link">setZoomEnabled(boolean)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>
<div class="block">设置是否启用缩放功能</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#setZoomEnabled(boolean)" class="member-name-link">setZoomEnabled(boolean)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>
<div class="block">设置是否启用缩放功能</div>
</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.SMBConfig.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)" class="member-name-link">SMBConfig(String, String, String, String, String, boolean)</a> - 类的构造器 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#startMonitoring()" class="member-name-link">startMonitoring()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">开始监听网络状态变化</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#startRecording(java.lang.String)" class="member-name-link">startRecording(String)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">开始录制</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#startStreaming()" class="member-name-link">startStreaming()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">启动推流（使用默认配置）</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#startStreaming(com.touptek.video.TpVideoSystem.StreamType)" class="member-name-link">startStreaming(TpVideoSystem.StreamType)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">启动推流</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#startStreaming(com.touptek.video.TpVideoSystem.StreamType,java.lang.String)" class="member-name-link">startStreaming(TpVideoSystem.StreamType, String)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">启动推流</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#startUsbDriveMonitor(android.content.Context,com.touptek.utils.TpFileManager.StorageListener)" class="member-name-link">startUsbDriveMonitor(Context, TpFileManager.StorageListener)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">开始监听U盘插拔事件。</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#stepCurrentVideoFrame()" class="member-name-link">stepCurrentVideoFrame()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">当前视频逐帧播放（简化版）</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#stepFrame()" class="member-name-link">stepFrame()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">逐帧播放</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#stop()" class="member-name-link">stop()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>
<div class="block">停止播放</div>
</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.html#stop()" class="member-name-link">stop()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpHdmiMonitor.html" title="com.touptek.utils中的类">TpHdmiMonitor</a></dt>
<dd>
<div class="block">停止 HDMI 状态检测线程。</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#stopMonitoring()" class="member-name-link">stopMonitoring()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">停止监听网络状态变化</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#stopRecording()" class="member-name-link">stopRecording()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">停止录制</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#stopStreaming()" class="member-name-link">stopStreaming()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">停止推流</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.html#stopUsbDriveMonitor(android.content.Context)" class="member-name-link">stopUsbDriveMonitor(Context)</a> - 类中的静态方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.html" title="com.touptek.utils中的类">TpFileManager</a></dt>
<dd>
<div class="block">停止监听U盘插拔事件。</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#switchToCameraMode()" class="member-name-link">switchToCameraMode()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">切换到Camera模式</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.html#switchToTvMode()" class="member-name-link">switchToTvMode()</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.html" title="com.touptek.video中的类">TpVideoSystem</a></dt>
<dd>
<div class="block">切换到TV模式</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.html#syncAllCurrentValuesToDevice()" class="member-name-link">syncAllCurrentValuesToDevice()</a> - enum class中的静态方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.html" title="enum class in com.touptek.video">TpIspParam</a></dt>
<dd>
<div class="block">同步所有当前值到设备</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>

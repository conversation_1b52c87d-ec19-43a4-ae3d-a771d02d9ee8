// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FolderItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivFolderIcon;

  @NonNull
  public final TextView tvFileCount;

  @NonNull
  public final TextView tvFolderName;

  private FolderItemBinding(@NonNull LinearLayout rootView, @NonNull ImageView ivFolderIcon,
      @NonNull TextView tvFileCount, @NonNull TextView tvFolderName) {
    this.rootView = rootView;
    this.ivFolderIcon = ivFolderIcon;
    this.tvFileCount = tvFileCount;
    this.tvFolderName = tvFolderName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FolderItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FolderItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.folder_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FolderItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_folder_icon;
      ImageView ivFolderIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivFolderIcon == null) {
        break missingId;
      }

      id = R.id.tv_file_count;
      TextView tvFileCount = ViewBindings.findChildViewById(rootView, id);
      if (tvFileCount == null) {
        break missingId;
      }

      id = R.id.tv_folder_name;
      TextView tvFolderName = ViewBindings.findChildViewById(rootView, id);
      if (tvFolderName == null) {
        break missingId;
      }

      return new FolderItemBinding((LinearLayout) rootView, ivFolderIcon, tvFileCount,
          tvFolderName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.touptek.utils;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * TpNetworkMonitor 类用于管理WiFi、以太网和热点相关操作。
 * <p>
 * 此类提供了网络连接的监控、状态查询和控制功能，支持WiFi、以太网和热点的状态管理。
 * 通过回调机制通知应用网络状态变化，并提供网络接口信息查询功能。
 * </p>
 * 
 * <p><b>主要功能：</b></p>
 * <ul>
 *   <li>监控WiFi和以太网连接状态</li>
 *   <li>管理和监控热点状态</li>
 *   <li>提供网络接口信息查询</li>
 *   <li>支持WiFi开关控制</li>
 *   <li>打开系统网络设置界面</li>
 * </ul>
 * 
 * <p><b>使用示例：</b></p>
 * <pre>{@code
 * // 创建网络管理器
 * TpNetworkMonitor networkManager = new TpNetworkMonitor(context,
 *     new TpNetworkMonitor.NetworkStateListener() {
 *         @Override
 *         public void onWifiStateChanged(boolean isConnected, String ssid) {
 *             if (isConnected) {
 *                 Log.d(TAG, "WiFi已连接: " + ssid);
 *             } else {
 *                 Log.d(TAG, "WiFi已断开");
 *             }
 *         }
 *         
 *         @Override
 *         public void onEthernetStateChanged(boolean isConnected) {
 *             Log.d(TAG, "以太网状态: " + (isConnected ? "已连接" : "已断开"));
 *         }
 *         
 *         @Override
 *         public void onHotspotStateChanged(boolean isEnabled, String hotspotInfo) {
 *             Log.d(TAG, "热点状态: " + (isEnabled ? "已开启" : "已关闭") + 
 *                   (isEnabled ? ", " + hotspotInfo : ""));
 *         }
 *     }, 
 *     wifiPanelLauncher
 * );
 * 
 * // 开始监控网络状态
 * networkManager.startMonitoring();
 * 
 * // 获取网络接口信息
 * List<TpNetworkMonitor.NetworkInterfaceInfo> interfaces =
 *     networkManager.getAvailableNetworkInterfaces();
 * 
 * // 控制WiFi
 * networkManager.toggleWifi(isCurrentlyConnected);
 * 
 * // 停止监控
 * networkManager.stopMonitoring();
 * }</pre>
 * 
 */
public class TpNetworkMonitor {
    
    /**
     * 网络状态监听器接口
     * <p>
     * 用于接收WiFi、以太网和热点状态变化的回调通知。
     * 实现此接口可以在UI上实时显示网络状态变化。
     * </p>
     */
    public interface NetworkStateListener {
        /**
         * 当WiFi状态发生变化时调用
         * 
         * @param isConnected 是否已连接到WiFi网络
         * @param ssid 当前连接的WiFi网络名称，如果未连接则为空字符串
         */
        void onWifiStateChanged(boolean isConnected, String ssid);
        
        /**
         * 当以太网状态发生变化时调用
         * 
         * @param isConnected 是否已连接到以太网
         */
        void onEthernetStateChanged(boolean isConnected);
        
        /**
         * 当热点状态发生变化时调用
         * 
         * @param isEnabled 热点是否已启用
         * @param hotspotInfo 热点信息，包含SSID和密码等
         */
        void onHotspotStateChanged(boolean isEnabled, String hotspotInfo);
    }
    
    private final Context context;
    private final WifiManager wifiManager;
    private final ConnectivityManager connectivityManager;
    private final NetworkStateListener listener;
    
    // 网络回调
    private ConnectivityManager.NetworkCallback networkCallback;
    
    // 热点状态监听
    private Handler mainHandler;
    private Runnable hotspotCheckRunnable;
    private BroadcastReceiver tetherChangeReceiver;
    private final ActivityResultLauncher<Intent> wifiPanelLauncher;
    
    /**
     * 创建一个新的网络管理器实例
     * 
     * @param context 应用上下文，用于访问系统服务
     * @param listener 网络状态监听器，用于接收网络状态变化通知
     * @param wifiPanelLauncher 用于启动系统WiFi设置面板的ActivityResultLauncher
     */
    public TpNetworkMonitor(Context context, NetworkStateListener listener, ActivityResultLauncher<Intent> wifiPanelLauncher) {
        this.context = context.getApplicationContext(); // 使用应用上下文避免内存泄漏
        this.listener = listener;
        this.wifiPanelLauncher = wifiPanelLauncher;
        
        // 初始化系统服务
        wifiManager = (WifiManager) this.context.getSystemService(Context.WIFI_SERVICE);
        connectivityManager = (ConnectivityManager) this.context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        // 初始化Handler
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * 开始监听网络状态变化
     * <p>
     * 此方法启动WiFi、以太网和热点状态的监控。
     * 注册网络回调以接收连接状态变化通知，并启动热点状态的周期性检查。
     * 调用此方法后，所有网络状态变化将通过NetworkStateListener回调通知。
     * 方法会立即更新当前网络状态并触发初始回调。
     * </p>
     */
    public void startMonitoring() {
        // 设置网络状态监听
        setupNetworkMonitoring();
        
        // 设置热点状态监听
        setupHotspotMonitoring();
        
        // 立即更新当前状态
        updateNetworkStatus();
        updateHotspotStatus();
    }
    
    /**
     * 停止监听网络状态变化
     */
    public void stopMonitoring() {
        // 注销网络监听
        if (networkCallback != null) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
            } catch (Exception e) {
                // 忽略可能的异常
            }
        }
        
        // 注销热点状态广播接收器
        if (tetherChangeReceiver != null) {
            try {
                context.unregisterReceiver(tetherChangeReceiver);
            } catch (Exception e) {
                // 忽略可能的异常
            }
        }
        
        // 停止热点状态检查
        if (mainHandler != null && hotspotCheckRunnable != null) {
            mainHandler.removeCallbacks(hotspotCheckRunnable);
        }
    }
    
    /**
     * 暂停热点状态检查（在Activity onPause时调用）
     */
    public void pauseHotspotMonitoring() {
        if (mainHandler != null && hotspotCheckRunnable != null) {
            mainHandler.removeCallbacks(hotspotCheckRunnable);
        }
    }
    
    /**
     * 恢复热点状态检查（在Activity onResume时调用）
     */
    public void resumeHotspotMonitoring() {
        if (mainHandler != null && hotspotCheckRunnable != null) {
            updateNetworkStatus();
            updateHotspotStatus();
            mainHandler.post(hotspotCheckRunnable);
        }
    }
    
    /**
     * 获取当前WiFi连接状态
     * <p>
     * 此方法检查当前WiFi的连接状态，包括是否连接和SSID信息。
     * 通过ConnectivityManager和NetworkCapabilities API获取准确的连接状态，
     * 确保只有当WiFi传输通道实际可用时才返回已连接状态。
     * 如果WiFi已连接，还会获取当前连接的SSID。
     * </p>
     * 
     * @return WiFi连接信息对象，包含连接状态(isConnected)和网络名称(ssid)
     */
    public WifiConnectionInfo getCurrentWifiState() {
        boolean isWifiConnected = false;
        String ssid = "";
        
        // 获取当前活动网络
        Network activeNetwork = connectivityManager.getActiveNetwork();
        if (activeNetwork != null) {
            NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
            if (networkCapabilities != null) {
                isWifiConnected = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
            }
        }
        
        // 如果WiFi已连接，获取SSID
        if (isWifiConnected) {
            ssid = wifiManager.getConnectionInfo().getSSID();
        }
        
        return new WifiConnectionInfo(isWifiConnected, ssid);
    }
    
    /**
     * 获取当前以太网连接状态
     * @return 是否连接到以太网
     */
    public boolean isEthernetConnected() {
        Network activeNetwork = connectivityManager.getActiveNetwork();
        if (activeNetwork != null) {
            NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
            if (networkCapabilities != null) {
                return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET);
            }
        }
        return false;
    }
    
    /**
     * 获取当前热点状态
     * @return 热点状态信息，包含是否开启和配置信息
     */
    public HotspotInfo getCurrentHotspotState() {
        boolean isEnabled = false;
        String hotspotInfo = "";
        
        try {
            // 使用反射方法获取热点状态
            Method method = wifiManager.getClass().getDeclaredMethod("isWifiApEnabled");
            method.setAccessible(true);
            isEnabled = (boolean) method.invoke(wifiManager);
            
            if (isEnabled) {
                // 尝试获取热点配置信息
                try {
                    Method getConfigMethod = wifiManager.getClass().getDeclaredMethod("getWifiApConfiguration");
                    getConfigMethod.setAccessible(true);
                    WifiConfiguration config = (WifiConfiguration) getConfigMethod.invoke(wifiManager);
                    
                    if (config != null && config.SSID != null) {
                        hotspotInfo = "热点名称: " + config.SSID;
                        if (config.preSharedKey != null) {
                            hotspotInfo += "\n密码: " + config.preSharedKey;
                        }
                    } else {
                        hotspotInfo = "热点已开启";
                    }
                } catch (Exception e) {
                    hotspotInfo = "热点已开启";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return new HotspotInfo(isEnabled, hotspotInfo);
    }
    
    /**
     * 切换WiFi开关状态
     * <p>
     * 如果WiFi当前已开启，则关闭WiFi；如果当前已关闭，则开启WiFi。
     * 此方法会立即执行操作，无需用户确认。
     * </p>
     * 
     * @param isCurrentlyConnected 当前WiFi是否已连接，true表示已连接，false表示未连接
     */
    public void toggleWifi(boolean isCurrentlyConnected) {
        if (isCurrentlyConnected) {
            disconnectWifi();
        } else {
            enableAndConnectWifi();
        }
    }
    
    /**
     * 打开热点设置页面
     */
    public void openHotspotSettings() {
        try {
            Intent intent = new Intent();
            intent.setClassName("com.android.settings", "com.android.settings.TetherSettings");
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            // 如果直接跳转失败，回退到常规无线设置页面
            Intent fallbackIntent = new Intent(Settings.ACTION_WIRELESS_SETTINGS);
            fallbackIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(fallbackIntent);
        }
    }
    
    /**
     * 设置网络监听
     */
    private void setupNetworkMonitoring() {
        // 创建网络回调
        networkCallback = new ConnectivityManager.NetworkCallback() {
            @Override
            public void onAvailable(@NonNull Network network) {
                super.onAvailable(network);
                // 确保在主线程上更新网络状态
                mainHandler.post(() -> updateNetworkStatus());
            }

            @Override
            public void onLost(@NonNull Network network) {
                super.onLost(network);
                // 确保在主线程上更新网络状态
                mainHandler.post(() -> updateNetworkStatus());
            }

            @Override
            public void onCapabilitiesChanged(@NonNull Network network, @NonNull NetworkCapabilities capabilities) {
                super.onCapabilitiesChanged(network, capabilities);
                // 确保在主线程上更新网络状态
                mainHandler.post(() -> updateNetworkStatus());
            }
        };

        // 注册网络回调
        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        builder.addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
               .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
               .addTransportType(NetworkCapabilities.TRANSPORT_ETHERNET);
        
        try {
            connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 设置热点状态监听
     */
    private void setupHotspotMonitoring() {
        // 创建热点检查任务
        hotspotCheckRunnable = new Runnable() {
            @Override
            public void run() {
                updateHotspotStatus();
                // 每2秒检查一次
                mainHandler.postDelayed(this, 2000);
            }
        };
        
        // 启动热点检查
        mainHandler.post(hotspotCheckRunnable);
        
        // 注册广播接收器以监听网络共享状态变化
        IntentFilter filter = new IntentFilter("android.net.conn.TETHER_STATE_CHANGED");
        tetherChangeReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                updateHotspotStatus();
            }
        };
        
        try {
            context.registerReceiver(tetherChangeReceiver, filter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 更新网络状态并通知监听器
     */
    private void updateNetworkStatus() {
        try {
            // 更新WiFi状态
            WifiConnectionInfo wifiInfo = getCurrentWifiState();
            if (listener != null) {
                listener.onWifiStateChanged(wifiInfo.isConnected(), wifiInfo.getSsid());
            }
            
            // 更新以太网状态
            boolean isEthernetConnected = isEthernetConnected();
            if (listener != null) {
                listener.onEthernetStateChanged(isEthernetConnected);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 更新热点状态并通知监听器
     */
    private void updateHotspotStatus() {
        try {
            // 获取热点状态
            HotspotInfo hotspotInfo = getCurrentHotspotState();
            
            // 通知监听器
            if (listener != null) {
                listener.onHotspotStateChanged(hotspotInfo.isEnabled(), hotspotInfo.getInfo());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 启用WiFi并尝试连接
     */
    private void enableAndConnectWifi() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // 对于Android Q (API 29) 及以上版本，使用设置面板
                if (wifiPanelLauncher != null) {
                    wifiPanelLauncher.launch(new Intent(Settings.Panel.ACTION_WIFI));
                } else {
                    // Fallback for when the launcher is not available
                    Intent panelIntent = new Intent(Settings.Panel.ACTION_WIFI);
                    panelIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(panelIntent);
                }
            } else {
                // 对于旧版本，启用WiFi并打开WiFi设置页面
                wifiManager.setWifiEnabled(true);
                Intent intent = new Intent(Settings.ACTION_WIFI_SETTINGS);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 断开WiFi连接
     */
    private void disconnectWifi() {
        try {
            // 禁用WiFi
            wifiManager.setWifiEnabled(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * WiFi连接信息类
     * <p>
     * 封装WiFi连接状态和SSID信息。
     * </p>
     */
    public static class WifiConnectionInfo {
        private final boolean connected;
        private final String ssid;
        
        /**
         * 创建一个新的WiFi连接信息对象
         * 
         * @param connected 是否已连接到WiFi网络
         * @param ssid 当前连接的WiFi网络名称
         */
        public WifiConnectionInfo(boolean connected, String ssid) {
            this.connected = connected;
            this.ssid = ssid;
        }
        
        /**
         * 获取WiFi连接状态
         * 
         * @return 是否已连接到WiFi网络
         */
        public boolean isConnected() {
            return connected;
        }
        
        /**
         * 获取当前连接的WiFi网络名称
         * 
         * @return WiFi网络的SSID，如果未连接则为空字符串
         */
        public String getSsid() {
            return ssid;
        }
    }
    
    /**
     * 热点信息类
     * <p>
     * 封装热点状态和配置信息。
     * </p>
     */
    public static class HotspotInfo {
        private final boolean enabled;
        private final String info;
        
        /**
         * 创建一个新的热点信息对象
         * 
         * @param enabled 热点是否已启用
         * @param info 热点配置信息，包含SSID和密码等
         */
        public HotspotInfo(boolean enabled, String info) {
            this.enabled = enabled;
            this.info = info;
        }
        
        /**
         * 获取热点启用状态
         * 
         * @return 热点是否已启用
         */
        public boolean isEnabled() {
            return enabled;
        }
        
        /**
         * 获取热点配置信息
         * 
         * @return 热点配置信息，包含SSID和密码等
         */
        public String getInfo() {
            return info;
        }
    }

    /**
     * 获取所有可用的网络接口信息
     * 
     * @return 网络接口信息列表
     */
    public List<NetworkInterfaceInfo> getAvailableNetworkInterfaces() {
        List<NetworkInterfaceInfo> interfaces = new ArrayList<>();
        
        try {
            java.util.Enumeration<java.net.NetworkInterface> networkInterfaces = 
                java.net.NetworkInterface.getNetworkInterfaces();
            
            while (networkInterfaces.hasMoreElements()) {
                java.net.NetworkInterface networkInterface = networkInterfaces.nextElement();
                String interfaceName = networkInterface.getName();
                
                if (!networkInterface.isUp() || networkInterface.isLoopback()) {
                    continue;
                }
                
                // 获取IP地址
                java.util.Enumeration<java.net.InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    java.net.InetAddress address = addresses.nextElement();
                    if (!address.isLoopbackAddress() && address instanceof java.net.Inet4Address) {
                        // 添加到列表
                        NetworkInterfaceInfo info = new NetworkInterfaceInfo(
                            interfaceName, 
                            address.getHostAddress(),
                            getInterfaceTypeDescription(interfaceName)
                        );
                        interfaces.add(info);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return interfaces;
    }

    /**
     * 根据网络接口名称获取描述
     */
    private String getInterfaceTypeDescription(String interfaceName) {
        if (interfaceName.startsWith("eth")) {
            return "以太网";
        } else if (interfaceName.startsWith("wlan")) {
            return "WiFi";
        } else if (interfaceName.startsWith("rmnet") || interfaceName.startsWith("ccmni")) {
            return "移动数据";
        } else {
            return "其他网络";
        }
    }

    /**
     * 网络接口信息类
     * <p>
     * 封装网络接口的名称、IP地址和描述信息。
     * 用于展示设备上可用的网络接口。
     * </p>
     */
    public static class NetworkInterfaceInfo {
        private String name;        // 接口名称，如wlan0
        private String ipAddress;   // IP地址
        private String description; // 描述，如"WiFi"
        
        /**
         * 创建一个新的网络接口信息对象
         * 
         * @param name 网络接口名称（如eth0, wlan0）
         * @param ipAddress 网络接口的IP地址
         * @param description 网络接口的描述信息
         */
        public NetworkInterfaceInfo(String name, String ipAddress, String description) {
            this.name = name;
            this.ipAddress = ipAddress;
            this.description = description;
        }
        
        /**
         * 获取网络接口名称
         * 
         * @return 网络接口名称
         */
        public String getName() {
            return name;
        }
        
        /**
         * 获取网络接口的IP地址
         * 
         * @return 网络接口的IP地址，如果未分配则为空字符串
         */
        public String getIpAddress() {
            return ipAddress;
        }
        
        /**
         * 获取网络接口的描述信息
         * 
         * @return 网络接口的描述信息
         */
        public String getDescription() {
            return description;
        }
        
        @Override
        public String toString() {
            return description + " (" + name + "): " + ipAddress;
        }
    }
} 
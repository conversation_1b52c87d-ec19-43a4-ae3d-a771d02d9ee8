package com.touptek.xcamview.view;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\u00020\u0001:\u0004/012B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\u001a\u001a\u00020\u001bJ \u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\nH\u0002J \u0010!\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\"\u001a\u00020\u000e2\u0006\u0010#\u001a\u00020\u000eH\u0002J \u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020\n2\u0006\u0010\'\u001a\u00020\n2\u0006\u0010(\u001a\u00020\u000eH\u0002J\u0010\u0010)\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u001eH\u0014J\u0010\u0010*\u001a\u00020%2\u0006\u0010+\u001a\u00020,H\u0016J\u000e\u0010-\u001a\u00020\u001b2\u0006\u0010.\u001a\u00020\fR\u000e\u0010\t\u001a\u00020\nX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/touptek/xcamview/view/MeasurementOverlayView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "HANDLE_RADIUS", "", "currentMode", "Lcom/touptek/xcamview/view/MeasurementOverlayView$Mode;", "endPoint", "Landroid/graphics/PointF;", "handleFillPaint", "Landroid/graphics/Paint;", "handlePaint", "linePaint", "selectedHandle", "selectedLineIndex", "shapes", "", "Lcom/touptek/xcamview/view/MeasurementOverlayView$Shape;", "startPoint", "textPaint", "clearAll", "", "drawCircleMeasurementText", "canvas", "Landroid/graphics/Canvas;", "center", "radius", "drawMeasurementText", "start", "end", "isInHandle", "", "x", "y", "point", "onDraw", "onTouchEvent", "event", "Landroid/view/MotionEvent;", "setMode", "mode", "Circle", "Line", "Mode", "Shape", "app_debug"})
public final class MeasurementOverlayView extends android.view.View {
    private final android.graphics.Paint linePaint = null;
    private final android.graphics.Paint handlePaint = null;
    private final android.graphics.Paint handleFillPaint = null;
    private final float HANDLE_RADIUS = 20.0F;
    private final android.graphics.Paint textPaint = null;
    private android.graphics.PointF startPoint;
    private android.graphics.PointF endPoint;
    private com.touptek.xcamview.view.MeasurementOverlayView.Mode currentMode = com.touptek.xcamview.view.MeasurementOverlayView.Mode.LINE;
    private final java.util.List<com.touptek.xcamview.view.MeasurementOverlayView.Shape> shapes = null;
    private int selectedHandle = -1;
    private int selectedLineIndex = -1;
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    public final void setMode(@org.jetbrains.annotations.NotNull
    com.touptek.xcamview.view.MeasurementOverlayView.Mode mode) {
    }
    
    @java.lang.Override
    protected void onDraw(@org.jetbrains.annotations.NotNull
    android.graphics.Canvas canvas) {
    }
    
    private final void drawMeasurementText(android.graphics.Canvas canvas, android.graphics.PointF start, android.graphics.PointF end) {
    }
    
    private final void drawCircleMeasurementText(android.graphics.Canvas canvas, android.graphics.PointF center, float radius) {
    }
    
    @java.lang.Override
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event) {
        return false;
    }
    
    private final boolean isInHandle(float x, float y, android.graphics.PointF point) {
        return false;
    }
    
    public final void clearAll() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0004\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/xcamview/view/MeasurementOverlayView$Mode;", "", "(Ljava/lang/String;I)V", "LINE", "CIRCLE", "app_debug"})
    public static enum Mode {
        /*public static final*/ LINE /* = new LINE() */,
        /*public static final*/ CIRCLE /* = new CIRCLE() */;
        
        Mode() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0002\u0003\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/xcamview/view/MeasurementOverlayView$Shape;", "", "()V", "Lcom/touptek/xcamview/view/MeasurementOverlayView$Circle;", "Lcom/touptek/xcamview/view/MeasurementOverlayView$Line;", "app_debug"})
    public static abstract class Shape {
        
        private Shape() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0014"}, d2 = {"Lcom/touptek/xcamview/view/MeasurementOverlayView$Line;", "Lcom/touptek/xcamview/view/MeasurementOverlayView$Shape;", "start", "Landroid/graphics/PointF;", "end", "(Landroid/graphics/PointF;Landroid/graphics/PointF;)V", "getEnd", "()Landroid/graphics/PointF;", "getStart", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class Line extends com.touptek.xcamview.view.MeasurementOverlayView.Shape {
        @org.jetbrains.annotations.NotNull
        private final android.graphics.PointF start = null;
        @org.jetbrains.annotations.NotNull
        private final android.graphics.PointF end = null;
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.view.MeasurementOverlayView.Line copy(@org.jetbrains.annotations.NotNull
        android.graphics.PointF start, @org.jetbrains.annotations.NotNull
        android.graphics.PointF end) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public Line(@org.jetbrains.annotations.NotNull
        android.graphics.PointF start, @org.jetbrains.annotations.NotNull
        android.graphics.PointF end) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.graphics.PointF component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.graphics.PointF getStart() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.graphics.PointF component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.graphics.PointF getEnd() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0014"}, d2 = {"Lcom/touptek/xcamview/view/MeasurementOverlayView$Circle;", "Lcom/touptek/xcamview/view/MeasurementOverlayView$Shape;", "center", "Landroid/graphics/PointF;", "point", "(Landroid/graphics/PointF;Landroid/graphics/PointF;)V", "getCenter", "()Landroid/graphics/PointF;", "getPoint", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class Circle extends com.touptek.xcamview.view.MeasurementOverlayView.Shape {
        @org.jetbrains.annotations.NotNull
        private final android.graphics.PointF center = null;
        @org.jetbrains.annotations.NotNull
        private final android.graphics.PointF point = null;
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.view.MeasurementOverlayView.Circle copy(@org.jetbrains.annotations.NotNull
        android.graphics.PointF center, @org.jetbrains.annotations.NotNull
        android.graphics.PointF point) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public Circle(@org.jetbrains.annotations.NotNull
        android.graphics.PointF center, @org.jetbrains.annotations.NotNull
        android.graphics.PointF point) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.graphics.PointF component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.graphics.PointF getCenter() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.graphics.PointF component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.graphics.PointF getPoint() {
            return null;
        }
    }
}
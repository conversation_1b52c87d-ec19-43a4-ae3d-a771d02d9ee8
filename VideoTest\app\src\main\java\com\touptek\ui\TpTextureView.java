package com.touptek.ui;

import android.content.Context;
import android.graphics.Matrix;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.TextureView;

import com.touptek.ui.internal.TpViewTransform;

import java.lang.reflect.Field;

/**
 * TpTextureView - 支持缩放和平移的TextureView
 * <p>
 * 基于TpZoomableImageView的设计，为TextureView提供缩放和平移功能。
 * 支持与ROIView的同步变换，提供与系统相册一致的用户体验。
 * </p>
 * 
 * 主要功能：
 * <ul>
 *   <li>双指缩放（焦点稳定）</li>
 *   <li>单指平移（硬边界限制）</li>
 *   <li>双击缩放切换</li>
 *   <li>ROIView同步变换</li>
 *   <li>手势冲突解决</li>
 * </ul>
 * 
 * 使用方式：
 * <pre>{@code
 * // 在布局文件中使用
 * <com.android.rockchip.camera2.view.TpTextureView
 *     android:id="@+id/textureView"
 *     android:layout_width="match_parent"
 *     android:layout_height="match_parent" />
 * 
 * // 在代码中使用
 * TpTextureView textureView = findViewById(R.id.textureView);
 * textureView.setZoomEnabled(true);
 * textureView.setPanEnabled(true);
 * textureView.setRoiView(roiView);
 * }</pre>
 */
public class TpTextureView extends TextureView {
    private static final String TAG = "TpTextureView";
    
    /**
     * 手势状态枚举
     */
    private enum GestureState {
        IDLE,       // 空闲状态
        SCALING,    // 缩放中
        PANNING     // 平移中
    }
    
    /**
     * 触摸事件处理策略接口
     */
    public interface TouchEventHandler {
        /**
         * 通知单击手势
         * @param event 触摸事件
         * @return true表示处理了单击，false表示忽略
         */
        boolean onSingleTapDetected(MotionEvent event);

        /**
         * 通知长按手势
         * @param event 触摸事件
         */
        void onLongPressDetected(MotionEvent event);

        /**
         * 通知平移手势开始
         * @param event 触摸事件
         * @return true表示允许平移，false表示拒绝
         */
        boolean onPanGestureDetected(MotionEvent event);

        /**
         * 通知缩放手势开始
         * @param event 触摸事件
         * @return true表示允许缩放，false表示拒绝
         */
        boolean onScaleGestureDetected(MotionEvent event);
    }
    
    /**
     * 缩放信息类
     */
    private static class ScaleInfo {
        float currentScale = 1.0f;      // 当前缩放比例
        float maxScale = 5.0f;          // 最大缩放比例
        boolean userSetMaxScale = false; // 用户是否手动设置了最大缩放

        void setUserMaxScale(float userMaxScale) {
            maxScale = userMaxScale;
            userSetMaxScale = true;
        }

        float getNextDoubleTapScale() {
            // 简化的双击缩放：始终返回1.0倍
            return 1.0f;
        }
    }

    // 核心组件
    private Matrix mMatrix = new Matrix();
    private ScaleInfo mScaleInfo = new ScaleInfo();
    private GestureState mGestureState = GestureState.IDLE;
    
    // 手势检测器
    private GestureDetector mGestureDetector;
    private ScaleGestureDetector mScaleGestureDetector;

    // 触摸状态
    private float mLastTouchX = 0f;
    private float mLastTouchY = 0f;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mResetGestureStateRunnable;
    
    // 配置选项
    private boolean mZoomEnabled = true;
    private boolean mPanEnabled = true;
    private boolean mDoubleTapEnabled = true;
    
    // ROI视图支持
    private TpRoiView mTpRoiView;
    
    // 监听器
    private OnZoomChangeListener mZoomChangeListener;
    private TouchEventHandler mTouchEventHandler;
    
    /**
     * 缩放变化监听器
     */
    public interface OnZoomChangeListener {
        void onZoomChanged(float scale, float focusX, float focusY);
    }
    
    public TpTextureView(Context context) {
        super(context);
        init(context);
    }
    
    public TpTextureView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public TpTextureView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    /**
     * 初始化组件
     */
    private void init(Context context) {
        // 初始化手势检测器
        initGestureDetectors(context);
        
        Log.d(TAG, "TpZoomableTextureView初始化完成");
    }
    
    /**
     * 初始化手势检测器
     */
    private void initGestureDetectors(Context context) {
        // 创建优化的缩放手势检测器（使用反射降低最小识别距离）
        mScaleGestureDetector = createOptimizedScaleGestureDetector(context);

        // 通用手势检测器（用于双击和长按等）
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (!mDoubleTapEnabled || !mZoomEnabled) return false;

                // 通知上层检测到缩放手势（双击缩放）
                if (mTouchEventHandler != null && !mTouchEventHandler.onScaleGestureDetected(e)) {
                    Log.d(TAG, "双击缩放被上层拒绝");
                    return false;
                }

                float targetScale = mScaleInfo.getNextDoubleTapScale();
                float scaleFactor = targetScale / mScaleInfo.currentScale;

                return performScale(scaleFactor, e.getX(), e.getY());
            }

            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                Log.d(TAG, "检测到单击");

                // 通知上层处理单击
                if (mTouchEventHandler != null) {
                    return mTouchEventHandler.onSingleTapDetected(e);
                }

                performClick();
                return true;
            }
            
            @Override
            public void onLongPress(MotionEvent e) {
                // 通知长按事件
                if (mTouchEventHandler != null) {
                    mTouchEventHandler.onLongPressDetected(e);
                }
            }
        });
    }

    /**
     * 创建优化的缩放手势检测器
     * 使用反射修改系统mMinSpan值，解决双指距离较短时无法缩放的问题
     */
    private ScaleGestureDetector createOptimizedScaleGestureDetector(Context context) {
        // 创建标准的缩放手势检测器
        ScaleGestureDetector detector = new ScaleGestureDetector(context, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                if (!mZoomEnabled) return false;

                float scaleFactor = detector.getScaleFactor();
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();

                return performScale(scaleFactor, focusX, focusY);
            }

            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                mGestureState = GestureState.SCALING;
                cancelResetGestureState();
                Log.d(TAG, "缩放开始");
                return true;
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                scheduleResetGestureState();
                Log.d(TAG, "缩放结束");
            }
        });

        // 使用反射修改mMinSpan，解决双指距离较短时无法缩放的问题
        try {
            Field field = detector.getClass().getDeclaredField("mMinSpan");
            field.setAccessible(true);
            field.set(detector, 1); // 设置为1像素，几乎没有限制
            Log.d(TAG, "成功通过反射优化ScaleGestureDetector敏感度");
        } catch (Exception e) {
            Log.w(TAG, "反射优化ScaleGestureDetector失败，使用默认配置", e);
        }

        return detector;
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean handled = false;

        // 使用优化后的缩放手势检测器
        if (mScaleGestureDetector != null) {
            handled = mScaleGestureDetector.onTouchEvent(event) || handled;
        }

        // 使用通用手势检测器（双击、单击、长按）
        if (mGestureDetector != null) {
            handled = mGestureDetector.onTouchEvent(event) || handled;
        }

        // 处理平移手势（只在非缩放状态下）
        if (mGestureState != GestureState.SCALING && mPanEnabled) {
            handled = handlePanGesture(event) || handled;
        }

        return handled || super.onTouchEvent(event);
    }
    
    
    /**
     * 处理平移手势
     */
    private boolean handlePanGesture(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 先通知上层检测到平移手势，让上层决定是否允许
                if (mTouchEventHandler != null) {
                    boolean allowPan = mTouchEventHandler.onPanGestureDetected(event);
                    Log.d(TAG, "检测到平移手势，上层决定: " + (allowPan ? "允许" : "拒绝"));

                    if (!allowPan) {
                        Log.d(TAG, "平移被上层拒绝，不设置PANNING状态");
                        return false;
                    }
                }

                mGestureState = GestureState.PANNING;
                mLastTouchX = event.getX();
                mLastTouchY = event.getY();
                Log.d(TAG, "设置平移状态: PANNING");
                return true;

            case MotionEvent.ACTION_MOVE:
                if (event.getPointerCount() == 1 && mGestureState == GestureState.PANNING) {
                    float deltaX = event.getX() - mLastTouchX;
                    float deltaY = event.getY() - mLastTouchY;

                    mLastTouchX = event.getX();
                    mLastTouchY = event.getY();

                    Log.d(TAG, "平移: deltaX=" + deltaX + ", deltaY=" + deltaY);
                    return performTranslate(deltaX, deltaY);
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (mGestureState == GestureState.PANNING) {
                    Log.d(TAG, "平移结束，重置状态: IDLE");
                }
                mGestureState = GestureState.IDLE;
                break;
        }

        return false;
    }

    /**
     * 执行缩放操作（优化版本，防止黑边闪烁）
     */
    private boolean performScale(float scaleFactor, float focusX, float focusY) {
        if (!mZoomEnabled) return false;

        // 计算新的缩放比例
        float newScale = mScaleInfo.currentScale * scaleFactor;

        // 限制缩放范围（最小1.0倍，最大为用户设置值）
        if (newScale < 1.0f) {
            scaleFactor = 1.0f / mScaleInfo.currentScale;
            newScale = 1.0f;
        } else if (newScale > mScaleInfo.maxScale) {
            scaleFactor = mScaleInfo.maxScale / mScaleInfo.currentScale;
            newScale = mScaleInfo.maxScale;
        }

        // 使用TransformUtils进行缩放（支持ROI同步）
        if (mTpRoiView != null) {
            TpViewTransform.applyZoom(this, mTpRoiView, scaleFactor, focusX, focusY);
        } else {
            TpViewTransform.applyZoom(this, scaleFactor, focusX, focusY);
        }

        // 更新当前缩放比例
        mScaleInfo.currentScale = getCurrentScale();

        // 通知监听器
        if (mZoomChangeListener != null) {
            mZoomChangeListener.onZoomChanged(mScaleInfo.currentScale, focusX, focusY);
        }

        Log.d(TAG, "缩放: " + mScaleInfo.currentScale + ", 焦点: (" + focusX + ", " + focusY + ")");
        return true;
    }

    /**
     * 执行平移操作（使用TransformUtils的硬边界限制）
     */
    private boolean performTranslate(float deltaX, float deltaY) {
        if (!mPanEnabled) return false;

        // 使用TransformUtils进行平移（支持ROI同步）
        if (mTpRoiView != null) {
            TpViewTransform.applyPan(this, mTpRoiView, deltaX, deltaY);
        } else {
            TpViewTransform.applyPan(this, deltaX, deltaY);
        }

        Log.d(TAG, "平移: deltaX=" + deltaX + ", deltaY=" + deltaY);
        return true;
    }

    /**
     * 获取当前缩放比例
     */
    public float getCurrentScale() {
        Matrix matrix = getTransform(null);
        if (matrix != null) {
            float[] values = new float[9];
            matrix.getValues(values);
            return values[Matrix.MSCALE_X];
        }
        return 1.0f;
    }

    /**
     * 手势状态重置相关方法
     */
    private void scheduleResetGestureState() {
        cancelResetGestureState();
        mResetGestureStateRunnable = () -> {
            if (mGestureState == GestureState.SCALING) {
                mGestureState = GestureState.IDLE;
                Log.d(TAG, "手势状态重置为IDLE");
            }
        };
        mHandler.postDelayed(mResetGestureStateRunnable, 200);
    }

    private void cancelResetGestureState() {
        if (mResetGestureStateRunnable != null) {
            mHandler.removeCallbacks(mResetGestureStateRunnable);
            mResetGestureStateRunnable = null;
        }
    }

    // ==================== 公共API方法 ====================

    /**
     * 设置最大缩放倍数（推荐API）
     * <p>
     * 最小缩放固定为适应屏幕大小，最大缩放由用户指定。
     * 这种设计符合专业相机应用的实际使用需求。
     * </p>
     *
     * @param maxScale 最大缩放倍数，必须大于1.0
     */
    public void setMaxScale(float maxScale) {
        if (maxScale > 1.0f) {
            mScaleInfo.setUserMaxScale(maxScale);
            Log.d(TAG, "最大缩放设置为: " + maxScale + " (最小缩放固定为适应屏幕)");
        }
    }



    /**
     * 设置是否启用缩放功能
     */
    public void setZoomEnabled(boolean enabled) {
        this.mZoomEnabled = enabled;
        Log.d(TAG, "缩放功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置是否启用平移功能
     */
    public void setPanEnabled(boolean enabled) {
        this.mPanEnabled = enabled;
        Log.d(TAG, "平移功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置是否启用双击缩放功能
     */
    public void setDoubleTapEnabled(boolean enabled) {
        this.mDoubleTapEnabled = enabled;
        Log.d(TAG, "双击缩放功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置ROI视图（用于同步变换）
     */
    public void setRoiView(TpRoiView tpRoiView) {
        this.mTpRoiView = tpRoiView;
        Log.d(TAG, "ROI视图已" + (tpRoiView != null ? "设置" : "移除"));
    }

    /**
     * 设置缩放变化监听器
     */
    public void setOnZoomChangeListener(OnZoomChangeListener listener) {
        this.mZoomChangeListener = listener;
    }
    
    /**
     * 设置触摸事件处理策略
     * @param handler 触摸事件处理器，null表示使用默认行为
     */
    public void setTouchEventHandler(TouchEventHandler handler) {
        this.mTouchEventHandler = handler;
        Log.d(TAG, "触摸事件处理器已" + (handler != null ? "设置" : "移除"));
    }
    
    /**
     * 获取当前的触摸事件处理器
     * @return 当前的触摸事件处理器
     */
    public TouchEventHandler getTouchEventHandler() {
        return mTouchEventHandler;
    }

    /**
     * 获取是否启用缩放功能
     */
    public boolean isZoomEnabled() {
        return mZoomEnabled;
    }

    /**
     * 获取是否启用平移功能
     */
    public boolean isPanEnabled() {
        return mPanEnabled;
    }

    /**
     * 获取是否启用双击缩放功能
     */
    public boolean isDoubleTapEnabled() {
        return mDoubleTapEnabled;
    }

    /**
     * 获取当前设置的最大缩放倍数
     *
     * @return 最大缩放倍数
     */
    public float getMaxScale() {
        return mScaleInfo.maxScale;
    }

}

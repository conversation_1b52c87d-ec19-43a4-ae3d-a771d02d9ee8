<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_image_compare" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\activity_image_compare.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_image_compare_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="14"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="45"/></Target><Target id="@+id/btn_sync" view="ImageButton"><Expressions/><location startLine="35" startOffset="8" endLine="41" endOffset="47"/></Target><Target id="@+id/btn_reset" view="ImageButton"><Expressions/><location startLine="43" startOffset="8" endLine="49" endOffset="45"/></Target><Target id="@+id/btn_swap" view="ImageButton"><Expressions/><location startLine="51" startOffset="8" endLine="57" endOffset="47"/></Target><Target id="@+id/compare_container" view="LinearLayout"><Expressions/><location startLine="62" startOffset="4" endLine="88" endOffset="18"/></Target><Target id="@+id/left_image" view="com.touptek.ui.TpImageView"><Expressions/><location startLine="69" startOffset="8" endLine="74" endOffset="42"/></Target><Target id="@+id/right_image" view="com.touptek.ui.TpImageView"><Expressions/><location startLine="81" startOffset="8" endLine="86" endOffset="42"/></Target><Target id="@+id/tv_left_info" view="TextView"><Expressions/><location startLine="99" startOffset="8" endLine="109" endOffset="40"/></Target><Target id="@+id/tv_right_info" view="TextView"><Expressions/><location startLine="116" startOffset="8" endLine="126" endOffset="40"/></Target></Targets></Layout>
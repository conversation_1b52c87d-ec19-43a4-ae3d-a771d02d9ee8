#Tue Jul 29 16:11:48 CST 2025
com.touptek.xcamview.app-main-5\:/color/red.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\red.xml
com.touptek.xcamview.app-main-5\:/color/tab_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\tab_text_color.xml
com.touptek.xcamview.app-main-5\:/drawable-v24/ic_launcher_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-v24\\ic_launcher_foreground.xml
com.touptek.xcamview.app-main-5\:/drawable/about_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\about_d.png
com.touptek.xcamview.app-main-5\:/drawable/about_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\about_n.png
com.touptek.xcamview.app-main-5\:/drawable/add_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\add_n.png
com.touptek.xcamview.app-main-5\:/drawable/bg_rounded_dialog_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_rounded_dialog_light.xml
com.touptek.xcamview.app-main-5\:/drawable/border_box.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\border_box.xml
com.touptek.xcamview.app-main-5\:/drawable/bottom_panel_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bottom_panel_background.xml
com.touptek.xcamview.app-main-5\:/drawable/brow_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\brow_d.png
com.touptek.xcamview.app-main-5\:/drawable/browser_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\browser_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_about_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_about_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_about_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_about_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_color_adjustment_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_color_adjustment_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_color_adjustment_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_color_adjustment_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_confirm_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_confirm_bg.xml
com.touptek.xcamview.app-main-5\:/drawable/btn_draw_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_draw_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_draw_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_draw_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_exposure_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_exposure_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_exposure_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_exposure_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_flip_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_flip_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_flip_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_flip_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_folder_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_folder_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_folder_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_folder_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_image_processing_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_image_processing_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_image_processing_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_image_processing_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_menu_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_menu_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_menu_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_menu_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_pause_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_pause_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_pause_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_pause_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_power_frequency_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_power_frequency_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_power_frequency_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_power_frequency_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_record_video_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_record_video_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_record_video_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_record_video_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_rounded_default.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_rounded_default.xml
com.touptek.xcamview.app-main-5\:/drawable/btn_settings_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_settings_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_settings_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_settings_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_take_photo_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_take_photo_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_take_photo_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_take_photo_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_white_balance_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_white_balance_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_white_balance_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_white_balance_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_zoom_in_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_zoom_in_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_zoom_in_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_zoom_in_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/btn_zoom_out_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_zoom_out_n.png
com.touptek.xcamview.app-main-5\:/drawable/btn_zoom_out_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_zoom_out_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_background.xml
com.touptek.xcamview.app-main-5\:/drawable/button_border_selected.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_border_selected.xml
com.touptek.xcamview.app-main-5\:/drawable/config_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\config_d.png
com.touptek.xcamview.app-main-5\:/drawable/config_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\config_n.png
com.touptek.xcamview.app-main-5\:/drawable/delete_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\delete_n.png
com.touptek.xcamview.app-main-5\:/drawable/dialog_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
com.touptek.xcamview.app-main-5\:/drawable/dialog_border.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_border.xml
com.touptek.xcamview.app-main-5\:/drawable/divider.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\divider.xml
com.touptek.xcamview.app-main-5\:/drawable/divider_vertical_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\divider_vertical_light.xml
com.touptek.xcamview.app-main-5\:/drawable/exposure_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\exposure_n.png
com.touptek.xcamview.app-main-5\:/drawable/flip_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\flip_n.png
com.touptek.xcamview.app-main-5\:/drawable/floating_nav_button_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_nav_button_bg.xml
com.touptek.xcamview.app-main-5\:/drawable/floating_nav_button_bg_enhanced.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_nav_button_bg_enhanced.xml
com.touptek.xcamview.app-main-5\:/drawable/floating_panel_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_panel_bg.xml
com.touptek.xcamview.app-main-5\:/drawable/freeze_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\freeze_d.png
com.touptek.xcamview.app-main-5\:/drawable/freeze_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\freeze_n.png
com.touptek.xcamview.app-main-5\:/drawable/grey_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\grey_background.xml
com.touptek.xcamview.app-main-5\:/drawable/groupbox_border.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\groupbox_border.xml
com.touptek.xcamview.app-main-5\:/drawable/groupbox_title_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\groupbox_title_background.xml
com.touptek.xcamview.app-main-5\:/drawable/home_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\home_n.png
com.touptek.xcamview.app-main-5\:/drawable/hz_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\hz_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_about.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_about.png
com.touptek.xcamview.app-main-5\:/drawable/ic_action1.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_action1.png
com.touptek.xcamview.app-main-5\:/drawable/ic_action2.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_action2.png
com.touptek.xcamview.app-main-5\:/drawable/ic_action3.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_action3.png
com.touptek.xcamview.app-main-5\:/drawable/ic_action4.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_action4.png
com.touptek.xcamview.app-main-5\:/drawable/ic_action5.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_action5.png
com.touptek.xcamview.app-main-5\:/drawable/ic_allselect.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_allselect.png
com.touptek.xcamview.app-main-5\:/drawable/ic_angle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_angle_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_annotation_arrow_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_annotation_arrow_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_annulus2_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_annulus2_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_annulus_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_annulus_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_arbline_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arbline_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_arc_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arc_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_arrow_back.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_arrowleft.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrowleft.png
com.touptek.xcamview.app-main-5\:/drawable/ic_arrowright_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrowright_d.png
com.touptek.xcamview.app-main-5\:/drawable/ic_calibration_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_calibration_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_cancel.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_cancel.png
com.touptek.xcamview.app-main-5\:/drawable/ic_cancel_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_cancel_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_centerc_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_centerc_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_checked.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_close.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.png
com.touptek.xcamview.app-main-5\:/drawable/ic_close_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_color_adjust.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_color_adjust.png
com.touptek.xcamview.app-main-5\:/drawable/ic_color_adjustment.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_color_adjustment.png
com.touptek.xcamview.app-main-5\:/drawable/ic_compare.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_compare.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_config_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_config_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_copy.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_copy.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_cut.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_cut.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_delete_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_details.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_details.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_draw.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_draw.png
com.touptek.xcamview.app-main-5\:/drawable/ic_ellipse_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_ellipse_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_export_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_export_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_exposure.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_exposure.png
com.touptek.xcamview.app-main-5\:/drawable/ic_fiveellipse_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fiveellipse_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_flip.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_flip.png
com.touptek.xcamview.app-main-5\:/drawable/ic_fold.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fold.png
com.touptek.xcamview.app-main-5\:/drawable/ic_fold_pressed.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fold_pressed.png
com.touptek.xcamview.app-main-5\:/drawable/ic_folder.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_folder.png
com.touptek.xcamview.app-main-5\:/drawable/ic_fourptangle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fourptangle_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_hline_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_hline_d.png
com.touptek.xcamview.app-main-5\:/drawable/ic_hline_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_hline_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_image_processing.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_image_processing.png
com.touptek.xcamview.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_lock_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_lock_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_menu.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_menu.png
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_back.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_back.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_back_classic.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_back_classic.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_back_curved.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_back_curved.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_back_large.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_back_large.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_back_option2.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_back_option2.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_back_option3.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_back_option3.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_back_shadow.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_back_shadow.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_back_simple.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_back_simple.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_next.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_next.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_next_large.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_next_large.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_next_shadow.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_next_shadow.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_previous.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_previous.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_previous_large.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_previous_large.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_nav_previous_shadow.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_nav_previous_shadow.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_parallel_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_parallel_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_paste.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_paste.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_pause.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_pause.png
com.touptek.xcamview.app-main-5\:/drawable/ic_pic.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_pic.png
com.touptek.xcamview.app-main-5\:/drawable/ic_picture.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_picture.png
com.touptek.xcamview.app-main-5\:/drawable/ic_point_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_point_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_polygon_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_polygon_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_power_frequency.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_power_frequency.png
com.touptek.xcamview.app-main-5\:/drawable/ic_preview.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_preview.png
com.touptek.xcamview.app-main-5\:/drawable/ic_randomcurve_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_randomcurve_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_record_start.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_record_start.png
com.touptek.xcamview.app-main-5\:/drawable/ic_record_video.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_record_video.png
com.touptek.xcamview.app-main-5\:/drawable/ic_rectangle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_rectangle_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_refresh.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_refresh.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_return.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_return.png
com.touptek.xcamview.app-main-5\:/drawable/ic_scale_bar_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_scale_bar_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_select_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_select_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_settings.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.png
com.touptek.xcamview.app-main-5\:/drawable/ic_storage.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_storage.png
com.touptek.xcamview.app-main-5\:/drawable/ic_swap_horiz.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_swap_horiz.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_take_photo.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_take_photo.png
com.touptek.xcamview.app-main-5\:/drawable/ic_text_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_text_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_threecircle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_threecircle_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_threeline_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_threeline_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_threepttwowircles_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_threepttwowircles_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_threerectangle_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_threerectangle_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_threevertical_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_threevertical_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_unchecked.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_video.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_video.png
com.touptek.xcamview.app-main-5\:/drawable/ic_video_triangle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_video_triangle.xml
com.touptek.xcamview.app-main-5\:/drawable/ic_vline_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_vline_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_wb_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_wb_n.png
com.touptek.xcamview.app-main-5\:/drawable/ic_white.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_white.png
com.touptek.xcamview.app-main-5\:/drawable/ic_white_balance.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_white_balance.png
com.touptek.xcamview.app-main-5\:/drawable/ic_zoom_in.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_zoom_in.png
com.touptek.xcamview.app-main-5\:/drawable/ic_zoom_out.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_zoom_out.png
com.touptek.xcamview.app-main-5\:/drawable/image_border_normal.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\image_border_normal.xml
com.touptek.xcamview.app-main-5\:/drawable/imageprocess_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\imageprocess_n.png
com.touptek.xcamview.app-main-5\:/drawable/isp_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\isp_n.png
com.touptek.xcamview.app-main-5\:/drawable/measure_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\measure_n.png
com.touptek.xcamview.app-main-5\:/drawable/nav_separator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\nav_separator.xml
com.touptek.xcamview.app-main-5\:/drawable/next_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\next_n.png
com.touptek.xcamview.app-main-5\:/drawable/oval_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\oval_button_background.xml
com.touptek.xcamview.app-main-5\:/drawable/popup_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\popup_background.xml
com.touptek.xcamview.app-main-5\:/drawable/record_start_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\record_start_n.png
com.touptek.xcamview.app-main-5\:/drawable/rounded_border.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_border.xml
com.touptek.xcamview.app-main-5\:/drawable/rounded_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_button_background.xml
com.touptek.xcamview.app-main-5\:/drawable/scenechange_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\scenechange_d.png
com.touptek.xcamview.app-main-5\:/drawable/scenechange_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\scenechange_n.png
com.touptek.xcamview.app-main-5\:/drawable/selector_settings_tab.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\selector_settings_tab.xml
com.touptek.xcamview.app-main-5\:/drawable/selector_tab_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\selector_tab_background.xml
com.touptek.xcamview.app-main-5\:/drawable/snap_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\snap_n.png
com.touptek.xcamview.app-main-5\:/drawable/status_banner_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\status_banner_bg.xml
com.touptek.xcamview.app-main-5\:/drawable/stepframe_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\stepframe_n.png
com.touptek.xcamview.app-main-5\:/drawable/sub_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\sub_n.png
com.touptek.xcamview.app-main-5\:/drawable/tab_selected_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_selected_bg.xml
com.touptek.xcamview.app-main-5\:/drawable/thumb_blue.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\thumb_blue.xml
com.touptek.xcamview.app-main-5\:/drawable/thumb_blue_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\thumb_blue_selector.xml
com.touptek.xcamview.app-main-5\:/drawable/title_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\title_background.xml
com.touptek.xcamview.app-main-5\:/drawable/tp_custom_enabled_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_custom_enabled_selector.xml
com.touptek.xcamview.app-main-5\:/drawable/tp_custom_radionbutton.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_custom_radionbutton.xml
com.touptek.xcamview.app-main-5\:/drawable/tp_custom_seekbar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_custom_seekbar.xml
com.touptek.xcamview.app-main-5\:/drawable/tp_custom_seekbar_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_custom_seekbar_thumb.xml
com.touptek.xcamview.app-main-5\:/drawable/tp_switch_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_switch_thumb.xml
com.touptek.xcamview.app-main-5\:/drawable/tp_switch_track.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tp_switch_track.xml
com.touptek.xcamview.app-main-5\:/drawable/track_blue_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\track_blue_selector.xml
com.touptek.xcamview.app-main-5\:/drawable/zoomin_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\zoomin_d.png
com.touptek.xcamview.app-main-5\:/drawable/zoomin_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\zoomin_n.png
com.touptek.xcamview.app-main-5\:/drawable/zoomout_d.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\zoomout_d.png
com.touptek.xcamview.app-main-5\:/drawable/zoomout_n.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\zoomout_n.png
com.touptek.xcamview.app-main-5\:/font/kai.ttf=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\kai.ttf
com.touptek.xcamview.app-main-5\:/font/song.ttf=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\song.ttf
com.touptek.xcamview.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.touptek.xcamview.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.touptek.xcamview.app-main-5\:/mipmap-hdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.png
com.touptek.xcamview.app-main-5\:/mipmap-hdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.png
com.touptek.xcamview.app-main-5\:/mipmap-mdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.png
com.touptek.xcamview.app-main-5\:/mipmap-mdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.png
com.touptek.xcamview.app-main-5\:/mipmap-xhdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.png
com.touptek.xcamview.app-main-5\:/mipmap-xhdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.png
com.touptek.xcamview.app-main-5\:/mipmap-xxhdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.png
com.touptek.xcamview.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.png
com.touptek.xcamview.app-main-5\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.png
com.touptek.xcamview.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.png=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.png
com.touptek.xcamview.app-main-5\:/xml/backup_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.touptek.xcamview.app-main-5\:/xml/data_extraction_rules.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/activity_image_compare.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_image_compare.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/activity_main.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/activity_touptek.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_touptek.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/activity_touptek_btn.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_touptek_btn.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/activity_welcome.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_welcome.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/autoae_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\autoae_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/browse_grid_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\browse_grid_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/browse_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\browse_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/copydialog_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\copydialog_settings.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/dialog_file_details.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_file_details.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/dialog_modern_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_modern_settings.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/dialog_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_settings.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/flip_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\flip_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/folder_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\folder_item.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/fragment_format_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_format_settings.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/fragment_measurement_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_measurement_settings.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/fragment_network_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_network_settings.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/fragment_storage_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_storage_settings.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/hz_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\hz_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/image_parameter_2_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\image_parameter_2_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/image_parameter_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\image_parameter_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/image_viewer.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\image_viewer.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/layout_input_info_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_input_info_item.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/measurement_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\measurement_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/operation_grid_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\operation_grid_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/popup_config_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\popup_config_menu.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/popup_menu_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\popup_menu_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/right_panel_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\right_panel_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/scene_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\scene_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/settings_misc.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\settings_misc.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/settings_record.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\settings_record.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/testdialog_settings.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\testdialog_settings.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/video_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\video_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/videodecode_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\videodecode_layout.xml
com.touptek.xcamview.app-packageDebugResources-2\:/layout/whitebalance_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\whitebalance_layout.xml

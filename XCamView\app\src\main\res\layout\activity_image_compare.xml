<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#000000">
    
    <!-- 工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#000000"
        android:elevation="4dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:text="图片对比"
            android:textSize="18sp"
            android:textColor="#FFFFFF"
            android:gravity="center"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/btn_reset"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_refresh"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="重置" />

        <ImageButton
            android:id="@+id/btn_swap"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_swap_horiz"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="交换位置" />

    </LinearLayout>
    
    <!-- 图片对比区域 -->
    <LinearLayout
        android:id="@+id/compare_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">
        
        <com.touptek.ui.TpImageView
            android:id="@+id/left_image"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="#000000" />

        <View
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:background="#CCCCCC" />

        <com.touptek.ui.TpImageView
            android:id="@+id/right_image"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="#000000" />
            
    </LinearLayout>
    
    <!-- 底部状态栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#F0F0F0"
        android:gravity="center">
        
        <TextView
            android:id="@+id/tv_left_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="图片1"
            android:textSize="12sp"
            android:textColor="#666666"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="middle" />
            
        <View
            android:layout_width="1dp"
            android:layout_height="20dp"
            android:background="#CCCCCC" />
            
        <TextView
            android:id="@+id/tv_right_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="图片2"
            android:textSize="12sp"
            android:textColor="#666666"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="middle" />
            
    </LinearLayout>
    
</LinearLayout>

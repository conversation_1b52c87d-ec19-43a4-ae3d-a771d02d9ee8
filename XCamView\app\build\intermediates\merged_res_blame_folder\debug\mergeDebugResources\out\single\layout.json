[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\copydialog_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\copydialog_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\fragment_storage_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\fragment_storage_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\activity_welcome.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_welcome.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\dialog_file_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\dialog_file_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\right_panel_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\right_panel_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\fragment_network_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\fragment_network_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\activity_touptek.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_touptek.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\folder_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\folder_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\activity_touptek_btn.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_touptek_btn.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\browse_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\browse_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\activity_image_compare.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_image_compare.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\settings_record.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\settings_record.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\popup_menu_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\popup_menu_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\browse_grid_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\browse_grid_layout.xml"}, {"merged": "com.touptek.xcamview.app-mergeDebugResources-40:/layout/activity_image_compare_multi.xml", "source": "com.touptek.xcamview.app-main-43:/layout/activity_image_compare_multi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\whitebalance_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\whitebalance_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\hz_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\hz_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\dialog_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\dialog_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\scene_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\scene_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\fragment_format_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\fragment_format_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\dialog_modern_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\dialog_modern_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\testdialog_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\testdialog_settings.xml"}, {"merged": "com.touptek.xcamview.app-mergeDebugResources-40:/layout/popup_config_menu.xml", "source": "com.touptek.xcamview.app-main-43:/layout/popup_config_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\measurement_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\measurement_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\fragment_measurement_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\fragment_measurement_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\image_parameter_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\image_parameter_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\flip_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\flip_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\layout_input_info_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\layout_input_info_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\video_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\video_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\image_parameter_2_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\image_parameter_2_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\videodecode_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\videodecode_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\autoae_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\autoae_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\image_viewer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\image_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\operation_grid_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\operation_grid_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\settings_misc.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\settings_misc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-mergeDebugResources-40:\\layout\\popup_config_menu.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\popup_config_menu.xml"}]
package com.touptek.test.isp;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;


//import com.touptek.R;
import com.android.rockchip.mediacodecnew.R;
import com.touptek.video.TpIspParam;

import java.util.List;

/**
 * TpTestDialog - ISP场景管理测试对话框
 * <p>
 * 提供简洁的弹窗界面来测试ISP场景管理功能，包括场景的保存、加载、
 * 列表查看和完整测试流程。采用异步执行避免阻塞UI线程。
 * </p>
 * 
 */
public class TpTestDialog extends DialogFragment {
    private static final String TAG = "TpTestDialog";
    
    // ===== UI组件 =====

    private Button btnSaveScene;
    private Button btnLoadScene;
    private Button btnDeleteScene;

    // 系统场景按钮
    private Button btnBiologicalScene;
    private Button btnStereoscopicScene;

    private ProgressBar progressBar;

    // ISP控件直接引用
    private CheckBox checkboxAutoExposure;
    private RadioGroup radioGroupWb;

    // 曝光控制
    private SeekBar seekbarExposureCompensation, seekbarExposureTime, seekbarExposureGain;
    private TextView tvExposureCompensationValue, tvExposureTimeValue, tvExposureGainValue;

    // 白平衡控制
    private SeekBar seekbarWbRed, seekbarWbGreen, seekbarWbBlue;
    private TextView tvWbRedValue, tvWbGreenValue, tvWbBlueValue;

    // 图像处理
    private SeekBar seekbarBrightness, seekbarContrast, seekbarSaturation, seekbarHue, seekbarGamma, seekbarSharpness;
    private TextView tvBrightnessValue, tvContrastValue, tvSaturationValue, tvHueValue, tvGammaValue, tvSharpnessValue;

    // 降噪增强
    private SeekBar seekbarDenoise, seekbarDarkEnhance, seekbarWdr, seekbarLdc;
    private TextView tvDenoiseValue, tvDarkEnhanceValue, tvWdrValue, tvLdcValue;

    // 色彩校正
    private SeekBar seekbarColorMode, seekbarColorTone;
    private TextView tvColorModeValue, tvColorToneValue;

    // 色温控制
    private SeekBar seekbarCtRed, seekbarCtGreen, seekbarCtBlue;
    private TextView tvCtRedValue, tvCtGreenValue, tvCtBlueValue;

    // 其他设置
    private SeekBar seekbarMirror, seekbarFlip, seekbarHz, seekbarBandwidth;
    private TextView tvMirrorValue, tvFlipValue, tvHzValue, tvBandwidthValue;

    // ===== 测试状态 =====

    private boolean isTestRunning = false;
    private Handler mainHandler;
    
    // ===== 静态工厂方法 =====
    
    /**
     * 创建并显示测试对话框
     *
     * @param context 上下文，必须是FragmentActivity
     */
    public static void show(Context context) {
        if (context instanceof FragmentActivity) {
            FragmentActivity activity = (FragmentActivity) context;

            TpTestDialog dialog = new TpTestDialog();
            dialog.show(activity.getSupportFragmentManager(), "TpTestDialog");
        } else {
            throw new IllegalArgumentException("Context必须是FragmentActivity的实例");
        }
    }

    
    // ===== 生命周期方法 =====
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mainHandler = new Handler(Looper.getMainLooper());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
    }
    
    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
        
        // 加载自定义布局
        LayoutInflater inflater = requireActivity().getLayoutInflater();
        View view = inflater.inflate(R.layout.dialog_tp_test, null);
        
        // 初始化UI组件
        initViews(view);
        setupListeners();
        
        builder.setView(view)
               .setTitle("ToupTek SDK 测试功能");
        
        return builder.create();
    }
    
    @Override
    public void onStart() {
        super.onStart();
        // 设置对话框大小
        if (getDialog() != null && getDialog().getWindow() != null) {
            int width = (int) (getResources().getDisplayMetrics().widthPixels * 0.9);
            int height = (int) (getResources().getDisplayMetrics().heightPixels * 0.75);
            getDialog().getWindow().setLayout(width, height);
        }
    }
    
    // ===== UI初始化方法 =====
    
    /**
     * 初始化UI组件
     */
    private void initViews(View view) {
        // 场景按钮
        btnSaveScene = view.findViewById(R.id.btn_save_scene);
        btnLoadScene = view.findViewById(R.id.btn_load_scene);
        btnDeleteScene = view.findViewById(R.id.btn_delete_scene);
        btnBiologicalScene = view.findViewById(R.id.btn_biological_scene);
        btnStereoscopicScene = view.findViewById(R.id.btn_stereoscopic_scene);
        progressBar = view.findViewById(R.id.progress_bar);

        // ISP控件
        checkboxAutoExposure = view.findViewById(R.id.checkbox_auto_exposure);
        radioGroupWb = view.findViewById(R.id.radio_group_wb);

        // 曝光控制
        seekbarExposureCompensation = view.findViewById(R.id.seekbar_exposure_compensation);
        tvExposureCompensationValue = view.findViewById(R.id.tv_exposure_compensation_value);
        seekbarExposureTime = view.findViewById(R.id.seekbar_exposure_time);
        tvExposureTimeValue = view.findViewById(R.id.tv_exposure_time_value);
        seekbarExposureGain = view.findViewById(R.id.seekbar_exposure_gain);
        tvExposureGainValue = view.findViewById(R.id.tv_exposure_gain_value);

        // 白平衡控制
        seekbarWbRed = view.findViewById(R.id.seekbar_wb_red);
        tvWbRedValue = view.findViewById(R.id.tv_wb_red_value);
        seekbarWbGreen = view.findViewById(R.id.seekbar_wb_green);
        tvWbGreenValue = view.findViewById(R.id.tv_wb_green_value);
        seekbarWbBlue = view.findViewById(R.id.seekbar_wb_blue);
        tvWbBlueValue = view.findViewById(R.id.tv_wb_blue_value);

        // 图像处理
        seekbarBrightness = view.findViewById(R.id.seekbar_brightness);
        tvBrightnessValue = view.findViewById(R.id.tv_brightness_value);
        seekbarContrast = view.findViewById(R.id.seekbar_contrast);
        tvContrastValue = view.findViewById(R.id.tv_contrast_value);
        seekbarSaturation = view.findViewById(R.id.seekbar_saturation);
        tvSaturationValue = view.findViewById(R.id.tv_saturation_value);
        seekbarHue = view.findViewById(R.id.seekbar_hue);
        tvHueValue = view.findViewById(R.id.tv_hue_value);
        seekbarGamma = view.findViewById(R.id.seekbar_gamma);
        tvGammaValue = view.findViewById(R.id.tv_gamma_value);
        seekbarSharpness = view.findViewById(R.id.seekbar_sharpness);
        tvSharpnessValue = view.findViewById(R.id.tv_sharpness_value);

        // 降噪增强
        seekbarDenoise = view.findViewById(R.id.seekbar_denoise);
        tvDenoiseValue = view.findViewById(R.id.tv_denoise_value);
        seekbarDarkEnhance = view.findViewById(R.id.seekbar_dark_enhance);
        tvDarkEnhanceValue = view.findViewById(R.id.tv_dark_enhance_value);
        seekbarWdr = view.findViewById(R.id.seekbar_wdr);
        tvWdrValue = view.findViewById(R.id.tv_wdr_value);
        seekbarLdc = view.findViewById(R.id.seekbar_ldc);
        tvLdcValue = view.findViewById(R.id.tv_ldc_value);

        // 色彩校正
        seekbarColorMode = view.findViewById(R.id.seekbar_color_mode);
        tvColorModeValue = view.findViewById(R.id.tv_color_mode_value);
        seekbarColorTone = view.findViewById(R.id.seekbar_color_tone);
        tvColorToneValue = view.findViewById(R.id.tv_color_tone_value);

        // 色温控制
        seekbarCtRed = view.findViewById(R.id.seekbar_ct_red);
        tvCtRedValue = view.findViewById(R.id.tv_ct_red_value);
        seekbarCtGreen = view.findViewById(R.id.seekbar_ct_green);
        tvCtGreenValue = view.findViewById(R.id.tv_ct_green_value);
        seekbarCtBlue = view.findViewById(R.id.seekbar_ct_blue);
        tvCtBlueValue = view.findViewById(R.id.tv_ct_blue_value);

        // 其他设置
        seekbarMirror = view.findViewById(R.id.seekbar_mirror);
        tvMirrorValue = view.findViewById(R.id.tv_mirror_value);
        seekbarFlip = view.findViewById(R.id.seekbar_flip);
        tvFlipValue = view.findViewById(R.id.tv_flip_value);
        seekbarHz = view.findViewById(R.id.seekbar_hz);
        tvHzValue = view.findViewById(R.id.tv_hz_value);
        seekbarBandwidth = view.findViewById(R.id.seekbar_bandwidth);
        tvBandwidthValue = view.findViewById(R.id.tv_bandwidth_value);

        // 初始状态设置
        progressBar.setVisibility(View.GONE);
    }
    
    /**
     * 设置按钮监听器
     */
    private void setupListeners() {
        // 场景按钮
        btnSaveScene.setOnClickListener(v -> showSaveSceneDialog());
        btnLoadScene.setOnClickListener(v -> showLoadSceneDialog());
        btnDeleteScene.setOnClickListener(v -> showDeleteSceneDialog());
        btnBiologicalScene.setOnClickListener(v -> applyScene("生物"));
        btnStereoscopicScene.setOnClickListener(v -> applyScene("体视"));

        // 曝光控制
        checkboxAutoExposure.setOnCheckedChangeListener((buttonView, isChecked) -> {
            TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE, isChecked ? 1 : 0);
            updateExposureControlStates(isChecked);
        });

        // 白平衡控制
        radioGroupWb.setOnCheckedChangeListener((group, checkedId) -> {
            int mode = 0;
            if (checkedId == R.id.radio_wb_auto) {
                mode = 1;
            } else if (checkedId == R.id.radio_wb_roi) {
                mode = 2;
            }
            TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_WBCHOICE, mode);
            updateWhiteBalanceControlStates(mode);
        });

        // ISP参数SeekBar
        setupSeekBar(seekbarExposureCompensation, tvExposureCompensationValue, TpIspParam.TOUPTEK_PARAM_EXPOSURECOMPENSATION);
        setupSeekBar(seekbarExposureTime, tvExposureTimeValue, TpIspParam.TOUPTEK_PARAM_EXPOSURETIME);
        setupSeekBar(seekbarExposureGain, tvExposureGainValue, TpIspParam.TOUPTEK_PARAM_EXPOSUREGAIN);
        setupSeekBar(seekbarWbRed, tvWbRedValue, TpIspParam.TOUPTEK_PARAM_WBREDGAIN);
        setupSeekBar(seekbarWbGreen, tvWbGreenValue, TpIspParam.TOUPTEK_PARAM_WBGREENGAIN);
        setupSeekBar(seekbarWbBlue, tvWbBlueValue, TpIspParam.TOUPTEK_PARAM_WBBLUEGAIN);
        setupSeekBar(seekbarBrightness, tvBrightnessValue, TpIspParam.TOUPTEK_PARAM_BRIGHTNESS);
        setupSeekBar(seekbarContrast, tvContrastValue, TpIspParam.TOUPTEK_PARAM_CONTRAST);
        setupSeekBar(seekbarSaturation, tvSaturationValue, TpIspParam.TOUPTEK_PARAM_SATURATION);
        setupSeekBar(seekbarHue, tvHueValue, TpIspParam.TOUPTEK_PARAM_HUE);
        setupSeekBar(seekbarGamma, tvGammaValue, TpIspParam.TOUPTEK_PARAM_GAMMA);
        setupSeekBar(seekbarSharpness, tvSharpnessValue, TpIspParam.TOUPTEK_PARAM_SHARPNESS);
        setupSeekBar(seekbarDenoise, tvDenoiseValue, TpIspParam.TOUPTEK_PARAM_DENOISE);
        setupSeekBar(seekbarDarkEnhance, tvDarkEnhanceValue, TpIspParam.TOUPTEK_PARAM_DARKENHANCE);
        setupSeekBar(seekbarWdr, tvWdrValue, TpIspParam.TOUPTEK_PARAM_WDREXPRATIO);
        setupSeekBar(seekbarLdc, tvLdcValue, TpIspParam.TOUPTEK_PARAM_LDCRATIO);
        setupSeekBar(seekbarColorMode, tvColorModeValue, TpIspParam.TOUPTEK_PARAM_COLORORGRAY);
        setupSeekBar(seekbarColorTone, tvColorToneValue, TpIspParam.TOUPTEK_PARAM_COLORTONE);
        setupSeekBar(seekbarCtRed, tvCtRedValue, TpIspParam.TOUPTEK_PARAM_CTREDGAIN);
        setupSeekBar(seekbarCtGreen, tvCtGreenValue, TpIspParam.TOUPTEK_PARAM_CTGREENGAIN);
        setupSeekBar(seekbarCtBlue, tvCtBlueValue, TpIspParam.TOUPTEK_PARAM_CTBLUEGAIN);
        setupSeekBar(seekbarMirror, tvMirrorValue, TpIspParam.TOUPTEK_PARAM_MIRROR);
        setupSeekBar(seekbarFlip, tvFlipValue, TpIspParam.TOUPTEK_PARAM_FLIP);
        setupSeekBar(seekbarHz, tvHzValue, TpIspParam.TOUPTEK_PARAM_HZ);
        setupSeekBar(seekbarBandwidth, tvBandwidthValue, TpIspParam.TOUPTEK_PARAM_BANDWIDTH);

        // 参数变化监听
        TpIspParam.addOnDataChangedListener(new TpIspParam.OnDataChangedListener() {
            @Override
            public void onDataChanged(TpIspParam param, int newValue) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> updateParameterUI(param, newValue));
                }
            }

            @Override
            public void onLongDataChanged(TpIspParam param, long newValue) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> updateParameterUI(param, (int) newValue));
                }
            }
        });

        // 初始化控件状态
        initControlStates();
    }

    // ===== 工具方法 =====

    /**
     * 统一的SeekBar设置方法
     */
    private void setupSeekBar(SeekBar seekBar, TextView valueView, TpIspParam param) {
        int currentValue = TpIspParam.getCurrentValue(param);
        seekBar.setMin(TpIspParam.getMinValue(param));
        seekBar.setMax(TpIspParam.getMaxValue(param));
        seekBar.setProgress(currentValue);
        valueView.setText(String.valueOf(currentValue));

        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    TpIspParam.updateParam(param, progress);
                    valueView.setText(String.valueOf(progress));
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
    }

    /**
     * 更新参数UI
     */
    private void updateParameterUI(TpIspParam param, int newValue) {
        switch (param) {
            case TOUPTEK_PARAM_EXPOSURECOMPENSATION:
                seekbarExposureCompensation.setProgress(newValue);
                tvExposureCompensationValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_EXPOSURETIME:
                seekbarExposureTime.setProgress(newValue);
                tvExposureTimeValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_EXPOSUREGAIN:
                seekbarExposureGain.setProgress(newValue);
                tvExposureGainValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_WBREDGAIN:
                seekbarWbRed.setProgress(newValue);
                tvWbRedValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_WBGREENGAIN:
                seekbarWbGreen.setProgress(newValue);
                tvWbGreenValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_WBBLUEGAIN:
                seekbarWbBlue.setProgress(newValue);
                tvWbBlueValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_BRIGHTNESS:
                seekbarBrightness.setProgress(newValue);
                tvBrightnessValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_CONTRAST:
                seekbarContrast.setProgress(newValue);
                tvContrastValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_SATURATION:
                seekbarSaturation.setProgress(newValue);
                tvSaturationValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_HUE:
                seekbarHue.setProgress(newValue);
                tvHueValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_GAMMA:
                seekbarGamma.setProgress(newValue);
                tvGammaValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_SHARPNESS:
                seekbarSharpness.setProgress(newValue);
                tvSharpnessValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_DENOISE:
                seekbarDenoise.setProgress(newValue);
                tvDenoiseValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_DARKENHANCE:
                seekbarDarkEnhance.setProgress(newValue);
                tvDarkEnhanceValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_WDREXPRATIO:
                seekbarWdr.setProgress(newValue);
                tvWdrValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_LDCRATIO:
                seekbarLdc.setProgress(newValue);
                tvLdcValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_COLORORGRAY:
                seekbarColorMode.setProgress(newValue);
                tvColorModeValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_COLORTONE:
                seekbarColorTone.setProgress(newValue);
                tvColorToneValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_CTREDGAIN:
                seekbarCtRed.setProgress(newValue);
                tvCtRedValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_CTGREENGAIN:
                seekbarCtGreen.setProgress(newValue);
                tvCtGreenValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_CTBLUEGAIN:
                seekbarCtBlue.setProgress(newValue);
                tvCtBlueValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_MIRROR:
                seekbarMirror.setProgress(newValue);
                tvMirrorValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_FLIP:
                seekbarFlip.setProgress(newValue);
                tvFlipValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_HZ:
                seekbarHz.setProgress(newValue);
                tvHzValue.setText(String.valueOf(newValue));
                break;
            case TOUPTEK_PARAM_BANDWIDTH:
                seekbarBandwidth.setProgress(newValue);
                tvBandwidthValue.setText(String.valueOf(newValue));
                break;
        }
    }

    /**
     * 更新曝光控件状态
     */
    private void updateExposureControlStates(boolean autoMode) {
        setSeekBarEnabled(seekbarExposureCompensation, tvExposureCompensationValue, autoMode);
        setSeekBarEnabled(seekbarExposureTime, tvExposureTimeValue, !autoMode);
        setSeekBarEnabled(seekbarExposureGain, tvExposureGainValue, !autoMode);
    }

    /**
     * 更新白平衡控件状态
     */
    private void updateWhiteBalanceControlStates(int mode) {
        boolean manualMode = (mode == 0);
        setSeekBarEnabled(seekbarWbRed, tvWbRedValue, manualMode);
        setSeekBarEnabled(seekbarWbGreen, tvWbGreenValue, manualMode);
        setSeekBarEnabled(seekbarWbBlue, tvWbBlueValue, manualMode);
    }

    /**
     * 设置SeekBar启用状态
     */
    private void setSeekBarEnabled(SeekBar seekBar, TextView valueView, boolean enabled) {
        seekBar.setEnabled(enabled);
        seekBar.setAlpha(enabled ? 1.0f : 0.5f);
        valueView.setAlpha(enabled ? 1.0f : 0.5f);
    }

    /**
     * 初始化控件状态
     */
    private void initControlStates() {
        // 初始化曝光控件状态
        int exposureMode = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_EXPOSURECHOICE);
        checkboxAutoExposure.setChecked(exposureMode == 1);
        updateExposureControlStates(exposureMode == 1);

        // 初始化白平衡控件状态
        int wbMode = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_WBCHOICE);
        switch (wbMode) {
            case 0:
                radioGroupWb.check(R.id.radio_wb_manual);
                break;
            case 1:
                radioGroupWb.check(R.id.radio_wb_auto);
                break;
            case 2:
                radioGroupWb.check(R.id.radio_wb_roi);
                break;
        }
        updateWhiteBalanceControlStates(wbMode);
    }

    // ===== 测试功能实现 =====
    
    /**
     * 显示保存场景对话框
     */
    private void showSaveSceneDialog() {
        if (isTestRunning) return;

        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
        final EditText input = new EditText(requireContext());
        input.setHint("请输入场景名称");

        builder.setTitle("保存场景")
                .setView(input)
                .setPositiveButton("保存", (dialog, which) -> {
                    String sceneName = input.getText().toString().trim();
                    if (!sceneName.isEmpty()) {
                        saveScene(sceneName);
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 保存场景
     */
    private void saveScene(String sceneName) {
        setTestRunning(true);

        new Thread(() -> {
            int result = TpIspParam.saveCurrentAsScene(sceneName);

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    showToast(result > 0 ? "场景保存成功" : "场景保存失败");
                    setTestRunning(false);
                });
            }
        }).start();
    }


    
    /**
     * 显示加载场景对话框
     */
    private void showLoadSceneDialog() {
        if (isTestRunning) return;

        List<String> sceneNames = TpIspParam.getAllSceneNames();
        if (sceneNames.isEmpty()) {
            showToast("没有可加载的场景");
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
        builder.setTitle("选择场景")
                .setItems(sceneNames.toArray(new String[0]), (dialog, which) -> {
                    String sceneName = sceneNames.get(which);
                    applyScene(sceneName);
                })
                .setNegativeButton("取消", null)
                .show();
    }









    // ===== 辅助方法 =====



    /**
     * 设置测试运行状态
     */
    private void setTestRunning(boolean running) {
        isTestRunning = running;
        btnSaveScene.setEnabled(!running);
        btnLoadScene.setEnabled(!running);
        btnDeleteScene.setEnabled(!running);
        progressBar.setVisibility(running ? View.VISIBLE : View.GONE);
    }



    /**
     * 显示Toast提示
     */
    private void showToast(String message) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show();
    }

    /**
     * 应用场景
     */
    private void applyScene(String sceneName) {
        if (isTestRunning) return;

        setTestRunning(true);

        new Thread(() -> {
            int result = TpIspParam.applyScene(sceneName);

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    showToast(result > 0 ? "场景应用成功" : "场景应用失败");
                    setTestRunning(false);
                });
            }
        }).start();
    }

    /**
     * 显示删除场景对话框
     */
    private void showDeleteSceneDialog() {
        if (isTestRunning) return;

        List<String> sceneNames = TpIspParam.getAllSceneNames();
        if (sceneNames.isEmpty()) {
            showToast("没有可删除的场景");
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
        builder.setTitle("删除场景")
                .setItems(sceneNames.toArray(new String[0]), (dialog, which) -> {
                    String sceneName = sceneNames.get(which);
                    if (TpIspParam.deleteScene(sceneName)) {
                        showToast("场景删除成功");
                    } else {
                        showToast("场景删除失败");
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }
}

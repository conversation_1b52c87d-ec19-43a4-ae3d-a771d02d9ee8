package com.touptek.xcamview.activity.ispdialogfragment.wbroimanagement;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001:\u00015B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\b\u0010\u001d\u001a\u00020\u0016H\u0002J\u001a\u0010\u001e\u001a\u0004\u0018\u00010\n2\u0006\u0010\u001f\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u000eH\u0002J\u0018\u0010!\u001a\u00020\u00102\u0006\u0010\u001f\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u000eH\u0002J\u0010\u0010\"\u001a\u00020\u00162\u0006\u0010#\u001a\u00020$H\u0014J(\u0010%\u001a\u00020\u00162\u0006\u0010&\u001a\u00020\u00072\u0006\u0010\'\u001a\u00020\u00072\u0006\u0010(\u001a\u00020\u00072\u0006\u0010)\u001a\u00020\u0007H\u0014J\u0010\u0010*\u001a\u00020\u00102\u0006\u0010+\u001a\u00020,H\u0016J&\u0010-\u001a\u00020\u00162\u0006\u0010.\u001a\u00020\u000e2\u0006\u0010/\u001a\u00020\u000e2\u0006\u00100\u001a\u00020\u000e2\u0006\u00101\u001a\u00020\u000eJ\u0014\u00102\u001a\u00020\u000e*\u0002032\u0006\u00104\u001a\u000203H\u0002R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R(\u0010\u0013\u001a\u0010\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u0016\u0018\u00010\u0014X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u000e\u0010\u001b\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/touptek/xcamview/activity/ispdialogfragment/wbroimanagement/TpRectangleOverlayView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "activeCorner", "Lcom/touptek/xcamview/activity/ispdialogfragment/wbroimanagement/TpRectangleOverlayView$Corner;", "cornerPaint", "Landroid/graphics/Paint;", "cornerRadius", "", "isDragging", "", "lastX", "lastY", "onPositionChanged", "Lkotlin/Function1;", "Landroid/graphics/RectF;", "", "getOnPositionChanged", "()Lkotlin/jvm/functions/Function1;", "setOnPositionChanged", "(Lkotlin/jvm/functions/Function1;)V", "rect", "rectPaint", "constrainRectangleToBounds", "getCornerAtPoint", "x", "y", "isPointOnBorder", "onDraw", "canvas", "Landroid/graphics/Canvas;", "onSizeChanged", "w", "h", "oldw", "oldh", "onTouchEvent", "event", "Landroid/view/MotionEvent;", "setRectanglePosition", "left", "top", "right", "bottom", "distanceTo", "Landroid/graphics/PointF;", "other", "Corner", "app_debug"})
public final class TpRectangleOverlayView extends android.view.View {
    private final android.graphics.Paint rectPaint = null;
    private final android.graphics.Paint cornerPaint = null;
    private final float cornerRadius = 10.0F;
    private android.graphics.RectF rect;
    private boolean isDragging = false;
    private float lastX = 0.0F;
    private float lastY = 0.0F;
    private com.touptek.xcamview.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView.Corner activeCorner;
    @org.jetbrains.annotations.Nullable
    private kotlin.jvm.functions.Function1<? super android.graphics.RectF, kotlin.Unit> onPositionChanged;
    
    @kotlin.jvm.JvmOverloads
    public TpRectangleOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public TpRectangleOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public TpRectangleOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlin.jvm.functions.Function1<android.graphics.RectF, kotlin.Unit> getOnPositionChanged() {
        return null;
    }
    
    public final void setOnPositionChanged(@org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function1<? super android.graphics.RectF, kotlin.Unit> p0) {
    }
    
    @java.lang.Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
    }
    
    public final void setRectanglePosition(float left, float top, float right, float bottom) {
    }
    
    @java.lang.Override
    protected void onDraw(@org.jetbrains.annotations.NotNull
    android.graphics.Canvas canvas) {
    }
    
    @java.lang.Override
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event) {
        return false;
    }
    
    private final com.touptek.xcamview.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView.Corner getCornerAtPoint(float x, float y) {
        return null;
    }
    
    private final boolean isPointOnBorder(float x, float y) {
        return false;
    }
    
    private final float distanceTo(android.graphics.PointF $this$distanceTo, android.graphics.PointF other) {
        return 0.0F;
    }
    
    private final void constrainRectangleToBounds() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0082\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/touptek/xcamview/activity/ispdialogfragment/wbroimanagement/TpRectangleOverlayView$Corner;", "", "(Ljava/lang/String;I)V", "TOP_LEFT", "TOP_RIGHT", "BOTTOM_RIGHT", "BOTTOM_LEFT", "app_debug"})
    static enum Corner {
        /*public static final*/ TOP_LEFT /* = new TOP_LEFT() */,
        /*public static final*/ TOP_RIGHT /* = new TOP_RIGHT() */,
        /*public static final*/ BOTTOM_RIGHT /* = new BOTTOM_RIGHT() */,
        /*public static final*/ BOTTOM_LEFT /* = new BOTTOM_LEFT() */;
        
        Corner() {
        }
    }
}
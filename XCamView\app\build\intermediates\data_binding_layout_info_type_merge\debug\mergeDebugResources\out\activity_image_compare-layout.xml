<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_image_compare" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\activity_image_compare.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_image_compare_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="132" endOffset="14"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="16" startOffset="8" endLine="25" endOffset="36"/></Target><Target id="@+id/btn_reset" view="ImageButton"><Expressions/><location startLine="38" startOffset="8" endLine="47" endOffset="36"/></Target><Target id="@+id/btn_swap" view="ImageButton"><Expressions/><location startLine="49" startOffset="8" endLine="57" endOffset="36"/></Target><Target id="@+id/compare_container" view="LinearLayout"><Expressions/><location startLine="62" startOffset="4" endLine="90" endOffset="18"/></Target><Target id="@+id/left_image" view="ImageView"><Expressions/><location startLine="69" startOffset="8" endLine="75" endOffset="42"/></Target><Target id="@+id/right_image" view="ImageView"><Expressions/><location startLine="82" startOffset="8" endLine="88" endOffset="42"/></Target><Target id="@+id/tv_left_info" view="TextView"><Expressions/><location startLine="101" startOffset="8" endLine="111" endOffset="40"/></Target><Target id="@+id/tv_right_info" view="TextView"><Expressions/><location startLine="118" startOffset="8" endLine="128" endOffset="40"/></Target></Targets></Layout>
package com.touptek.xcamview.util;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 2, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0001\u001a\n\u0010\u0002\u001a\u00020\u0003*\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"dpToPx", "", "setupEdgeToEdgeFullScreen", "", "Landroidx/appcompat/app/AppCompatActivity;", "app_debug"})
public final class TpExtensionsKt {
    
    public static final int dpToPx(int $this$dpToPx) {
        return 0;
    }
    
    public static final void setupEdgeToEdgeFullScreen(@org.jetbrains.annotations.NotNull
    androidx.appcompat.app.AppCompatActivity $this$setupEdgeToEdgeFullScreen) {
    }
}
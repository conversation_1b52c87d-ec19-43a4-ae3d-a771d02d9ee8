<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>TpSambaClient</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="declaration: package: com.touptek.utils, class: TpSambaClient">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.touptek.utils</a></div>
<h1 title="类 TpSambaClient" class="title">类 TpSambaClient</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.touptek.utils.TpSambaClient</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TpSambaClient</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">SambaUploader类用于连接Samba服务器并上传图片和视频。
 <p>
 此类提供了以下功能：
 <ul>
   <li>保存和加载Samba连接设置</li>
   <li>测试Samba连接</li>
   <li>将本地图片上传到Samba服务器</li>
   <li>将本地视频上传到Samba服务器</li>
   <li>提供上传状态回调</li>
 </ul>
 
 <h2>项目配置</h2>
 
 <h3>1. build.gradle配置</h3>
 <p>需要在app/build.gradle文件的dependencies部分添加JCIFS依赖：</p>
 <pre><code>
 dependencies {
     // 现有依赖...
     
     // JCIFS库，用于Samba文件传输
     implementation 'eu.agno3.jcifs:jcifs-ng:2.1.9'
 }
 </code></pre>
 
 <h3>2. AndroidManifest.xml配置</h3>
 <p>需要在AndroidManifest.xml中添加以下网络权限：</p>
 <pre><code>
 &lt;!-- 网络权限，用于Samba文件传输 --&gt;
 &lt;uses-permission android:name="android.permission.INTERNET" /&gt;
 &lt;uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /&gt;
 &lt;uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /&gt;
 </code></pre>

 <h2>使用方法</h2>
 
 <h3>1. 初始化</h3>
 <pre><code>
 // 创建SambaUploader实例
 TpSambaClient sambaUploader = new TpSambaClient(context);
 </code></pre>
 
 <h3>2. 配置连接参数</h3>
 <pre><code>
 // 设置Samba连接参数
 sambaUploader.setConnectionParams(
     "*************",  // 服务器IP
     "username",        // 用户名（留空表示匿名访问）
     "password",        // 密码（留空表示匿名访问）
     "share",           // 共享名称
     "/photos",         // 远程路径
     true               // 是否启用上传
 );
 
 // 或者单独设置是否启用
 sambaUploader.setEnabled(true);

 // 使用配置对象方式（推荐）
 TpSambaClient.SMBConfig config = new TpSambaClient.SMBConfig(
     "*************",   // 服务器IP
     "username",        // 用户名
     "password",        // 密码
     "share",           // 共享名称
     "/photos",         // 远程路径
     true               // 是否启用
 );
 sambaUploader.setConnectionParams(config);

 // 获取当前配置
 TpSambaClient.SMBConfig currentConfig = sambaUploader.getConnectionParams();
 String serverIp = currentConfig.getServerIp();
 </code></pre>

 <h3>3. 测试连接</h3>
 <pre><code>
 sambaUploader.testConnection(new TpSambaClient.UploadListener() {
     @Override
     public void onUploadSuccess(String remoteFilePath) {
         // 连接成功
         Toast.makeText(context, "连接成功!", Toast.LENGTH_SHORT).show();
     }
     
     @Override
     public void onUploadFailed(String errorMessage) {
         // 连接失败
         Toast.makeText(context, "连接失败: " + errorMessage, Toast.LENGTH_SHORT).show();
     }
 });
 </code></pre>
 
 <h3>4. 上传文件</h3>
 <pre><code>
 // 上传图片文件
 String imagePath = "/storage/emulated/0/Pictures/image.jpg";
 sambaUploader.uploadFile(imagePath, new TpSambaClient.UploadListener() {
     @Override
     public void onUploadSuccess(String remoteFilePath) {
         Log.d("TpSambaClient", "文件上传成功: " + remoteFilePath);
     }

     @Override
     public void onUploadFailed(String errorMessage) {
         Log.e("TpSambaClient", "文件上传失败: " + errorMessage);
     }
 });

 // 上传视频文件
 String videoPath = "/storage/emulated/0/Movies/video.mp4";
 sambaUploader.uploadFile(videoPath, new TpSambaClient.UploadListener() {
     @Override
     public void onUploadSuccess(String remoteFilePath) {
         Log.d("TpSambaClient", "视频上传成功: " + remoteFilePath);
     }

     @Override
     public void onUploadFailed(String errorMessage) {
         Log.e("TpSambaClient", "视频上传失败: " + errorMessage);
     }
 });

 // 上传文件并指定远程文件名
 String localPath = "/storage/emulated/0/Pictures/photo.jpg";
 String remoteName = "renamed_photo.jpg";
 sambaUploader.uploadFile(localPath, remoteName, new TpSambaClient.UploadListener() {
     @Override
     public void onUploadSuccess(String remoteFilePath) {
         Log.d("TpSambaClient", "文件上传成功: " + remoteFilePath);
     }

     @Override
     public void onUploadFailed(String errorMessage) {
         Log.e("TpSambaClient", "文件上传失败: " + errorMessage);
     }
 });
 </code></pre>
 
 <h3>5. 获取远程目录列表</h3>
 <pre><code>
 sambaUploader.getRemoteDirectories(new TpSambaClient.DirectoryListListener() {
     @Override
     public void onDirectoriesLoaded(List&lt;String&gt; directories) {
         // 处理获取到的目录列表
         for (String dir : directories) {
             Log.d("TpSambaClient", "发现目录: " + dir);
         }
     }
     
     @Override
     public void onDirectoryLoadFailed(String errorMessage) {
         Log.e("TpSambaClient", "获取目录失败: " + errorMessage);
     }
 });
 </code></pre>
 
 <h3>6. 与CaptureImageHelper集成</h3>
 <pre><code>
 // 创建CaptureImageHelper并设置回调
 TpCaptureImage captureImageHelper = TpCaptureImage.builder(new Size(3840, 2160))
     .onImageSaved(filePath -&gt; {
         // 图片已保存到本地
         runOnUiThread(() -&gt; Toast.makeText(this, "图片已保存: " + filePath, Toast.LENGTH_SHORT).show());
         
         // 在回调中处理SMB上传
         if (sambaUploader != null &amp;&amp; sambaUploader.isEnabled()) {
             Log.d(TAG, "开始上传图片到SMB服务器: " + filePath);
             sambaUploader.uploadFile(filePath, new TpSambaClient.UploadListener() {
                 @Override
                 public void onUploadSuccess(String remoteFilePath) {
                     Log.d(TAG, "图片上传成功: " + remoteFilePath);
                 }

                 @Override
                 public void onUploadFailed(String errorMessage) {
                     Log.e(TAG, "图片上传失败: " + errorMessage);
                 }
             });
         }
     })
     .onError(errorMessage -&gt; {
         // 显示错误提示
         runOnUiThread(() -&gt; Toast.makeText(this, "抓图失败: " + errorMessage, Toast.LENGTH_SHORT).show());
     })
     .build();
 </code></pre>
 
 <h3>7. 在Activity中完整实现</h3>
 <pre><code>
 public class MainActivity extends AppCompatActivity {
     private TpSambaClient sambaUploader;
     private TpCaptureImage captureImageHelper;
     
     @Override
     protected void onCreate(Bundle savedInstanceState) {
         super.onCreate(savedInstanceState);
         
         // 初始化SMB上传器
         sambaUploader = new TpSambaClient(this);
         
         // 初始化抓图助手
         captureImageHelper = TpCaptureImage.builder(new Size(3840, 2160))
             .onImageSaved(filePath -&gt; {
                 // 显示Toast提示
                 runOnUiThread(() -&gt; Toast.makeText(this, "图片已保存: " + filePath, Toast.LENGTH_SHORT).show());
                 
                 // 在回调中处理SMB上传
                 if (sambaUploader != null &amp;&amp; sambaUploader.isEnabled()) {
                     Log.d(TAG, "开始上传图片到SMB服务器: " + filePath);
                     sambaUploader.uploadFile(filePath, new TpSambaClient.UploadListener() {
                         @Override
                         public void onUploadSuccess(String remoteFilePath) {
                             Log.d(TAG, "图片上传成功: " + remoteFilePath);
                         }
                         
                         @Override
                         public void onUploadFailed(String errorMessage) {
                             Log.e(TAG, "图片上传失败: " + errorMessage);
                         }
                     });
                 }
             })
             .onError(errorMessage -&gt; {
                 // 显示错误提示
                 runOnUiThread(() -&gt; Toast.makeText(this, "抓图失败: " + errorMessage, Toast.LENGTH_SHORT).show());
             })
             .build();
             
         // 设置SMB设置按钮点击事件
         findViewById(R.id.btnSmbSettings).setOnClickListener(v -&gt; {
             showSMBSettingsDialog();
         });
         
         // 设置拍照按钮点击事件
         findViewById(R.id.btnCapture).setOnClickListener(v -&gt; {
             captureImage();
         });
     }
     
     // 显示SMB设置对话框
     private void showSMBSettingsDialog() {
         if (sambaUploader != null) {
             SMBSettingsDialog dialog = new SMBSettingsDialog(this, sambaUploader);
             dialog.show();
         }
     }
     
     // 抓图方法
     private void captureImage() {
         Size imageSize = new Size(3840, 2160);
         String outputPath = TpFileManager.createImagePath(this, "prefix", true, "jpg");
         captureImageHelper.requestCapture(imageSize, outputPath);
     }
 }
 </code></pre>
 
 <h3>9. API说明</h3>
 
 <h4>接口和回调</h4>
 <ul>
   <li><code>UploadListener</code> - 上传结果回调接口，包含成功和失败的回调方法</li>
   <li><code>DirectoryListListener</code> - 目录列表回调接口，包含成功获取目录列表和失败的回调方法</li>
 </ul>
 
 <h4>主要方法</h4>
 <ul>
   <li><code>TpSambaClient(Context context)</code> - 构造函数，创建上传器实例</li>
   <li><code>setConnectionParams(String serverIp, String username, String password, String shareName, String remotePath, boolean enabled)</code> - 设置连接参数</li>
   <li><code>setConnectionParams(SMBConfig config)</code> - 使用配置对象设置连接参数</li>
   <li><code>getConnectionParams()</code> - 获取当前连接配置</li>
   <li><code>isEnabled()</code> - 检查是否启用了Samba上传</li>
   <li><code>setEnabled(boolean enabled)</code> - 设置是否启用Samba上传</li>
   <li><code>testConnection(UploadListener callback)</code> - 测试Samba服务器连接</li>
   <li><code>getRemoteDirectories(DirectoryListListener callback)</code> - 获取远程目录列表</li>
   <li><code>uploadFile(String localFilePath, UploadListener callback)</code> - 上传文件到Samba服务器</li>
   <li><code>uploadFile(String localFilePath, String remoteFileName, UploadListener callback)</code> - 上传文件并指定远程文件名</li>
 </ul></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TpSambaClient.DirectoryListListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">目录列表回调接口</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TpSambaClient.SMBConfig.html" class="type-name-link" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></code></div>
<div class="col-last odd-row-color">
<div class="block">SMB连接配置类</div>
</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TpSambaClient.UploadListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">上传结果回调接口</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(android.content.Context)" class="member-name-link">TpSambaClient</a><wbr>(android.content.Context&nbsp;context)</code></div>
<div class="col-last even-row-color">
<div class="block">构造函数</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getConnectionParams()" class="member-name-link">getConnectionParams</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前连接配置</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRemoteDirectories(com.touptek.utils.TpSambaClient.DirectoryListListener)" class="member-name-link">getRemoteDirectories</a><wbr>(<a href="TpSambaClient.DirectoryListListener.html" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a>&nbsp;callback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取远程目录列表</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isEnabled()" class="member-name-link">isEnabled</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查是否启用了Samba上传</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setConnectionParams(com.touptek.utils.TpSambaClient.SMBConfig)" class="member-name-link">setConnectionParams</a><wbr>(<a href="TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a>&nbsp;config)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置Samba连接参数（使用配置对象）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setConnectionParams(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)" class="member-name-link">setConnectionParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;serverIp,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;username,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;password,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;shareName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;remotePath,
 boolean&nbsp;enabled)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置Samba连接参数</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEnabled(boolean)" class="member-name-link">setEnabled</a><wbr>(boolean&nbsp;enabled)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置是否启用Samba上传</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#testConnection(com.touptek.utils.TpSambaClient.UploadListener)" class="member-name-link">testConnection</a><wbr>(<a href="TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a>&nbsp;callback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">测试Samba服务器连接</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#uploadFile(java.lang.String,com.touptek.utils.TpSambaClient.UploadListener)" class="member-name-link">uploadFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;localFilePath,
 <a href="TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a>&nbsp;callback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">上传文件到Samba服务器（统一方法）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#uploadFile(java.lang.String,java.lang.String,com.touptek.utils.TpSambaClient.UploadListener)" class="member-name-link">uploadFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;localFilePath,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;remoteFileName,
 <a href="TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a>&nbsp;callback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">上传文件到Samba服务器（指定远程文件名）</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(android.content.Context)">
<h3>TpSambaClient</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TpSambaClient</span><wbr><span class="parameters">(android.content.Context&nbsp;context)</span></div>
<div class="block">构造函数</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="setConnectionParams(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)">
<h3>setConnectionParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setConnectionParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;serverIp,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;username,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;password,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;shareName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;remotePath,
 boolean&nbsp;enabled)</span></div>
<div class="block">设置Samba连接参数</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>serverIp</code> - Samba服务器的IP地址</dd>
<dd><code>username</code> - 登录用户名，留空表示匿名访问</dd>
<dd><code>password</code> - 登录密码，留空表示匿名访问</dd>
<dd><code>shareName</code> - Samba共享名称</dd>
<dd><code>remotePath</code> - 服务器上的路径</dd>
<dd><code>enabled</code> - 是否启用Samba上传功能</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setConnectionParams(com.touptek.utils.TpSambaClient.SMBConfig)">
<h3>setConnectionParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setConnectionParams</span><wbr><span class="parameters">(<a href="TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a>&nbsp;config)</span></div>
<div class="block">设置Samba连接参数（使用配置对象）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>config</code> - SMB连接配置对象</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getConnectionParams()">
<h3>getConnectionParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TpSambaClient.SMBConfig.html" title="com.touptek.utils中的类">TpSambaClient.SMBConfig</a></span>&nbsp;<span class="element-name">getConnectionParams</span>()</div>
<div class="block">获取当前连接配置</div>
<dl class="notes">
<dt>返回:</dt>
<dd>SMB连接配置对象</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isEnabled()">
<h3>isEnabled</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEnabled</span>()</div>
<div class="block">检查是否启用了Samba上传</div>
</section>
</li>
<li>
<section class="detail" id="setEnabled(boolean)">
<h3>setEnabled</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEnabled</span><wbr><span class="parameters">(boolean&nbsp;enabled)</span></div>
<div class="block">设置是否启用Samba上传</div>
</section>
</li>
<li>
<section class="detail" id="testConnection(com.touptek.utils.TpSambaClient.UploadListener)">
<h3>testConnection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">testConnection</span><wbr><span class="parameters">(<a href="TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a>&nbsp;callback)</span></div>
<div class="block">测试Samba服务器连接</div>
</section>
</li>
<li>
<section class="detail" id="getRemoteDirectories(com.touptek.utils.TpSambaClient.DirectoryListListener)">
<h3>getRemoteDirectories</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getRemoteDirectories</span><wbr><span class="parameters">(<a href="TpSambaClient.DirectoryListListener.html" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a>&nbsp;callback)</span></div>
<div class="block">获取远程目录列表</div>
</section>
</li>
<li>
<section class="detail" id="uploadFile(java.lang.String,com.touptek.utils.TpSambaClient.UploadListener)">
<h3>uploadFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">uploadFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;localFilePath,
 <a href="TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a>&nbsp;callback)</span></div>
<div class="block">上传文件到Samba服务器（统一方法）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>localFilePath</code> - 本地文件路径</dd>
<dd><code>callback</code> - 上传结果回调</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uploadFile(java.lang.String,java.lang.String,com.touptek.utils.TpSambaClient.UploadListener)">
<h3>uploadFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">uploadFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;localFilePath,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;remoteFileName,
 <a href="TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a>&nbsp;callback)</span></div>
<div class="block">上传文件到Samba服务器（指定远程文件名）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>localFilePath</code> - 本地文件路径</dd>
<dd><code>remoteFileName</code> - 远程文件名（可选，为null时使用本地文件名）</dd>
<dd><code>callback</code> - 上传结果回调</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>

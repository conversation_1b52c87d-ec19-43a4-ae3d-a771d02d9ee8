package com.touptek.xcamview.activity.ispdialogfragment;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010%\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0002J\u0010\u0010\r\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u0010\u0010\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u0010\u0012\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\b\u0010\u0013\u001a\u00020\nH\u0002J&\u0010\u0014\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0016J\b\u0010\u001b\u001a\u00020\nH\u0016J\u0010\u0010\u001c\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J(\u0010\u001d\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u001f\u001a\u00020\u00072\u0006\u0010 \u001a\u00020!H\u0002J\u0018\u0010\"\u001a\u00020\n2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010#\u001a\u00020\u0007H\u0002J0\u0010$\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010%\u001a\u00020\u00072\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010&\u001a\u00020\u00072\u0006\u0010\'\u001a\u00020\u0007H\u0002J \u0010(\u001a\u00020\n2\u0006\u0010)\u001a\u00020*2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010#\u001a\u00020\u0007H\u0002J(\u0010+\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u001f\u001a\u00020\u00072\u0006\u0010 \u001a\u00020!H\u0002J\u0010\u0010,\u001a\u00020\n2\u0006\u0010-\u001a\u00020.H\u0002J\u0018\u0010/\u001a\u00020\n2\u0006\u0010 \u001a\u00020!2\u0006\u00100\u001a\u00020\u0007H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/touptek/xcamview/activity/ispdialogfragment/TpImageProcessDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "()V", "handler", "Landroid/os/Handler;", "seekBarListeners", "", "", "Landroid/widget/SeekBar$OnSeekBarChangeListener;", "changeAdjustBtnState", "", "enable", "", "handleImageDefaultButtonClick", "view", "Landroid/view/View;", "initButtonControls", "initParameterValue", "initSeekbar", "initTpIspParam", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onStart", "setDefaultParameter", "setSeekBarProgress", "seekBarId", "textViewId", "param", "Lcom/touptek/video/TpIspParam;", "setShortPress", "delta", "setupButtonControl", "buttonId", "shortIncrement", "longIncrement", "setupLongPress", "button", "Landroid/widget/ImageButton;", "setupSeekBar", "showToast", "message", "", "updateSeekBar", "newValue", "app_debug"})
public final class TpImageProcessDialogFragment extends androidx.fragment.app.DialogFragment {
    private final android.os.Handler handler = null;
    private final java.util.Map<java.lang.Integer, android.widget.SeekBar.OnSeekBarChangeListener> seekBarListeners = null;
    
    public TpImageProcessDialogFragment() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    private final void initTpIspParam() {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    private final void initParameterValue(android.view.View view) {
    }
    
    private final void setSeekBarProgress(android.view.View view, int seekBarId, int textViewId, com.touptek.video.TpIspParam param) {
    }
    
    private final void initSeekbar(android.view.View view) {
    }
    
    private final void setupSeekBar(android.view.View view, int seekBarId, int textViewId, com.touptek.video.TpIspParam param) {
    }
    
    private final void updateSeekBar(com.touptek.video.TpIspParam param, int newValue) {
    }
    
    private final void handleImageDefaultButtonClick(android.view.View view) {
    }
    
    private final void setDefaultParameter(android.view.View view) {
    }
    
    private final void changeAdjustBtnState(boolean enable) {
    }
    
    private final void initButtonControls(android.view.View view) {
    }
    
    private final void setupButtonControl(android.view.View view, int buttonId, int seekBarId, int shortIncrement, int longIncrement) {
    }
    
    private final void setShortPress(int seekBarId, int delta) {
    }
    
    private final void setupLongPress(android.widget.ImageButton button, int seekBarId, int delta) {
    }
}
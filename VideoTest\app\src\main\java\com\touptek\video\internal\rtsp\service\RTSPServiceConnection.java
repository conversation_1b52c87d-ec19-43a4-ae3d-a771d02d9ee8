package com.touptek.video.internal.rtsp.service;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;

import com.touptek.video.internal.rtsp.config.RTSPConfig;

/**
 * RTSP服务连接管理类
 * 负责管理与RTSPService的连接
 */
public class RTSPServiceConnection implements ServiceConnection {
    
    private static final String TAG = "RTSPServiceConnection";
    
    private final Context context;
    private final RTSPConfig config;
    private final ProjectionData projectionData;
    private final UrlCallback onServiceConnected;
    private final Runnable onServiceDisconnected;
    
    /**
     * URL回调接口
     */
    public interface UrlCallback {
        void onUrl(String url);
    }
    
    /**
     * 服务实例
     */
    private RTSPService service;
    
    /**
     * 创建服务连接
     * @param context 上下文
     * @param config RTSP配置
     * @param projectionData 投影数据（屏幕捕获时需要，相机推流时可为null）
     * @param onServiceConnected 服务连接成功回调
     * @param onServiceDisconnected 服务断开连接回调
     */
    public RTSPServiceConnection(
            Context context,
            RTSPConfig config,
            ProjectionData projectionData,
            UrlCallback onServiceConnected,
            Runnable onServiceDisconnected) {
        this.context = context;
        this.config = config;
        this.projectionData = projectionData;
        this.onServiceConnected = onServiceConnected;
        this.onServiceDisconnected = onServiceDisconnected;
    }
    
    /**
     * 服务连接成功回调
     */
    @Override
    public void onServiceConnected(ComponentName name, IBinder binder) {
        Log.d(TAG, "Service connected");
        
        RTSPService.RTSPBinder rtspBinder = (RTSPService.RTSPBinder) binder;
        service = rtspBinder.getService();
        
        // 开始推流
        String rtspUrl = service.startStreaming(config, projectionData);
        
        if (rtspUrl != null) {
            Log.d(TAG, "Streaming started: " + rtspUrl);
            onServiceConnected.onUrl(rtspUrl);
        } else {
            Log.e(TAG, "Failed to start streaming");
            release();
            context.unbindService(this);
        }
    }
    
    /**
     * 服务断开连接回调
     */
    @Override
    public void onServiceDisconnected(ComponentName name) {
        Log.d(TAG, "Service disconnected");
        service = null;
        onServiceDisconnected.run();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        Log.d(TAG, "Releasing service connection");
        if (service != null) {
            service.stopStreaming();
            service = null;
        }
    }
    
    /**
     * 绑定服务
     * @return 是否成功绑定
     */
    public boolean bind() {
        try {
            Intent intent = new Intent(context, RTSPService.class);
            return context.bindService(intent, this, Context.BIND_AUTO_CREATE);
        } catch (Exception e) {
            Log.e(TAG, "Failed to bind service", e);
            return false;
        }
    }
    
    /**
     * 解绑服务
     */
    public void unbind() {
        try {
            release();
            context.unbindService(this);
        } catch (Exception e) {
            Log.e(TAG, "Failed to unbind service", e);
        }
    }
}

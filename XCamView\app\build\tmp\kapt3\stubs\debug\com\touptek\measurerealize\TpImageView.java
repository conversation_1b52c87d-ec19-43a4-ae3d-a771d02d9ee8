package com.touptek.measurerealize;

import java.lang.System;

@android.annotation.SuppressLint(value = {"ClickableViewAccessibility"})
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0013\b\u0007\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0018\u00104\u001a\u00020#2\u0006\u00105\u001a\u00020\n2\u0006\u00106\u001a\u00020\nH\u0002J\b\u00107\u001a\u00020#H\u0002J\b\u00108\u001a\u00020#H\u0002J\b\u00109\u001a\u00020#H\u0003J\u0006\u0010:\u001a\u00020\nJ\u0006\u0010;\u001a\u00020\nJ\b\u0010<\u001a\u00020\nH\u0002J\b\u0010=\u001a\u00020>H\u0002J\u0010\u0010?\u001a\u00020\n2\u0006\u0010@\u001a\u00020&H\u0002J\u0010\u0010A\u001a\u00020#2\u0006\u0010@\u001a\u00020&H\u0002J\b\u0010B\u001a\u00020#H\u0002J \u0010C\u001a\u00020#2\u0006\u0010D\u001a\u00020\n2\u0006\u0010E\u001a\u00020\n2\u0006\u0010F\u001a\u00020\nH\u0002J\b\u0010G\u001a\u00020\u0018H\u0002J\b\u0010H\u001a\u00020#H\u0014J(\u0010I\u001a\u00020#2\u0006\u0010J\u001a\u00020\u00072\u0006\u0010K\u001a\u00020\u00072\u0006\u0010L\u001a\u00020\u00072\u0006\u0010M\u001a\u00020\u0007H\u0014J\b\u0010N\u001a\u00020\u0018H\u0016J\u0006\u0010O\u001a\u00020#J\u0012\u0010P\u001a\u00020#2\b\u0010Q\u001a\u0004\u0018\u00010RH\u0016J\u0012\u0010S\u001a\u00020#2\b\u0010T\u001a\u0004\u0018\u00010UH\u0016J\u0016\u0010V\u001a\u00020#2\u000e\u0010W\u001a\n\u0012\u0004\u0012\u00020#\u0018\u00010\"J(\u0010X\u001a\u00020#2 \u0010Y\u001a\u001c\u0012\u0004\u0012\u00020&\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0018\u0018\u00010%J\u001c\u0010Z\u001a\u00020#2\u0014\u0010W\u001a\u0010\u0012\u0004\u0012\u00020&\u0012\u0004\u0012\u00020#\u0018\u00010(J\u000e\u0010[\u001a\u00020#2\u0006\u0010\\\u001a\u00020\u0018J\b\u0010]\u001a\u00020#H\u0002J\b\u0010^\u001a\u00020#H\u0002J\u0018\u0010_\u001a\u00020#2\u0006\u0010`\u001a\u00020\n2\u0006\u0010a\u001a\u00020\nH\u0002J\b\u0010b\u001a\u00020#H\u0002J\u0012\u0010c\u001a\u00020#2\b\u0010d\u001a\u0004\u0018\u00010RH\u0002J\u0010\u0010e\u001a\u00020#2\u0006\u0010f\u001a\u00020 H\u0002J\b\u0010g\u001a\u00020#H\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\nX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020 X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010!\u001a\n\u0012\u0004\u0012\u00020#\u0018\u00010\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000R(\u0010$\u001a\u001c\u0012\u0004\u0012\u00020&\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0018\u0018\u00010%X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\'\u001a\u0010\u0012\u0004\u0012\u00020&\u0012\u0004\u0012\u00020#\u0018\u00010(X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020 X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010*\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020,X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010-\u001a\u00020\u00078BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b0\u00101\u001a\u0004\b.\u0010/R\u000e\u00102\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006h"}, d2 = {"Lcom/touptek/measurerealize/TpImageView;", "Landroidx/appcompat/widget/AppCompatImageView;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "CUSTOM_SCALE_THRESHOLD", "", "MAX_SCALE", "MIN_SCALE_SPAN", "SCALE_SENSITIVITY", "baseScale", "currentScale", "dynamicMinScale", "flingAnimator", "Landroid/animation/ValueAnimator;", "gestureDetector", "Landroid/view/GestureDetector;", "imageHeight", "imageWidth", "isCustomScaling", "", "isInitialized", "isScaling", "isZoomEnabled", "lastDistance", "lastFocusX", "lastFocusY", "matrix", "Landroid/graphics/Matrix;", "matrixChangeListener", "Lkotlin/Function0;", "", "measurementTouchHandler", "Lkotlin/Function3;", "Landroid/view/MotionEvent;", "onSingleTapListener", "Lkotlin/Function1;", "savedMatrix", "scaleAnimator", "scaleDetector", "Landroid/view/ScaleGestureDetector;", "touchSlop", "getTouchSlop", "()I", "touchSlop$delegate", "Lkotlin/Lazy;", "viewHeight", "viewWidth", "animateTranslate", "deltaX", "deltaY", "cancelAnimations", "captureDrawableDimensions", "configureScaleDetectorSensitivity", "getBaseScale", "getCurrentScale", "getCurrentScaleFactor", "getDisplayRect", "Landroid/graphics/RectF;", "getDistance", "event", "handleCustomScale", "instantCheckBounds", "instantScaleTo", "targetScale", "focusX", "focusY", "isAtMinimumScale", "onDetachedFromWindow", "onSizeChanged", "w", "h", "oldw", "oldh", "performClick", "restoreTouchListener", "setImageBitmap", "bm", "Landroid/graphics/Bitmap;", "setImageDrawable", "drawable", "Landroid/graphics/drawable/Drawable;", "setMatrixChangeListener", "listener", "setMeasurementTouchHandler", "handler", "setOnSingleTapListener", "setZoomEnabled", "enabled", "setupGestureDetectors", "setupTouchListener", "startFlingAnimation", "velocityX", "velocityY", "syncCurrentScale", "updateBitmapDimensions", "bitmap", "updateImageMatrix", "newMatrix", "updateMatrix", "app_debug"})
public final class TpImageView extends androidx.appcompat.widget.AppCompatImageView {
    private float currentScale = 1.0F;
    private float baseScale = 1.0F;
    private float dynamicMinScale = 0.3F;
    private final float MAX_SCALE = 5.0F;
    private final float SCALE_SENSITIVITY = 3.0F;
    private final float MIN_SCALE_SPAN = 10.0F;
    private final float CUSTOM_SCALE_THRESHOLD = 3.0F;
    private final android.graphics.Matrix matrix = null;
    private final android.graphics.Matrix savedMatrix = null;
    private android.view.ScaleGestureDetector scaleDetector;
    private android.view.GestureDetector gestureDetector;
    private boolean isInitialized = false;
    private boolean isScaling = false;
    private boolean isCustomScaling = false;
    private boolean isZoomEnabled = true;
    private int viewWidth = 0;
    private int viewHeight = 0;
    private int imageWidth = 0;
    private int imageHeight = 0;
    private android.animation.ValueAnimator scaleAnimator;
    private android.animation.ValueAnimator flingAnimator;
    private float lastDistance = -1.0F;
    private float lastFocusX = 0.0F;
    private float lastFocusY = 0.0F;
    private final kotlin.Lazy touchSlop$delegate = null;
    private kotlin.jvm.functions.Function1<? super android.view.MotionEvent, kotlin.Unit> onSingleTapListener;
    private kotlin.jvm.functions.Function0<kotlin.Unit> matrixChangeListener;
    private kotlin.jvm.functions.Function3<? super android.view.MotionEvent, ? super java.lang.Integer, ? super java.lang.Integer, java.lang.Boolean> measurementTouchHandler;
    
    @kotlin.jvm.JvmOverloads
    public TpImageView(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public TpImageView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public TpImageView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    private final int getTouchSlop() {
        return 0;
    }
    
    @android.annotation.SuppressLint(value = {"SoonBlockedPrivateApi"})
    private final void configureScaleDetectorSensitivity() {
    }
    
    @java.lang.Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
    }
    
    @java.lang.Override
    public void setImageBitmap(@org.jetbrains.annotations.Nullable
    android.graphics.Bitmap bm) {
    }
    
    @java.lang.Override
    public void setImageDrawable(@org.jetbrains.annotations.Nullable
    android.graphics.drawable.Drawable drawable) {
    }
    
    private final void updateBitmapDimensions(android.graphics.Bitmap bitmap) {
    }
    
    private final void captureDrawableDimensions() {
    }
    
    private final void setupGestureDetectors() {
    }
    
    private final void setupTouchListener() {
    }
    
    private final void updateMatrix() {
    }
    
    /**
     * 🔧 获取当前真实缩放级别 - 从矩阵读取准确值
     */
    private final float getCurrentScaleFactor() {
        return 0.0F;
    }
    
    /**
     * 🔄 同步currentScale变量与矩阵状态
     */
    private final void syncCurrentScale() {
    }
    
    /**
     * 🔧 检查是否处于最小缩放状态 - 使用真实矩阵值
     */
    private final boolean isAtMinimumScale() {
        return false;
    }
    
    private final void instantCheckBounds() {
    }
    
    private final void animateTranslate(float deltaX, float deltaY) {
    }
    
    private final void startFlingAnimation(float velocityX, float velocityY) {
    }
    
    private final void instantScaleTo(float targetScale, float focusX, float focusY) {
    }
    
    private final android.graphics.RectF getDisplayRect() {
        return null;
    }
    
    private final void updateImageMatrix(android.graphics.Matrix newMatrix) {
    }
    
    private final void cancelAnimations() {
    }
    
    @java.lang.Override
    public boolean performClick() {
        return false;
    }
    
    @java.lang.Override
    protected void onDetachedFromWindow() {
    }
    
    public final float getCurrentScale() {
        return 0.0F;
    }
    
    public final float getBaseScale() {
        return 0.0F;
    }
    
    public final void setZoomEnabled(boolean enabled) {
    }
    
    public final void setOnSingleTapListener(@org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function1<? super android.view.MotionEvent, kotlin.Unit> listener) {
    }
    
    public final void setMatrixChangeListener(@org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    /**
     * 🎯 设置测量触摸处理器 - 支持缩放+测量的混合模式
     */
    public final void setMeasurementTouchHandler(@org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function3<? super android.view.MotionEvent, ? super java.lang.Integer, ? super java.lang.Integer, java.lang.Boolean> handler) {
    }
    
    /**
     * 🔄 恢复原始触摸监听器 - 清除测量处理器
     */
    public final void restoreTouchListener() {
    }
    
    private final void handleCustomScale(android.view.MotionEvent event) {
    }
    
    private final float getDistance(android.view.MotionEvent event) {
        return 0.0F;
    }
}
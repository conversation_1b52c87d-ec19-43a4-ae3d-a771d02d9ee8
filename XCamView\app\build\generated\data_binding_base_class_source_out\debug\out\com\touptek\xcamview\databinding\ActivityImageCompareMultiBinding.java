// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.measurerealize.TpImageView;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityImageCompareMultiBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnReset;

  @NonNull
  public final ImageButton btnSyncMode;

  @NonNull
  public final TpImageView imageView1;

  @NonNull
  public final TpImageView imageView2;

  @NonNull
  public final TpImageView imageView3;

  @NonNull
  public final TpImageView imageView4;

  @NonNull
  public final TextView tvInfo1;

  @NonNull
  public final TextView tvInfo2;

  @NonNull
  public final TextView tvInfo3;

  @NonNull
  public final TextView tvInfo4;

  private ActivityImageCompareMultiBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnBack, @NonNull ImageButton btnReset, @NonNull ImageButton btnSyncMode,
      @NonNull TpImageView imageView1, @NonNull TpImageView imageView2,
      @NonNull TpImageView imageView3, @NonNull TpImageView imageView4, @NonNull TextView tvInfo1,
      @NonNull TextView tvInfo2, @NonNull TextView tvInfo3, @NonNull TextView tvInfo4) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnReset = btnReset;
    this.btnSyncMode = btnSyncMode;
    this.imageView1 = imageView1;
    this.imageView2 = imageView2;
    this.imageView3 = imageView3;
    this.imageView4 = imageView4;
    this.tvInfo1 = tvInfo1;
    this.tvInfo2 = tvInfo2;
    this.tvInfo3 = tvInfo3;
    this.tvInfo4 = tvInfo4;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityImageCompareMultiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityImageCompareMultiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_image_compare_multi, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityImageCompareMultiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_reset;
      ImageButton btnReset = ViewBindings.findChildViewById(rootView, id);
      if (btnReset == null) {
        break missingId;
      }

      id = R.id.btn_sync_mode;
      ImageButton btnSyncMode = ViewBindings.findChildViewById(rootView, id);
      if (btnSyncMode == null) {
        break missingId;
      }

      id = R.id.image_view_1;
      TpImageView imageView1 = ViewBindings.findChildViewById(rootView, id);
      if (imageView1 == null) {
        break missingId;
      }

      id = R.id.image_view_2;
      TpImageView imageView2 = ViewBindings.findChildViewById(rootView, id);
      if (imageView2 == null) {
        break missingId;
      }

      id = R.id.image_view_3;
      TpImageView imageView3 = ViewBindings.findChildViewById(rootView, id);
      if (imageView3 == null) {
        break missingId;
      }

      id = R.id.image_view_4;
      TpImageView imageView4 = ViewBindings.findChildViewById(rootView, id);
      if (imageView4 == null) {
        break missingId;
      }

      id = R.id.tv_info_1;
      TextView tvInfo1 = ViewBindings.findChildViewById(rootView, id);
      if (tvInfo1 == null) {
        break missingId;
      }

      id = R.id.tv_info_2;
      TextView tvInfo2 = ViewBindings.findChildViewById(rootView, id);
      if (tvInfo2 == null) {
        break missingId;
      }

      id = R.id.tv_info_3;
      TextView tvInfo3 = ViewBindings.findChildViewById(rootView, id);
      if (tvInfo3 == null) {
        break missingId;
      }

      id = R.id.tv_info_4;
      TextView tvInfo4 = ViewBindings.findChildViewById(rootView, id);
      if (tvInfo4 == null) {
        break missingId;
      }

      return new ActivityImageCompareMultiBinding((LinearLayout) rootView, btnBack, btnReset,
          btnSyncMode, imageView1, imageView2, imageView3, imageView4, tvInfo1, tvInfo2, tvInfo3,
          tvInfo4);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

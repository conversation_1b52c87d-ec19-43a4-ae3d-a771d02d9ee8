<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 12:51:44 CST 2025 -->
<title>TpVideoSystem</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="declaration: package: com.touptek.video, class: TpVideoSystem">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.touptek.video</a></div>
<h1 title="类 TpVideoSystem" class="title">类 TpVideoSystem</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.touptek.video.TpVideoSystem</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TpVideoSystem</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">TpVideoSystem - ToupTek视频系统类
 <p>
 基于VideoEncoderActivity的成功实现，提供统一的视频预览、录制和图像捕获功能。
 使用TpVideoConfig配置系统，支持灵活的视频参数配置，确保高质量的录制性能。
 采用双层API设计：外层简单API满足80%客户需求，内层专业API满足20%高级需求。
 </p>

 <p><strong>⚠️ 重要：推流功能的正确使用方式</strong></p>
 <p>为了支持RTSP推流功能，必须在Activity的onCreate()方法中创建TpVideoSystem实例：</p>
 <pre><code>
 public class MainActivity extends AppCompatActivity {
     private TpVideoSystem videoSystem;

     @Override
     protected void onCreate(Bundle savedInstanceState) {
         super.onCreate(savedInstanceState);

         // ✅ 正确：在onCreate中创建，支持推流功能
         TpVideoConfig config = TpVideoConfig.createDefault1080P();
         videoSystem = new TpVideoSystem(this, config);

         // 后续在TextureView准备好后调用initialize()
     }
 }
 </code></pre>

 <p><strong>原因：</strong>RTSP推流功能需要注册ActivityResultLauncher来申请屏幕录制权限，
 而Android要求这种注册必须在Activity进入STARTED状态之前完成。</p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static enum&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TpVideoSystem.StreamType.html" class="type-name-link" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a></code></div>
<div class="col-last even-row-color">
<div class="block">流类型枚举</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TpVideoSystem.TpVideoSystemAdapter.html" class="type-name-link" title="com.touptek.video中的类">TpVideoSystem.TpVideoSystemAdapter</a></code></div>
<div class="col-last odd-row-color">
<div class="block">视频系统监听器适配器</div>
</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TpVideoSystem.TpVideoSystemListener.html" class="type-name-link" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">视频系统监听器接口</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(androidx.appcompat.app.AppCompatActivity)" class="member-name-link">TpVideoSystem</a><wbr>(androidx.appcompat.app.AppCompatActivity&nbsp;activity)</code></div>
<div class="col-last even-row-color">
<div class="block">构造函数 - 使用默认1080P配置</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(androidx.appcompat.app.AppCompatActivity,com.touptek.video.TpVideoConfig)" class="member-name-link">TpVideoSystem</a><wbr>(androidx.appcompat.app.AppCompatActivity&nbsp;activity,
 <a href="TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a>&nbsp;config)</code></div>
<div class="col-last odd-row-color">
<div class="block">构造函数 - 使用自定义配置</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#captureImage(java.lang.String)" class="member-name-link">captureImage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">捕获图像</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.touptek.video.internal.TpCameraManager</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCameraManager()" class="member-name-link">getCameraManager</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取相机管理器（高级用户）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentStreamType()" class="member-name-link">getCurrentStreamType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前流类型</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentVideoPath()" class="member-name-link">getCurrentVideoPath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前播放的视频路径（简化版）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentVideoPlaybackSpeed()" class="member-name-link">getCurrentVideoPlaybackSpeed</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前视频的播放速度（简化版）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentVideoPosition()" class="member-name-link">getCurrentVideoPosition</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前播放位置（简化版）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.touptek.video.internal.TpCaptureImage</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImageCapture()" class="member-name-link">getImageCapture</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取图像捕获器（高级用户）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.touptek.video.internal.service.TpStreamingService</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStreamingService()" class="member-name-link">getStreamingService</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取推流服务（高级用户）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStreamUrl()" class="member-name-link">getStreamUrl</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取推流URL</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.touptek.video.internal.TpTvPreview</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTvPreviewHelper()" class="member-name-link">getTvPreviewHelper</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取TV预览助手实例（高级功能）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVideoConfig()" class="member-name-link">getVideoConfig</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前视频配置</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.touptek.video.internal.TpVideoDecoder</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVideoDecoder()" class="member-name-link">getVideoDecoder</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取视频解码器（高级用户）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVideoDuration()" class="member-name-link">getVideoDuration</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取视频总时长（简化版）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.touptek.video.internal.TpVideoEncoder</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVideoEncoder()" class="member-name-link">getVideoEncoder</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取视频编码器（高级用户）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initialize(android.view.Surface)" class="member-name-link">initialize</a><wbr>(android.view.Surface&nbsp;previewSurface)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">初始化视频系统</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCameraStarted()" class="member-name-link">isCameraStarted</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取相机启动状态</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCurrentVideoPlaybackCompleted()" class="member-name-link">isCurrentVideoPlaybackCompleted</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查当前视频播放是否已完成（简化版）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isInitialized()" class="member-name-link">isInitialized</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取初始化状态</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isRecording()" class="member-name-link">isRecording</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取录制状态</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isStreaming()" class="member-name-link">isStreaming</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取推流状态</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isTvMode()" class="member-name-link">isTvMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取当前是否为TV模式</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isVideoPlaying()" class="member-name-link">isVideoPlaying</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查是否正在播放视频（简化版）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadFullImage(java.lang.String,android.widget.ImageView)" class="member-name-link">loadFullImage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">加载高质量图像</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadThumbnail(java.lang.String,android.widget.ImageView)" class="member-name-link">loadThumbnail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">加载图像缩略图</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pauseVideo()" class="member-name-link">pauseVideo</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">暂停视频播放（简化版）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#playVideo(java.lang.String,android.view.Surface)" class="member-name-link">playVideo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;videoPath,
 android.view.Surface&nbsp;surface)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">开始视频播放（简化版）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#release()" class="member-name-link">release</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">释放资源</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#releaseVideo()" class="member-name-link">releaseVideo</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止视频播放（简化版）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resetCurrentVideoToStart()" class="member-name-link">resetCurrentVideoToStart</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">重置当前视频到开始位置（简化版）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resumeVideo()" class="member-name-link">resumeVideo</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">恢复视频播放（简化版）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#seekCurrentVideoRelative(long)" class="member-name-link">seekCurrentVideoRelative</a><wbr>(long&nbsp;deltaMs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">当前视频相对跳转（简化版）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#seekVideoTo(long)" class="member-name-link">seekVideoTo</a><wbr>(long&nbsp;position)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">跳转到指定位置（简化版）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCurrentVideoPlaybackSpeed(float)" class="member-name-link">setCurrentVideoPlaybackSpeed</a><wbr>(float&nbsp;speed)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置当前视频的播放速度（简化版）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setListener(com.touptek.video.TpVideoSystem.TpVideoSystemListener)" class="member-name-link">setListener</a><wbr>(<a href="TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a>&nbsp;listener)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置监听器</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStreamType(com.touptek.video.TpVideoSystem.StreamType)" class="member-name-link">setStreamType</a><wbr>(<a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a>&nbsp;streamType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置推流类型</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTvContainer(android.view.ViewGroup)" class="member-name-link">setTvContainer</a><wbr>(android.view.ViewGroup&nbsp;container)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置TV预览容器</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startRecording(java.lang.String)" class="member-name-link">startRecording</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">开始录制</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startStreaming()" class="member-name-link">startStreaming</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">启动推流（使用默认配置）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startStreaming(com.touptek.video.TpVideoSystem.StreamType)" class="member-name-link">startStreaming</a><wbr>(<a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a>&nbsp;streamType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">启动推流</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startStreaming(com.touptek.video.TpVideoSystem.StreamType,java.lang.String)" class="member-name-link">startStreaming</a><wbr>(<a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a>&nbsp;streamType,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;networkInterface)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">启动推流</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stepCurrentVideoFrame()" class="member-name-link">stepCurrentVideoFrame</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">当前视频逐帧播放（简化版）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopRecording()" class="member-name-link">stopRecording</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止录制</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stopStreaming()" class="member-name-link">stopStreaming</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">停止推流</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#switchToCameraMode()" class="member-name-link">switchToCameraMode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">切换到Camera模式</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#switchToTvMode()" class="member-name-link">switchToTvMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">切换到TV模式</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#updateBitRate(int)" class="member-name-link">updateBitRate</a><wbr>(int&nbsp;bitRate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置比特率</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#updateResolution(int,int)" class="member-name-link">updateResolution</a><wbr>(int&nbsp;width,
 int&nbsp;height)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">设置分辨率</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#updateVideoConfig(com.touptek.video.TpVideoConfig)" class="member-name-link">updateVideoConfig</a><wbr>(<a href="TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a>&nbsp;config)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">更新视频配置（仅在未初始化时允许）</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(androidx.appcompat.app.AppCompatActivity)">
<h3>TpVideoSystem</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TpVideoSystem</span><wbr><span class="parameters">(androidx.appcompat.app.AppCompatActivity&nbsp;activity)</span></div>
<div class="block">构造函数 - 使用默认1080P配置</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>activity</code> - Activity实例（推流功能需要）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(androidx.appcompat.app.AppCompatActivity,com.touptek.video.TpVideoConfig)">
<h3>TpVideoSystem</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TpVideoSystem</span><wbr><span class="parameters">(androidx.appcompat.app.AppCompatActivity&nbsp;activity,
 <a href="TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a>&nbsp;config)</span></div>
<div class="block">构造函数 - 使用自定义配置</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>activity</code> - Activity实例（推流功能需要）</dd>
<dd><code>config</code> - 视频配置</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="setListener(com.touptek.video.TpVideoSystem.TpVideoSystemListener)">
<h3>setListener</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setListener</span><wbr><span class="parameters">(<a href="TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a>&nbsp;listener)</span></div>
<div class="block">设置监听器</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>listener</code> - 监听器</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initialize(android.view.Surface)">
<h3>initialize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initialize</span><wbr><span class="parameters">(android.view.Surface&nbsp;previewSurface)</span></div>
<div class="block">初始化视频系统</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>previewSurface</code> - 预览Surface</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startRecording(java.lang.String)">
<h3>startRecording</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startRecording</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputPath)</span></div>
<div class="block">开始录制</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>outputPath</code> - 输出路径，如果为null则使用默认路径</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stopRecording()">
<h3>stopRecording</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopRecording</span>()</div>
<div class="block">停止录制</div>
</section>
</li>
<li>
<section class="detail" id="captureImage(java.lang.String)">
<h3>captureImage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">captureImage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputPath)</span></div>
<div class="block">捕获图像</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>outputPath</code> - 输出路径，如果为null则使用默认路径
  captureImage方法会根据路径的后缀名抓取对应格式的图片。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isRecording()">
<h3>isRecording</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isRecording</span>()</div>
<div class="block">获取录制状态</div>
</section>
</li>
<li>
<section class="detail" id="isCameraStarted()">
<h3>isCameraStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCameraStarted</span>()</div>
<div class="block">获取相机启动状态</div>
</section>
</li>
<li>
<section class="detail" id="isInitialized()">
<h3>isInitialized</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isInitialized</span>()</div>
<div class="block">获取初始化状态</div>
</section>
</li>
<li>
<section class="detail" id="loadThumbnail(java.lang.String,android.widget.ImageView)">
<h3>loadThumbnail</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">loadThumbnail</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView)</span></div>
<div class="block">加载图像缩略图
 <p>用于列表显示，小尺寸、低质量，节省内存和加载时间</p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>imagePath</code> - 图像或视频文件路径</dd>
<dd><code>imageView</code> - 目标ImageView</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadFullImage(java.lang.String,android.widget.ImageView)">
<h3>loadFullImage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">loadFullImage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;imagePath,
 android.widget.ImageView&nbsp;imageView)</span></div>
<div class="block">加载高质量图像
 <p>用于详细查看，高质量显示</p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>imagePath</code> - 图像文件路径</dd>
<dd><code>imageView</code> - 目标ImageView</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVideoConfig()">
<h3>getVideoConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a></span>&nbsp;<span class="element-name">getVideoConfig</span>()</div>
<div class="block">获取当前视频配置</div>
</section>
</li>
<li>
<section class="detail" id="updateVideoConfig(com.touptek.video.TpVideoConfig)">
<h3>updateVideoConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">updateVideoConfig</span><wbr><span class="parameters">(<a href="TpVideoConfig.html" title="com.touptek.video中的类">TpVideoConfig</a>&nbsp;config)</span>
                       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalStateException.html" title="java.lang中的类或接口" class="external-link">IllegalStateException</a></span></div>
<div class="block">更新视频配置（仅在未初始化时允许）</div>
<dl class="notes">
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalStateException.html" title="java.lang中的类或接口" class="external-link">IllegalStateException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="updateBitRate(int)">
<h3>updateBitRate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">updateBitRate</span><wbr><span class="parameters">(int&nbsp;bitRate)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalArgumentException.html" title="java.lang中的类或接口" class="external-link">IllegalArgumentException</a></span></div>
<div class="block">设置比特率
 <p>
 设置视频比特率。支持初始化前配置和运行时动态调整。
 运行时调整适用于根据网络状况或存储需求动态调整视频质量。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>bitRate</code> - 比特率（bps），例如：8_000_000 表示8Mbps</dd>
<dt>返回:</dt>
<dd>是否成功设置比特率</dd>
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalArgumentException.html" title="java.lang中的类或接口" class="external-link">IllegalArgumentException</a></code> - 如果比特率无效（必须大于0）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="updateResolution(int,int)">
<h3>updateResolution</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">updateResolution</span><wbr><span class="parameters">(int&nbsp;width,
 int&nbsp;height)</span></div>
<div class="block">设置分辨率
 <p>
 通过HDMI重置机制实现分辨率动态调整。此方法会：
 <ul>
 <li>保存新的分辨率配置</li>
 <li>执行HDMI重置（模拟拔出再插入）</li>
 <li>在HDMI重新连接时自动应用新分辨率</li>
 </ul>

 <p><b>⚠️ 注意事项：</b></p>
 <ul>
 <li>此操作会短暂中断视频预览（约1-2秒）</li>
 <li>如果正在录制，会自动停止录制</li>
 <li>如果正在推流，会自动停止推流</li>
 <li>操作完成后需要重新开始录制或推流</li>
 </ul></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>width</code> - 新的视频宽度</dd>
<dd><code>height</code> - 新的视频高度</dd>
<dt>返回:</dt>
<dd>是否成功启动分辨率调整流程</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startStreaming()">
<h3>startStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">startStreaming</span>()</div>
<div class="block">启动推流（使用默认配置）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>true表示启动成功</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startStreaming(com.touptek.video.TpVideoSystem.StreamType)">
<h3>startStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">startStreaming</span><wbr><span class="parameters">(<a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a>&nbsp;streamType)</span></div>
<div class="block">启动推流</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>streamType</code> - 流类型（CAMERA或SCREEN）</dd>
<dt>返回:</dt>
<dd>true表示启动成功</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startStreaming(com.touptek.video.TpVideoSystem.StreamType,java.lang.String)">
<h3>startStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">startStreaming</span><wbr><span class="parameters">(<a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a>&nbsp;streamType,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;networkInterface)</span></div>
<div class="block">启动推流</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>streamType</code> - 流类型</dd>
<dd><code>networkInterface</code> - 网络接口（null使用默认）</dd>
<dt>返回:</dt>
<dd>true表示启动成功</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stopStreaming()">
<h3>stopStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">stopStreaming</span>()</div>
<div class="block">停止推流</div>
<dl class="notes">
<dt>返回:</dt>
<dd>true表示停止成功</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isStreaming()">
<h3>isStreaming</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isStreaming</span>()</div>
<div class="block">获取推流状态</div>
</section>
</li>
<li>
<section class="detail" id="getStreamUrl()">
<h3>getStreamUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getStreamUrl</span>()</div>
<div class="block">获取推流URL</div>
<dl class="notes">
<dt>返回:</dt>
<dd>推流URL，如果未推流则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentStreamType()">
<h3>getCurrentStreamType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a></span>&nbsp;<span class="element-name">getCurrentStreamType</span>()</div>
<div class="block">获取当前流类型</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前流类型</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStreamType(com.touptek.video.TpVideoSystem.StreamType)">
<h3>setStreamType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStreamType</span><wbr><span class="parameters">(<a href="TpVideoSystem.StreamType.html" title="enum class in com.touptek.video">TpVideoSystem.StreamType</a>&nbsp;streamType)</span></div>
<div class="block">设置推流类型</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>streamType</code> - 流类型（CAMERA或SCREEN）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="switchToTvMode()">
<h3>switchToTvMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">switchToTvMode</span>()
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalStateException.html" title="java.lang中的类或接口" class="external-link">IllegalStateException</a></span></div>
<div class="block">切换到TV模式
 <p>
 切换到TV模式后，将停止相机预览并启动TV预览。
 在TV模式下，录制和图像捕获功能将被禁用。
 </p></div>
<dl class="notes">
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalStateException.html" title="java.lang中的类或接口" class="external-link">IllegalStateException</a></code> - 如果当前状态不允许切换（如正在录制）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="switchToCameraMode()">
<h3>switchToCameraMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">switchToCameraMode</span>()
                        throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalStateException.html" title="java.lang中的类或接口" class="external-link">IllegalStateException</a></span></div>
<div class="block">切换到Camera模式
 <p>
 切换到Camera模式后，将停止TV预览并启动相机预览。
 在Camera模式下，录制和图像捕获功能将被启用。
 </p></div>
<dl class="notes">
<dt>抛出:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/IllegalStateException.html" title="java.lang中的类或接口" class="external-link">IllegalStateException</a></code> - 如果当前状态不允许切换</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isTvMode()">
<h3>isTvMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isTvMode</span>()</div>
<div class="block">获取当前是否为TV模式</div>
<dl class="notes">
<dt>返回:</dt>
<dd>true表示TV模式，false表示Camera模式</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTvContainer(android.view.ViewGroup)">
<h3>setTvContainer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTvContainer</span><wbr><span class="parameters">(android.view.ViewGroup&nbsp;container)</span></div>
<div class="block">设置TV预览容器
 <p>
 必须在使用TV模式功能之前调用此方法设置TV预览的容器视图。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>container</code> - TV预览容器视图</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="playVideo(java.lang.String,android.view.Surface)">
<h3>playVideo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">playVideo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;videoPath,
 android.view.Surface&nbsp;surface)</span></div>
<div class="block">开始视频播放（简化版）
 <p>
 提供简化的视频播放接口，内部自动管理VideoDecoder实例。
 如果已有视频在播放，会先停止当前播放再开始新的播放。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>videoPath</code> - 视频文件路径</dd>
<dd><code>surface</code> - 播放表面</dd>
<dt>返回:</dt>
<dd>是否成功开始播放</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="pauseVideo()">
<h3>pauseVideo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pauseVideo</span>()</div>
<div class="block">暂停视频播放（简化版）</div>
</section>
</li>
<li>
<section class="detail" id="resumeVideo()">
<h3>resumeVideo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resumeVideo</span>()</div>
<div class="block">恢复视频播放（简化版）</div>
</section>
</li>
<li>
<section class="detail" id="releaseVideo()">
<h3>releaseVideo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">releaseVideo</span>()</div>
<div class="block">停止视频播放（简化版）</div>
</section>
</li>
<li>
<section class="detail" id="seekVideoTo(long)">
<h3>seekVideoTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">seekVideoTo</span><wbr><span class="parameters">(long&nbsp;position)</span></div>
<div class="block">跳转到指定位置（简化版）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>position</code> - 目标位置（毫秒）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isVideoPlaying()">
<h3>isVideoPlaying</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isVideoPlaying</span>()</div>
<div class="block">检查是否正在播放视频（简化版）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>true表示正在播放</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentVideoPosition()">
<h3>getCurrentVideoPosition</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getCurrentVideoPosition</span>()</div>
<div class="block">获取当前播放位置（简化版）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前位置（毫秒）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVideoDuration()">
<h3>getVideoDuration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getVideoDuration</span>()</div>
<div class="block">获取视频总时长（简化版）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>视频总时长（毫秒）</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentVideoPath()">
<h3>getCurrentVideoPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getCurrentVideoPath</span>()</div>
<div class="block">获取当前播放的视频路径（简化版）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前视频路径，如果没有播放则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCurrentVideoPlaybackSpeed(float)">
<h3>setCurrentVideoPlaybackSpeed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">setCurrentVideoPlaybackSpeed</span><wbr><span class="parameters">(float&nbsp;speed)</span></div>
<div class="block">设置当前视频的播放速度（简化版）
 <p>
 为当前正在播放的视频设置播放速度。支持变速播放功能，
 常用于快速浏览或慢动作分析。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>speed</code> - 播放速度倍率（0.25f - 2.0f），1.0f表示正常速度</dd>
<dt>返回:</dt>
<dd>是否设置成功</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentVideoPlaybackSpeed()">
<h3>getCurrentVideoPlaybackSpeed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getCurrentVideoPlaybackSpeed</span>()</div>
<div class="block">获取当前视频的播放速度（简化版）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前播放速度倍率，如果没有视频播放则返回1.0f</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stepCurrentVideoFrame()">
<h3>stepCurrentVideoFrame</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">stepCurrentVideoFrame</span>()</div>
<div class="block">当前视频逐帧播放（简化版）
 <p>
 解码并显示当前视频的下一帧，然后暂停。
 适用于精确的帧分析。
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>是否执行成功</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="seekCurrentVideoRelative(long)">
<h3>seekCurrentVideoRelative</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">seekCurrentVideoRelative</span><wbr><span class="parameters">(long&nbsp;deltaMs)</span></div>
<div class="block">当前视频相对跳转（简化版）
 <p>
 基于当前播放位置进行时间偏移跳转。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>deltaMs</code> - 时间偏移量（毫秒），正数前进，负数后退</dd>
<dt>返回:</dt>
<dd>是否跳转成功</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isCurrentVideoPlaybackCompleted()">
<h3>isCurrentVideoPlaybackCompleted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCurrentVideoPlaybackCompleted</span>()</div>
<div class="block">检查当前视频播放是否已完成（简化版）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>true表示播放完成</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resetCurrentVideoToStart()">
<h3>resetCurrentVideoToStart</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">resetCurrentVideoToStart</span>()</div>
<div class="block">重置当前视频到开始位置（简化版）
 <p>
 将当前视频的播放位置重置到开头。
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>是否重置成功</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVideoEncoder()">
<h3>getVideoEncoder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">com.touptek.video.internal.TpVideoEncoder</span>&nbsp;<span class="element-name">getVideoEncoder</span>()</div>
<div class="block">获取视频编码器（高级用户）
 <p>⚠️ 高级用户专用API</p>
 <p>提供对VideoEncoder的直接访问，用于高级视频编码控制。
 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简单API。</p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>VideoEncoder实例，如果未初始化则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCameraManager()">
<h3>getCameraManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">com.touptek.video.internal.TpCameraManager</span>&nbsp;<span class="element-name">getCameraManager</span>()</div>
<div class="block">获取相机管理器（高级用户）
 <p>⚠️ 高级用户专用API</p>
 <p>提供对CameraManagerHelper的直接访问，用于高级相机控制和参数调整。
 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简单API。</p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>CameraManagerHelper实例，如果未初始化则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImageCapture()">
<h3>getImageCapture</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">com.touptek.video.internal.TpCaptureImage</span>&nbsp;<span class="element-name">getImageCapture</span>()</div>
<div class="block">获取图像捕获器（高级用户）
 <p>⚠️ 高级用户专用API</p>
 <p>提供对CaptureImageHelper的直接访问，用于高级图像捕获控制。
 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简单API。</p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>CaptureImageHelper实例，如果未初始化则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVideoDecoder()">
<h3>getVideoDecoder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">com.touptek.video.internal.TpVideoDecoder</span>&nbsp;<span class="element-name">getVideoDecoder</span>()</div>
<div class="block">获取视频解码器（高级用户）
 <p>⚠️ 高级用户专用API</p>
 <p>提供对当前VideoDecoder的直接访问，用于高级播放控制。
 此方法返回通过playVideo()方法创建的当前VideoDecoder实例。
 高级用户可以通过此实例进行精确的播放控制，如变速播放、逐帧播放、精确跳转等。
 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简化API。</p>

 <p><b>使用示例：</b></p>
 <pre><code>
 // 先通过简化API创建播放器
 videoSystem.playVideo(videoPath, surface);

 // 高级用户获取VideoDecoder进行精确控制
 TpVideoDecoder decoder = videoSystem.getVideoDecoder();
 if (decoder != null) {
     decoder.setPlaybackSpeed(1.5f);        // 1.5倍速播放
     decoder.stepFrame();                    // 逐帧播放
     decoder.seekRelative(5000);             // 相对跳转5秒
     decoder.seekTo(30000000);               // 跳转到30秒位置

     // 状态查询
     boolean isPaused = decoder.isPaused();
     long duration = decoder.getVideoDuration();
     long position = decoder.getCurrentPosition();
 }
 </code></pre></div>
<dl class="notes">
<dt>返回:</dt>
<dd>当前通过playVideo()创建的VideoDecoder实例，如果没有则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStreamingService()">
<h3>getStreamingService</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">com.touptek.video.internal.service.TpStreamingService</span>&nbsp;<span class="element-name">getStreamingService</span>()</div>
<div class="block">获取推流服务（高级用户）
 <p>⚠️ 高级用户专用API</p>
 <p>提供对StreamingService的直接访问，用于高级推流控制。
 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简单API。</p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>StreamingService实例，如果未初始化则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTvPreviewHelper()">
<h3>getTvPreviewHelper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">com.touptek.video.internal.TpTvPreview</span>&nbsp;<span class="element-name">getTvPreviewHelper</span>()</div>
<div class="block">获取TV预览助手实例（高级功能）
 <p>
 此方法用于高级用户直接访问TV预览助手进行深度定制。
 如果TV预览助手尚未初始化，此方法会自动初始化它。
 </p></div>
<dl class="notes">
<dt>返回:</dt>
<dd>TvPreviewHelper实例，如果TV容器未设置则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="release()">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">release</span>()</div>
<div class="block">释放资源</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>

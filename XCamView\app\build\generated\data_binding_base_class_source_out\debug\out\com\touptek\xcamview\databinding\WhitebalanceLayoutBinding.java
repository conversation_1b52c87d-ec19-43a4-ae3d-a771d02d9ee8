// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class WhitebalanceLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBlueAdd;

  @NonNull
  public final ImageButton btnBlueReduce;

  @NonNull
  public final Button btnDefaultWb;

  @NonNull
  public final ImageButton btnGreenAdd;

  @NonNull
  public final ImageButton btnGreenReduce;

  @NonNull
  public final ImageButton btnRedAdd;

  @NonNull
  public final ImageButton btnRedReduce;

  @NonNull
  public final RadioButton radioAutoTv;

  @NonNull
  public final RadioButton radioManualTv;

  @NonNull
  public final RadioButton radioRoiTv;

  @NonNull
  public final SeekBar seekbarBlueTv;

  @NonNull
  public final SeekBar seekbarGreenTv;

  @NonNull
  public final SeekBar seekbarRedTv;

  @NonNull
  public final TextView textBlueValue;

  @NonNull
  public final TextView textGreenValue;

  @NonNull
  public final TextView textRedValue;

  @NonNull
  public final RadioGroup wbBtnGroup;

  private WhitebalanceLayoutBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnBlueAdd,
      @NonNull ImageButton btnBlueReduce, @NonNull Button btnDefaultWb,
      @NonNull ImageButton btnGreenAdd, @NonNull ImageButton btnGreenReduce,
      @NonNull ImageButton btnRedAdd, @NonNull ImageButton btnRedReduce,
      @NonNull RadioButton radioAutoTv, @NonNull RadioButton radioManualTv,
      @NonNull RadioButton radioRoiTv, @NonNull SeekBar seekbarBlueTv,
      @NonNull SeekBar seekbarGreenTv, @NonNull SeekBar seekbarRedTv,
      @NonNull TextView textBlueValue, @NonNull TextView textGreenValue,
      @NonNull TextView textRedValue, @NonNull RadioGroup wbBtnGroup) {
    this.rootView = rootView;
    this.btnBlueAdd = btnBlueAdd;
    this.btnBlueReduce = btnBlueReduce;
    this.btnDefaultWb = btnDefaultWb;
    this.btnGreenAdd = btnGreenAdd;
    this.btnGreenReduce = btnGreenReduce;
    this.btnRedAdd = btnRedAdd;
    this.btnRedReduce = btnRedReduce;
    this.radioAutoTv = radioAutoTv;
    this.radioManualTv = radioManualTv;
    this.radioRoiTv = radioRoiTv;
    this.seekbarBlueTv = seekbarBlueTv;
    this.seekbarGreenTv = seekbarGreenTv;
    this.seekbarRedTv = seekbarRedTv;
    this.textBlueValue = textBlueValue;
    this.textGreenValue = textGreenValue;
    this.textRedValue = textRedValue;
    this.wbBtnGroup = wbBtnGroup;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static WhitebalanceLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static WhitebalanceLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.whitebalance_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static WhitebalanceLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_blue_add;
      ImageButton btnBlueAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnBlueAdd == null) {
        break missingId;
      }

      id = R.id.btn_blue_reduce;
      ImageButton btnBlueReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnBlueReduce == null) {
        break missingId;
      }

      id = R.id.btn_Default_wb;
      Button btnDefaultWb = ViewBindings.findChildViewById(rootView, id);
      if (btnDefaultWb == null) {
        break missingId;
      }

      id = R.id.btn_green_add;
      ImageButton btnGreenAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnGreenAdd == null) {
        break missingId;
      }

      id = R.id.btn_green_reduce;
      ImageButton btnGreenReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnGreenReduce == null) {
        break missingId;
      }

      id = R.id.btn_red_add;
      ImageButton btnRedAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnRedAdd == null) {
        break missingId;
      }

      id = R.id.btn_red_reduce;
      ImageButton btnRedReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnRedReduce == null) {
        break missingId;
      }

      id = R.id.radio_auto_tv;
      RadioButton radioAutoTv = ViewBindings.findChildViewById(rootView, id);
      if (radioAutoTv == null) {
        break missingId;
      }

      id = R.id.radio_manual_tv;
      RadioButton radioManualTv = ViewBindings.findChildViewById(rootView, id);
      if (radioManualTv == null) {
        break missingId;
      }

      id = R.id.radio_roi_tv;
      RadioButton radioRoiTv = ViewBindings.findChildViewById(rootView, id);
      if (radioRoiTv == null) {
        break missingId;
      }

      id = R.id.seekbar_blue_tv;
      SeekBar seekbarBlueTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarBlueTv == null) {
        break missingId;
      }

      id = R.id.seekbar_green_tv;
      SeekBar seekbarGreenTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarGreenTv == null) {
        break missingId;
      }

      id = R.id.seekbar_red_tv;
      SeekBar seekbarRedTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarRedTv == null) {
        break missingId;
      }

      id = R.id.text_blue_value;
      TextView textBlueValue = ViewBindings.findChildViewById(rootView, id);
      if (textBlueValue == null) {
        break missingId;
      }

      id = R.id.text_green_value;
      TextView textGreenValue = ViewBindings.findChildViewById(rootView, id);
      if (textGreenValue == null) {
        break missingId;
      }

      id = R.id.text_red_value;
      TextView textRedValue = ViewBindings.findChildViewById(rootView, id);
      if (textRedValue == null) {
        break missingId;
      }

      id = R.id.wb_btn_group;
      RadioGroup wbBtnGroup = ViewBindings.findChildViewById(rootView, id);
      if (wbBtnGroup == null) {
        break missingId;
      }

      return new WhitebalanceLayoutBinding((LinearLayout) rootView, btnBlueAdd, btnBlueReduce,
          btnDefaultWb, btnGreenAdd, btnGreenReduce, btnRedAdd, btnRedReduce, radioAutoTv,
          radioManualTv, radioRoiTv, seekbarBlueTv, seekbarGreenTv, seekbarRedTv, textBlueValue,
          textGreenValue, textRedValue, wbBtnGroup);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.touptek.xcamview"
    android:sharedUserId="android.uid.system"
    android:versionCode="5002"
    android:versionName="14" >

    <uses-sdk
        android:minSdkVersion="31"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- 适用于Android 11及以上 -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <permission
        android:name="com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="XCamView"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" >

        <!-- android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" -->
        <!-- android:theme="@style/Theme.AppCompat.DayNight" -->


        <!-- 设置MainActivity为启动Activity -->
        <activity
            android:name="com.touptek.xcamview.activity.MainActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:screenOrientation="unspecified"
            android:supportsPictureInPicture="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name="com.touptek.xcamview.activity.videomanagement.TpVideoEncoderActivity" />
        <activity android:name="com.touptek.xcamview.activity.videomanagement.TpVideoDecoderActivity" />
        <activity android:name="com.touptek.xcamview.activity.browse.TpVideoBrowse" />
        <activity
            android:name="com.touptek.xcamview.activity.compare.TpImageCompareActivity"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <activity
            android:name="com.touptek.xcamview.activity.compare.TpImageCompareMultiActivity"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.touptek.xcamview.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
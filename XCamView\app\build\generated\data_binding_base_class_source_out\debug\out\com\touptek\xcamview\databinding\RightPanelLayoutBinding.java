// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class RightPanelLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnCreateFolder;

  @NonNull
  public final RecyclerView rvFolderList;

  @NonNull
  public final TextView tvFolderTitle;

  private RightPanelLayoutBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnBack,
      @NonNull ImageButton btnCreateFolder, @NonNull RecyclerView rvFolderList,
      @NonNull TextView tvFolderTitle) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnCreateFolder = btnCreateFolder;
    this.rvFolderList = rvFolderList;
    this.tvFolderTitle = tvFolderTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static RightPanelLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static RightPanelLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.right_panel_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static RightPanelLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_create_folder;
      ImageButton btnCreateFolder = ViewBindings.findChildViewById(rootView, id);
      if (btnCreateFolder == null) {
        break missingId;
      }

      id = R.id.rv_folder_list;
      RecyclerView rvFolderList = ViewBindings.findChildViewById(rootView, id);
      if (rvFolderList == null) {
        break missingId;
      }

      id = R.id.tv_folder_title;
      TextView tvFolderTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvFolderTitle == null) {
        break missingId;
      }

      return new RightPanelLayoutBinding((LinearLayout) rootView, btnBack, btnCreateFolder,
          rvFolderList, tvFolderTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

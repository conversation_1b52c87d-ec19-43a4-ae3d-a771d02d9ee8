<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>O - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="index: O">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a>
<h2 class="title" id="I:O">O</h2>
<dl class="index">
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onCameraStarted()" class="member-name-link">onCameraStarted()</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">相机启动完成</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#onDataChanged(com.touptek.video.TpIspParam,int)" class="member-name-link">onDataChanged(TpIspParam, int)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">处理参数变化事件</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.OnDataChangedListener.html#onDataChanged(com.touptek.video.TpIspParam,int)" class="member-name-link">onDataChanged(TpIspParam, int)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.OnDataChangedListener.html" title="com.touptek.video中的接口">TpIspParam.OnDataChangedListener</a></dt>
<dd>
<div class="block">当 int 类型数据发生变化时调用。</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#onDetachedFromWindow()" class="member-name-link">onDetachedFromWindow()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">视图从窗口分离时调用，用于清理资源</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.html#onDetachedFromWindow()" class="member-name-link">onDetachedFromWindow()</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.html" title="com.touptek.ui中的类">TpVideoPlayerView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.DirectoryListListener.html#onDirectoriesLoaded(java.util.List)" class="member-name-link">onDirectoriesLoaded(List&lt;String&gt;)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.DirectoryListListener.html" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.DirectoryListListener.html#onDirectoryLoadFailed(java.lang.String)" class="member-name-link">onDirectoryLoadFailed(String)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.DirectoryListListener.html" title="com.touptek.utils中的接口">TpSambaClient.DirectoryListListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#onDraw(android.graphics.Canvas)" class="member-name-link">onDraw(Canvas)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">绘制视图</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemAdapter.html#onError(java.lang.String)" class="member-name-link">onError(String)</a> - 类中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemAdapter.html" title="com.touptek.video中的类">TpVideoSystem.TpVideoSystemAdapter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onError(java.lang.String)" class="member-name-link">onError(String)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">发生错误（核心方法，建议实现）</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html#onEthernetStateChanged(boolean)" class="member-name-link">onEthernetStateChanged(boolean)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html" title="com.touptek.utils中的接口">TpNetworkMonitor.NetworkStateListener</a></dt>
<dd>
<div class="block">当以太网状态发生变化时调用</div>
</dd>
<dt><a href="../com/touptek/utils/TpHdmiMonitor.HdmiListener.html#onHdmiStatusChanged(boolean)" class="member-name-link">onHdmiStatusChanged(boolean)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpHdmiMonitor.HdmiListener.html" title="com.touptek.utils中的接口">TpHdmiMonitor.HdmiListener</a></dt>
<dd>
<div class="block">当 HDMI 状态发生变化时调用。</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html#onHotspotStateChanged(boolean,java.lang.String)" class="member-name-link">onHotspotStateChanged(boolean, String)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html" title="com.touptek.utils中的接口">TpNetworkMonitor.NetworkStateListener</a></dt>
<dd>
<div class="block">当热点状态发生变化时调用</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onImageCaptured(java.lang.String)" class="member-name-link">onImageCaptured(String)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">图像捕获完成</div>
</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#onLongDataChanged(com.touptek.video.TpIspParam,long)" class="member-name-link">onLongDataChanged(TpIspParam, long)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">处理长整型参数变化事件</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.OnDataChangedListener.html#onLongDataChanged(com.touptek.video.TpIspParam,long)" class="member-name-link">onLongDataChanged(TpIspParam, long)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.OnDataChangedListener.html" title="com.touptek.video中的接口">TpIspParam.OnDataChangedListener</a></dt>
<dd>
<div class="block">当 long 类型数据发生变化时调用。</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html#onLongPressDetected(android.view.MotionEvent)" class="member-name-link">onLongPressDetected(MotionEvent)</a> - 接口中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html" title="com.touptek.ui中的接口">TpTextureView.TouchEventHandler</a></dt>
<dd>
<div class="block">通知长按手势</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.VideoPlayerListener.html#onNextVideo()" class="member-name-link">onNextVideo()</a> - 接口中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.VideoPlayerListener.html" title="com.touptek.ui中的接口">TpVideoPlayerView.VideoPlayerListener</a></dt>
<dd>
<div class="block">请求播放下一个视频</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html#onPanGestureDetected(android.view.MotionEvent)" class="member-name-link">onPanGestureDetected(MotionEvent)</a> - 接口中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html" title="com.touptek.ui中的接口">TpTextureView.TouchEventHandler</a></dt>
<dd>
<div class="block">通知平移手势开始</div>
</dd>
<dt><a href="../com/touptek/ui/TpVideoPlayerView.VideoPlayerListener.html#onPreviousVideo()" class="member-name-link">onPreviousVideo()</a> - 接口中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpVideoPlayerView.VideoPlayerListener.html" title="com.touptek.ui中的接口">TpVideoPlayerView.VideoPlayerListener</a></dt>
<dd>
<div class="block">请求播放上一个视频</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onRecordingStarted(java.lang.String)" class="member-name-link">onRecordingStarted(String)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">录制开始</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onRecordingStopped(java.lang.String)" class="member-name-link">onRecordingStopped(String)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">录制停止</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html#onScaleGestureDetected(android.view.MotionEvent)" class="member-name-link">onScaleGestureDetected(MotionEvent)</a> - 接口中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html" title="com.touptek.ui中的接口">TpTextureView.TouchEventHandler</a></dt>
<dd>
<div class="block">通知缩放手势开始</div>
</dd>
<dt><a href="../com/touptek/video/TpIspParam.OnSerialStateChangedListener.html#onSerialStateChanged(boolean)" class="member-name-link">onSerialStateChanged(boolean)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpIspParam.OnSerialStateChangedListener.html" title="com.touptek.video中的接口">TpIspParam.OnSerialStateChangedListener</a></dt>
<dd>
<div class="block">当串口状态发生变化时调用。</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html#onSingleTapDetected(android.view.MotionEvent)" class="member-name-link">onSingleTapDetected(MotionEvent)</a> - 接口中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.TouchEventHandler.html" title="com.touptek.ui中的接口">TpTextureView.TouchEventHandler</a></dt>
<dd>
<div class="block">通知单击手势</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#onSizeChanged(int,int,int,int)" class="member-name-link">onSizeChanged(int, int, int, int)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#onSizeChanged(int,int,int,int)" class="member-name-link">onSizeChanged(int, int, int, int)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">视图大小变化时调用</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onStreamingStatusChanged(boolean,java.lang.String)" class="member-name-link">onStreamingStatusChanged(boolean, String)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">推流状态变化回调</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.html#onTouchEvent(android.view.MotionEvent)" class="member-name-link">onTouchEvent(MotionEvent)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.html" title="com.touptek.ui中的类">TpImageView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpRoiView.html#onTouchEvent(android.view.MotionEvent)" class="member-name-link">onTouchEvent(MotionEvent)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpRoiView.html" title="com.touptek.ui中的类">TpRoiView</a></dt>
<dd>
<div class="block">处理触摸事件</div>
</dd>
<dt><a href="../com/touptek/ui/TpTextureView.html#onTouchEvent(android.view.MotionEvent)" class="member-name-link">onTouchEvent(MotionEvent)</a> - 类中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.html" title="com.touptek.ui中的类">TpTextureView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.UploadListener.html#onUploadFailed(java.lang.String)" class="member-name-link">onUploadFailed(String)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpSambaClient.UploadListener.html#onUploadSuccess(java.lang.String)" class="member-name-link">onUploadSuccess(String)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpSambaClient.UploadListener.html" title="com.touptek.utils中的接口">TpSambaClient.UploadListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpFileManager.StorageListener.html#onUsbDriveConnected(java.lang.String)" class="member-name-link">onUsbDriveConnected(String)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.StorageListener.html" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a></dt>
<dd>
<div class="block">当U盘插入时调用。</div>
</dd>
<dt><a href="../com/touptek/utils/TpFileManager.StorageListener.html#onUsbDriveDisconnected(java.lang.String)" class="member-name-link">onUsbDriveDisconnected(String)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpFileManager.StorageListener.html" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a></dt>
<dd>
<div class="block">当U盘拔出时调用。</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onVideoPlaybackCompleted(java.lang.String)" class="member-name-link">onVideoPlaybackCompleted(String)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">视频播放完成回调</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onVideoPlaybackStarted(java.lang.String)" class="member-name-link">onVideoPlaybackStarted(String)</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">视频播放开始回调</div>
</dd>
<dt><a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html#onVideoPlaybackStopped()" class="member-name-link">onVideoPlaybackStopped()</a> - 接口中的方法 com.touptek.video.<a href="../com/touptek/video/TpVideoSystem.TpVideoSystemListener.html" title="com.touptek.video中的接口">TpVideoSystem.TpVideoSystemListener</a></dt>
<dd>
<div class="block">视频播放停止回调</div>
</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html#onWifiStateChanged(boolean,java.lang.String)" class="member-name-link">onWifiStateChanged(boolean, String)</a> - 接口中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.NetworkStateListener.html" title="com.touptek.utils中的接口">TpNetworkMonitor.NetworkStateListener</a></dt>
<dd>
<div class="block">当WiFi状态发生变化时调用</div>
</dd>
<dt><a href="../com/touptek/ui/TpImageView.OnZoomChangeListener.html#onZoomChanged(float,float,float)" class="member-name-link">onZoomChanged(float, float, float)</a> - 接口中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpImageView.OnZoomChangeListener.html" title="com.touptek.ui中的接口">TpImageView.OnZoomChangeListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/ui/TpTextureView.OnZoomChangeListener.html#onZoomChanged(float,float,float)" class="member-name-link">onZoomChanged(float, float, float)</a> - 接口中的方法 com.touptek.ui.<a href="../com/touptek/ui/TpTextureView.OnZoomChangeListener.html" title="com.touptek.ui中的接口">TpTextureView.OnZoomChangeListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/touptek/utils/TpNetworkMonitor.html#openHotspotSettings()" class="member-name-link">openHotspotSettings()</a> - 类中的方法 com.touptek.utils.<a href="../com/touptek/utils/TpNetworkMonitor.html" title="com.touptek.utils中的类">TpNetworkMonitor</a></dt>
<dd>
<div class="block">打开热点设置页面</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">F</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">R</a>&nbsp;<a href="index-15.html">S</a>&nbsp;<a href="index-16.html">T</a>&nbsp;<a href="index-17.html">U</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="../allpackages-index.html">所有程序包</a></main>
</div>
</div>
</body>
</html>

package com.touptek.xcamview.activity.compare

import android.content.Context
import android.content.Intent
import android.graphics.Matrix
import android.graphics.PointF
import android.os.Bundle
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.touptek.xcamview.R
import com.touptek.video.TpVideoSystem
import com.touptek.video.TpVideoConfig
import com.touptek.measurerealize.TpImageView
import java.io.File

class TpImageCompareActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TpImageCompareActivity"
        const val EXTRA_LEFT_IMAGE = "left_image"
        const val EXTRA_RIGHT_IMAGE = "right_image"
        
        fun start(context: Context, leftImagePath: String, rightImagePath: String) {
            val intent = Intent(context, TpImageCompareActivity::class.java).apply {
                putExtra(EXTRA_LEFT_IMAGE, leftImagePath)
                putExtra(EXTRA_RIGHT_IMAGE, rightImagePath)
            }
            context.startActivity(intent)
        }
    }
    
    // UI组件
    private lateinit var leftImageView: TpImageView
    private lateinit var rightImageView: TpImageView
    private lateinit var btnBack: ImageButton
    private lateinit var btnSync: ImageButton
    private lateinit var btnReset: ImageButton
    private lateinit var btnSwap: ImageButton
    private lateinit var tvLeftInfo: TextView
    private lateinit var tvRightInfo: TextView
    
    // 图片路径
    private var leftImagePath: String? = null
    private var rightImagePath: String? = null

    // 同步状态
    private var isSyncEnabled = true
    private var isUpdating = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_image_compare)
        
        initViews()
        getImagePaths()
        loadImages()
        updateImageInfo()
        setupClickListeners()
        setupMatrixSync()
    }
    
    private fun initViews() {
        leftImageView = findViewById(R.id.left_image)
        rightImageView = findViewById(R.id.right_image)
        btnBack = findViewById(R.id.btn_back)
        btnSync = findViewById(R.id.btn_sync)
        btnReset = findViewById(R.id.btn_reset)
        btnSwap = findViewById(R.id.btn_swap)
        tvLeftInfo = findViewById(R.id.tv_left_info)
        tvRightInfo = findViewById(R.id.tv_right_info)

        // 初始化同步按钮状态
        updateSyncButtonState()
    }
    
    private fun getImagePaths() {
        leftImagePath = intent.getStringExtra(EXTRA_LEFT_IMAGE)
        rightImagePath = intent.getStringExtra(EXTRA_RIGHT_IMAGE)
        
        if (leftImagePath == null || rightImagePath == null) {
            Toast.makeText(this, "图片路径无效", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        // 更新底部信息显示
        updateImageInfo()
    }
    
    private fun updateImageInfo() {
        leftImagePath?.let { path ->
            val fileName = File(path).name
            tvLeftInfo.text = fileName
        }
        
        rightImagePath?.let { path ->
            val fileName = File(path).name
            tvRightInfo.text = fileName
        }
    }
    
    private fun loadImages() {
        // 创建TpVideoSystem实例用于图片加载（不需要初始化）
        val config = TpVideoConfig.createDefault4K()
        val videoSystem = TpVideoSystem(this, config)

        leftImagePath?.let { path ->
            try {
                videoSystem.loadFullImage(path, leftImageView)
                Log.d(TAG, "左侧图片加载成功: $path")
            } catch (e: Exception) {
                Log.e(TAG, "左侧图片加载失败: $path", e)
                Toast.makeText(this, "左侧图片加载失败", Toast.LENGTH_SHORT).show()
            }
        }

        rightImagePath?.let { path ->
            try {
                videoSystem.loadFullImage(path, rightImageView)
                Log.d(TAG, "右侧图片加载成功: $path")
            } catch (e: Exception) {
                Log.e(TAG, "右侧图片加载失败: $path", e)
                Toast.makeText(this, "右侧图片加载失败", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun setupClickListeners() {
        btnBack.setOnClickListener {
            finish()
        }

        btnSync.setOnClickListener {
            toggleSync()
        }

        btnReset.setOnClickListener {
            resetImages()
        }

        btnSwap.setOnClickListener {
            swapImages()
        }
    }
    


    private fun resetImages() {
        // TpImageView内置重置功能，这里重新加载图片即可
        loadImages()
        Toast.makeText(this, "已重置图片位置", Toast.LENGTH_SHORT).show()
    }
    
    private fun swapImages() {
        // 交换图片路径
        val tempPath = leftImagePath
        leftImagePath = rightImagePath
        rightImagePath = tempPath

        // 重新加载图片
        loadImages()
        updateImageInfo()

        Toast.makeText(this, "已交换图片位置", Toast.LENGTH_SHORT).show()
    }

    // ===== 同步功能相关方法 =====

    private fun setupMatrixSync() {
        // 监听左侧图片的Matrix变化
        leftImageView.setMatrixChangeListener {
            if (isSyncEnabled && !isUpdating) {
                syncMatrixFromLeftToRight()
            }
        }

        // 监听右侧图片的Matrix变化
        rightImageView.setMatrixChangeListener {
            if (isSyncEnabled && !isUpdating) {
                syncMatrixFromRightToLeft()
            }
        }
    }

    private fun syncMatrixFromLeftToRight() {
        isUpdating = true
        try {
            val leftMatrix = leftImageView.imageMatrix
            rightImageView.imageMatrix = Matrix(leftMatrix)
        } catch (e: Exception) {
            Log.e(TAG, "同步Matrix失败", e)
        } finally {
            isUpdating = false
        }
    }

    private fun syncMatrixFromRightToLeft() {
        isUpdating = true
        try {
            val rightMatrix = rightImageView.imageMatrix
            leftImageView.imageMatrix = Matrix(rightMatrix)
        } catch (e: Exception) {
            Log.e(TAG, "同步Matrix失败", e)
        } finally {
            isUpdating = false
        }
    }

    private fun toggleSync() {
        isSyncEnabled = !isSyncEnabled
        updateSyncButtonState()

        val message = if (isSyncEnabled) "已开启同步缩放" else "已关闭同步缩放"
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        Log.d(TAG, message)
    }

    private fun updateSyncButtonState() {
        // 根据同步状态更新按钮透明度
        btnSync.alpha = if (isSyncEnabled) 1.0f else 0.5f
    }
}

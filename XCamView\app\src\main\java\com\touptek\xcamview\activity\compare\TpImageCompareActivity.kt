package com.touptek.xcamview.activity.compare

import android.content.Context
import android.content.Intent
import android.graphics.Matrix
import android.graphics.PointF
import android.os.Bundle
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.touptek.xcamview.R
import com.touptek.video.TpVideoSystem
import com.touptek.video.TpVideoConfig
import java.io.File

class TpImageCompareActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TpImageCompareActivity"
        const val EXTRA_LEFT_IMAGE = "left_image"
        const val EXTRA_RIGHT_IMAGE = "right_image"
        
        fun start(context: Context, leftImagePath: String, rightImagePath: String) {
            val intent = Intent(context, TpImageCompareActivity::class.java).apply {
                putExtra(EXTRA_LEFT_IMAGE, leftImagePath)
                putExtra(EXTRA_RIGHT_IMAGE, rightImagePath)
            }
            context.startActivity(intent)
        }
    }
    
    // UI组件
    private lateinit var leftImageView: ImageView
    private lateinit var rightImageView: ImageView
    private lateinit var btnBack: ImageButton
    private lateinit var btnReset: ImageButton
    private lateinit var btnSwap: ImageButton
    private lateinit var tvLeftInfo: TextView
    private lateinit var tvRightInfo: TextView
    
    // 图像变换矩阵
    private val leftMatrix = Matrix()
    private val rightMatrix = Matrix()
    private val savedLeftMatrix = Matrix()
    private val savedRightMatrix = Matrix()
    
    // 手势检测器
    private lateinit var gestureDetector: GestureDetector
    private lateinit var scaleGestureDetector: ScaleGestureDetector
    
    // 图片路径
    private var leftImagePath: String? = null
    private var rightImagePath: String? = null

    // TpVideoSystem实例
    private var videoSystem: TpVideoSystem? = null

    // 触摸状态
    private var isScaling = false
    private val lastTouchPoint = PointF()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_image_compare)
        
        initViews()
        getImagePaths()
        setupGestures()
        loadImages()
        setupClickListeners()
    }
    
    private fun initViews() {
        leftImageView = findViewById(R.id.left_image)
        rightImageView = findViewById(R.id.right_image)
        btnBack = findViewById(R.id.btn_back)
        btnReset = findViewById(R.id.btn_reset)
        btnSwap = findViewById(R.id.btn_swap)
        tvLeftInfo = findViewById(R.id.tv_left_info)
        tvRightInfo = findViewById(R.id.tv_right_info)
        
        // 设置ImageView的scaleType为matrix以支持自定义变换
        leftImageView.scaleType = ImageView.ScaleType.MATRIX
        rightImageView.scaleType = ImageView.ScaleType.MATRIX
    }
    
    private fun getImagePaths() {
        leftImagePath = intent.getStringExtra(EXTRA_LEFT_IMAGE)
        rightImagePath = intent.getStringExtra(EXTRA_RIGHT_IMAGE)
        
        if (leftImagePath == null || rightImagePath == null) {
            Toast.makeText(this, "图片路径无效", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        // 更新底部信息显示
        updateImageInfo()
    }
    
    private fun updateImageInfo() {
        leftImagePath?.let { path ->
            val fileName = File(path).name
            tvLeftInfo.text = fileName
        }
        
        rightImagePath?.let { path ->
            val fileName = File(path).name
            tvRightInfo.text = fileName
        }
    }
    
    private fun loadImages() {
        // 创建TpVideoSystem实例用于图片加载（不需要初始化）
        val config = TpVideoConfig.createDefault4K()
        val videoSystem = TpVideoSystem(this, config)

        leftImagePath?.let { path ->
            try {
                videoSystem.loadFullImage(path, leftImageView)
                Log.d(TAG, "左侧图片加载成功: $path")
            } catch (e: Exception) {
                Log.e(TAG, "左侧图片加载失败: $path", e)
                Toast.makeText(this, "左侧图片加载失败", Toast.LENGTH_SHORT).show()
            }
        }

        rightImagePath?.let { path ->
            try {
                videoSystem.loadFullImage(path, rightImageView)
                Log.d(TAG, "右侧图片加载成功: $path")
            } catch (e: Exception) {
                Log.e(TAG, "右侧图片加载失败: $path", e)
                Toast.makeText(this, "右侧图片加载失败", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun setupClickListeners() {
        // 优化按钮显示效果
        optimizeButtons()

        btnBack.setOnClickListener {
            finish()
        }

        btnReset.setOnClickListener {
            resetImages()
        }

        btnSwap.setOnClickListener {
            swapImages()
        }
    }

    private fun optimizeButtons() {
        // 增加按钮尺寸和清晰度
        val buttonSize = (56 * resources.displayMetrics.density).toInt()
        val padding = (12 * resources.displayMetrics.density).toInt()

        listOf(btnBack, btnReset, btnSwap).forEach { button ->
            button.layoutParams.width = buttonSize
            button.layoutParams.height = buttonSize
            button.setPadding(padding, padding, padding, padding)
            button.scaleType = ImageView.ScaleType.CENTER_INSIDE
            button.requestLayout()
        }
    }
    
    private fun setupGestures() {
        gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                if (!isScaling) {
                    syncTranslation(-distanceX, -distanceY)
                }
                return true
            }
            
            override fun onDoubleTap(e: MotionEvent): Boolean {
                resetImages()
                return true
            }
        })
        
        scaleGestureDetector = ScaleGestureDetector(this, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
                isScaling = true
                return true
            }
            
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scaleFactor = detector.scaleFactor
                val focusX = detector.focusX
                val focusY = detector.focusY
                
                syncScale(scaleFactor, focusX, focusY)
                return true
            }
            
            override fun onScaleEnd(detector: ScaleGestureDetector) {
                isScaling = false
            }
        })
        
        val touchListener = View.OnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    lastTouchPoint.set(event.x, event.y)
                    savedLeftMatrix.set(leftMatrix)
                    savedRightMatrix.set(rightMatrix)
                }
            }
            
            gestureDetector.onTouchEvent(event)
            scaleGestureDetector.onTouchEvent(event)
            true
        }
        
        leftImageView.setOnTouchListener(touchListener)
        rightImageView.setOnTouchListener(touchListener)
    }
    
    private fun syncTranslation(dx: Float, dy: Float) {
        leftMatrix.set(savedLeftMatrix)
        rightMatrix.set(savedRightMatrix)

        // 应用边界限制
        val constrainedDx = constrainTranslationX(dx)
        val constrainedDy = constrainTranslationY(dy)

        leftMatrix.postTranslate(constrainedDx, constrainedDy)
        rightMatrix.postTranslate(constrainedDx, constrainedDy)

        leftImageView.imageMatrix = leftMatrix
        rightImageView.imageMatrix = rightMatrix

        savedLeftMatrix.set(leftMatrix)
        savedRightMatrix.set(rightMatrix)
    }

    private fun constrainTranslationX(dx: Float): Float {
        val values = FloatArray(9)
        leftMatrix.getValues(values)
        val currentX = values[Matrix.MTRANS_X]
        val scaleX = values[Matrix.MSCALE_X]

        val drawable = leftImageView.drawable ?: return dx
        val imageWidth = drawable.intrinsicWidth * scaleX
        val viewWidth = leftImageView.width.toFloat()

        val newX = currentX + dx

        return when {
            imageWidth <= viewWidth -> {
                // 图片小于视图，居中显示
                (viewWidth - imageWidth) / 2 - currentX
            }
            newX > 0 -> -currentX  // 左边界
            newX < viewWidth - imageWidth -> viewWidth - imageWidth - currentX  // 右边界
            else -> dx
        }
    }

    private fun constrainTranslationY(dy: Float): Float {
        val values = FloatArray(9)
        leftMatrix.getValues(values)
        val currentY = values[Matrix.MTRANS_Y]
        val scaleY = values[Matrix.MSCALE_Y]

        val drawable = leftImageView.drawable ?: return dy
        val imageHeight = drawable.intrinsicHeight * scaleY
        val viewHeight = leftImageView.height.toFloat()

        val newY = currentY + dy

        return when {
            imageHeight <= viewHeight -> {
                // 图片小于视图，居中显示
                (viewHeight - imageHeight) / 2 - currentY
            }
            newY > 0 -> -currentY  // 上边界
            newY < viewHeight - imageHeight -> viewHeight - imageHeight - currentY  // 下边界
            else -> dy
        }
    }
    
    private fun syncScale(scaleFactor: Float, focusX: Float, focusY: Float) {
        // 计算相对于各自ImageView的焦点
        val leftViewWidth = leftImageView.width
        val rightViewWidth = rightImageView.width
        
        // 左侧图片的焦点
        val leftFocusX = if (focusX <= leftViewWidth) focusX else leftViewWidth / 2f
        val leftFocusY = focusY
        
        // 右侧图片的焦点
        val rightFocusX = if (focusX > leftViewWidth) focusX - leftViewWidth - 2 else rightViewWidth / 2f
        val rightFocusY = focusY
        
        leftMatrix.postScale(scaleFactor, scaleFactor, leftFocusX, leftFocusY)
        rightMatrix.postScale(scaleFactor, scaleFactor, rightFocusX, rightFocusY)
        
        leftImageView.imageMatrix = leftMatrix
        rightImageView.imageMatrix = rightMatrix
        
        savedLeftMatrix.set(leftMatrix)
        savedRightMatrix.set(rightMatrix)
    }
    
    private fun resetImages() {
        leftMatrix.reset()
        rightMatrix.reset()
        savedLeftMatrix.reset()
        savedRightMatrix.reset()
        
        leftImageView.imageMatrix = leftMatrix
        rightImageView.imageMatrix = rightMatrix
        
        Toast.makeText(this, "已重置图片位置", Toast.LENGTH_SHORT).show()
    }
    
    private fun swapImages() {
        // 交换图片路径
        val tempPath = leftImagePath
        leftImagePath = rightImagePath
        rightImagePath = tempPath
        
        // 重新加载图片
        loadImages()
        updateImageInfo()
        resetImages()
        
        Toast.makeText(this, "已交换图片位置", Toast.LENGTH_SHORT).show()
    }
}

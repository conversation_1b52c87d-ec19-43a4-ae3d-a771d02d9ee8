[{"level_": 0, "message_": "Start JSON generation. Platform version: 27 min SDK version: arm64-v8a", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a\\android_gradle_build.json due to:", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a'", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a'", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamView\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=27\" ^\n  \"-DANDROID_PLATFORM=android-27\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++11\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamView\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3t3vu28a\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamView\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3t3vu28a\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BC:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamView\\\\app\\\\.cxx\\\\Debug\\\\3t3vu28a\\\\arm64-v8a\" ^\n  -GNinja\n", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamView\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=27\" ^\n  \"-DANDROID_PLATFORM=android-27\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++11\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamView\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3t3vu28a\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamView\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3t3vu28a\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BC:\\\\hhx\\\\rk3588\\\\AndroidStudio\\\\XCamView\\\\app\\\\.cxx\\\\Debug\\\\3t3vu28a\\\\arm64-v8a\" ^\n  -GNinja\n", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a\\compile_commands.json.bin existed but not C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a\\compile_commands.json", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]
package com.touptek.xcamview.util

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.ImageView
import java.io.File
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 简化版的TpImageLoader，专为XCamView图片对比功能设计
 * 提供基本的图片加载功能，支持异步加载和简单的内存优化
 */
object TpImageLoader {
    private const val TAG = "TpImageLoader"
    
    // 线程池用于异步加载
    private val executor: ExecutorService = Executors.newFixedThreadPool(2)
    
    // 主线程Handler
    private val mainHandler = Handler(Looper.getMainLooper())
    
    /**
     * 加载图片到ImageView
     * @param imagePath 图片文件路径
     * @param imageView 目标ImageView
     */
    fun loadImage(imagePath: String, imageView: ImageView) {
        if (imagePath.isEmpty()) {
            Log.e(TAG, "图片路径为空")
            return
        }
        
        val file = File(imagePath)
        if (!file.exists()) {
            Log.e(TAG, "图片文件不存在: $imagePath")
            return
        }
        
        // 异步加载图片
        executor.execute {
            try {
                val bitmap = loadBitmapFromFile(imagePath, imageView.width, imageView.height)
                
                // 在主线程更新UI
                mainHandler.post {
                    if (bitmap != null) {
                        imageView.setImageBitmap(bitmap)
                        Log.d(TAG, "图片加载成功: $imagePath")
                    } else {
                        Log.e(TAG, "图片解码失败: $imagePath")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载图片时发生错误: $imagePath", e)
                mainHandler.post {
                    // 可以设置一个默认的错误图片
                    imageView.setImageResource(android.R.drawable.ic_menu_gallery)
                }
            }
        }
    }
    
    /**
     * 从文件加载Bitmap，带有内存优化的采样
     */
    private fun loadBitmapFromFile(imagePath: String, targetWidth: Int, targetHeight: Int): Bitmap? {
        return try {
            // 如果目标尺寸无效，使用默认尺寸
            val reqWidth = if (targetWidth > 0) targetWidth else 1920
            val reqHeight = if (targetHeight > 0) targetHeight else 1080
            
            // 首先获取图片尺寸
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            
            // 计算采样率
            options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)
            
            // 实际解码图片
            options.inJustDecodeBounds = false
            BitmapFactory.decodeFile(imagePath, options)
            
        } catch (e: Exception) {
            Log.e(TAG, "解码图片失败: $imagePath", e)
            null
        }
    }
    
    /**
     * 计算合适的采样率
     */
    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1
        
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            
            // 计算最大的inSampleSize值，该值是2的幂，并且保持高度和宽度都大于请求的高度和宽度
            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        executor.shutdown()
    }
}

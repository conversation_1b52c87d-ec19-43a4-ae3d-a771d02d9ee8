package com.touptek.xcamview.activity.browse.imagemanagement

import android.graphics.Matrix
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.util.BaseDialogFragment
import com.touptek.xcamview.databinding.ImageViewerBinding
import com.touptek.measurerealize.TpImageView
import com.touptek.video.TpVideoSystem
import com.touptek.video.TpVideoConfig
import java.util.ArrayList
import kotlin.apply
import kotlin.collections.indices
import kotlin.let
import kotlin.run

class TpImageDecodeDialogFragment : BaseDialogFragment() {
    private lateinit var binding: ImageViewerBinding
    private var buttonsVisible = false
    private lateinit var imagePaths: List<String>
    private var currentPosition = 0

    // TpVideoSystem实例用于图片加载
    private var videoSystem: TpVideoSystem? = null


    companion object {
        private const val ARG_IMAGE_PATHS = "image_paths"
        private const val ARG_CURRENT_POSITION = "current_position"

        fun newInstance(imagePaths: List<String>, currentPosition: Int): TpImageDecodeDialogFragment {
            val fragment = TpImageDecodeDialogFragment()
            val args = Bundle().apply {
                putStringArrayList(ARG_IMAGE_PATHS, ArrayList(imagePaths))
                putInt(ARG_CURRENT_POSITION, currentPosition)
            }
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ImageViewerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 获取传递的参数
        imagePaths = arguments?.getStringArrayList(ARG_IMAGE_PATHS) ?: run {
            dismiss()
            return
        }
        currentPosition = arguments?.getInt(ARG_CURRENT_POSITION, 0) ?: 0

        // 初始化TpVideoSystem
        initVideoSystem()

        // 加载当前图片
        loadCurrentImage()

        // 初始化按钮面板为隐藏状态
        binding.buttonPanel.visibility = View.GONE

        // 设置TpImageView的单击监听器来切换按钮可见性
        binding.imageView.setOnSingleTapListener {
            toggleButtons()
        }

        // 按钮点击事件
        binding.btnPrevious.setOnClickListener {
            showPreviousImage()
        }

        binding.btnNext.setOnClickListener {
            showNextImage()
        }

        binding.btnBack.setOnClickListener {
            dismiss()
        }


    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog) // 全屏样式
    }

    override fun onStart() {
        super.onStart()
        // 设置对话框全屏
        dialog?.window?.let { window ->
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        }
    }

    private fun initVideoSystem() {
        val config = TpVideoConfig.createDefault4K()
        videoSystem = TpVideoSystem(requireActivity(), config)
    }

    private fun loadCurrentImage() {
        if (currentPosition in imagePaths.indices) {
            // 使用TpVideoSystem加载高质量图片
            videoSystem?.loadFullImage(imagePaths[currentPosition], binding.imageView)
            updateButtonStates()
        }
    }

    private fun showPreviousImage() {
        if (currentPosition > 0) {
            currentPosition--
            loadCurrentImage()
        }
    }

    private fun showNextImage() {
        if (currentPosition < imagePaths.size - 1) {
            currentPosition++
            loadCurrentImage()
        }
    }

    private fun updateButtonStates() {
        // 更新上一页按钮状态
        binding.btnPrevious.isEnabled = currentPosition > 0
        binding.btnPrevious.alpha = if (currentPosition > 0) 1.0f else 0.5f

        // 更新下一页按钮状态
        binding.btnNext.isEnabled = currentPosition < imagePaths.size - 1
        binding.btnNext.alpha = if (currentPosition < imagePaths.size - 1) 1.0f else 0.5f

        // 更新标题或其他UI元素（如果需要）
        // 例如：binding.tvImageCounter.text = "${currentPosition + 1}/${imagePaths.size}"
    }

    private fun toggleButtons() {
        buttonsVisible = !buttonsVisible
        binding.buttonPanel.visibility = if (buttonsVisible) View.VISIBLE else View.GONE
    }




}
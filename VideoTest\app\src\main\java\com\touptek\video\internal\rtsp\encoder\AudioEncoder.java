package com.touptek.video.internal.rtsp.encoder;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.AudioAttributes;
import android.media.AudioFormat;
import android.media.AudioPlaybackCaptureConfiguration;
import android.media.AudioRecord;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.MediaRecorder;
import android.media.projection.MediaProjection;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.pedro.rtspserver.RtspServer;

import java.nio.ByteBuffer;

/**
 * 音频编码器
 * 负责捕获系统音频或麦克风并编码为AAC格式
 */
public class AudioEncoder {
    private static final String TAG = "AudioEncoder";
    public static final int SAMPLE_RATE = 44100;
    private static final int CHANNEL_COUNT = 1; // 单声道
    private static final String MIME_TYPE = MediaFormat.MIMETYPE_AUDIO_AAC;
    private static final int BIT_RATE = 128000; // 128kbps
    
    private final Context context;
    private final MediaProjection mediaProjection;
    private final boolean recordMic;
    
    /**
     * 音频录制器
     */
    private AudioRecord audioRecord;
    
    /**
     * AAC编码器
     */
    private MediaCodec encoder;
    
    /**
     * 缓冲区信息
     */
    private final MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
    
    /**
     * 音频缓冲区大小
     */
    private int bufferSize = 0;
    
    private AudioEncoder(Builder builder) {
        this.context = builder.context;
        this.mediaProjection = builder.mediaProjection;
        this.recordMic = builder.recordMic;
        this.bufferSize = builder.bufferSize;
        try {
            setupAudioRecord();
            setupEncoder();
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize audio encoder", e);
            release();
        }
    }

    public static Builder builder(Context context, MediaProjection mediaProjection) {
        return new Builder(context, mediaProjection);
    }

    public static class Builder {
        private final Context context;
        private final MediaProjection mediaProjection;
        private boolean recordMic = false;
        private int bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT);

        private Builder(Context context, MediaProjection mediaProjection) {
            this.context = context;
            this.mediaProjection = mediaProjection;
        }

        public Builder recordMicrophone(boolean recordMic) {
            this.recordMic = recordMic;
            return this;
        }

        public Builder setBufferSize(int bufferSize) {
            this.bufferSize = bufferSize;
            return this;
        }

        public AudioEncoder build() {
            return new AudioEncoder(this);
        }
    }
    
    /**
     * 设置音频录制器
     */
    private void setupAudioRecord() {
        // 检查录音权限（当录制麦克风时需要）
        if (recordMic && ActivityCompat.checkSelfPermission(
                context, 
                Manifest.permission.RECORD_AUDIO
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            Log.e(TAG, "No RECORD_AUDIO permission");
            return;
        }
        
        // 音频编码和通道配置
        int encoding = AudioFormat.ENCODING_PCM_16BIT;
        int channelConfig = AudioFormat.CHANNEL_IN_MONO;
        
        // 获取最小缓冲区大小
        bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, channelConfig, encoding);
        
        // 创建AudioRecord
        AudioRecord.Builder builder = new AudioRecord.Builder()
            .setAudioFormat(
                new AudioFormat.Builder()
                    .setSampleRate(SAMPLE_RATE)
                    .setEncoding(encoding)
                    .setChannelMask(channelConfig)
                    .build()
            )
            .setBufferSizeInBytes(bufferSize);
        
        // 根据是否录制麦克风设置音频源
        if (recordMic) {
            builder.setAudioSource(MediaRecorder.AudioSource.MIC);
        } else {
            // 设置系统音频捕获配置
            AudioPlaybackCaptureConfiguration audioConfig = new AudioPlaybackCaptureConfiguration.Builder(mediaProjection)
                .addMatchingUsage(AudioAttributes.USAGE_MEDIA)
                .addMatchingUsage(AudioAttributes.USAGE_GAME)
                .addMatchingUsage(AudioAttributes.USAGE_UNKNOWN)
                .build();
            
            builder.setAudioPlaybackCaptureConfig(audioConfig);
        }
        
        try {
            audioRecord = builder.build();
            audioRecord.startRecording();
        } catch (Exception e) {
            Log.e(TAG, "Error creating AudioRecord", e);
        }
    }
    
    /**
     * 设置编码器
     */
    private void setupEncoder() {
        try {
            // 创建AAC音频格式
            MediaFormat format = MediaFormat.createAudioFormat(MIME_TYPE, SAMPLE_RATE, CHANNEL_COUNT);
            format.setInteger(MediaFormat.KEY_AAC_PROFILE, MediaCodecInfo.CodecProfileLevel.AACObjectLC);
            format.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE);
            format.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 16384);
            
            // 创建编码器
            encoder = MediaCodec.createEncoderByType(MIME_TYPE);
            encoder.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            encoder.start();
        } catch (Exception e) {
            Log.e(TAG, "Error setting up encoder", e);
        }
    }
    
    /**
     * 编码音频并发送到RTSP服务器
     * @param rtspServer RTSP服务器
     * @param timestamp 时间戳（微秒）
     */
    public void encodeAudio(RtspServer rtspServer, long timestamp) {
        if (audioRecord == null || encoder == null) return;
        
        try {
            // 分配缓冲区读取PCM数据
            ByteBuffer audioBuffer = ByteBuffer.allocateDirect(bufferSize);
            
            // 从AudioRecord读取PCM数据
            int bytesRead = audioRecord.read(audioBuffer, bufferSize, AudioRecord.READ_NON_BLOCKING);
            
            if (bytesRead > 0) {
                // 获取编码器输入缓冲区
                int inputBufferIndex = encoder.dequeueInputBuffer(0);
                
                if (inputBufferIndex >= 0) {
                    // 写入PCM数据到编码器
                    ByteBuffer inputBuffer = encoder.getInputBuffer(inputBufferIndex);
                    if (inputBuffer != null) {
                        audioBuffer.flip();
                        inputBuffer.put(audioBuffer);
                        encoder.queueInputBuffer(inputBufferIndex, 0, bytesRead, timestamp, 0);
                    }
                }
                
                // 获取编码后的AAC数据
                int outputBufferIndex;
                do {
                    outputBufferIndex = encoder.dequeueOutputBuffer(bufferInfo, 0);
                    
                    if (outputBufferIndex >= 0) {
                        ByteBuffer outputBuffer = encoder.getOutputBuffer(outputBufferIndex);
                        
                        if (outputBuffer != null) {
                            // 发送AAC数据到RTSP服务器
                            rtspServer.sendAudio(outputBuffer, bufferInfo);
                        }
                        
                        encoder.releaseOutputBuffer(outputBufferIndex, false);
                    }
                } while (outputBufferIndex >= 0);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error encoding audio", e);
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        try {
            if (audioRecord != null) {
                audioRecord.stop();
                audioRecord.release();
                audioRecord = null;
            }
            
            if (encoder != null) {
                encoder.stop();
                encoder.release();
                encoder = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error releasing audio resources", e);
        }
    }
}

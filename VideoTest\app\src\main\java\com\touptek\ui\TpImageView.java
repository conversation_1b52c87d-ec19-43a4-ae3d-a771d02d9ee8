package com.touptek.ui;

import android.content.Context;
import android.graphics.Matrix;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

import com.touptek.ui.internal.TpViewTransform;

import java.lang.reflect.Field;

/**
 * TpImageView - 模仿Android系统相册的可缩放ImageView
 * <p>
 * 全新实现，提供与系统相册一致的缩放和平移体验：
 * - 硬边界限制，无阻尼效果
 * - 智能双击缩放：始终回到能让整个图像完全显示在屏幕上的最小缩放比例
 * - 双指缩放时焦点稳定，无跳跃感
 * - 解决手势冲突问题
 * </p>
 * 
 * 主要功能：
 * <ul>
 *   <li>双指缩放（焦点稳定）</li>
 *   <li>单指平移（硬边界限制）</li>
 *   <li>双击缩放切换</li>
 *   <li>小图自动居中</li>
 *   <li>大图边缘对齐</li>
 * </ul>
 * 
 * 使用方式：
 * <pre>{@code
 * // 在布局文件中使用
 * <com.android.rockchip.camera2.view.TpImageView
 *     android:id="@+id/zoomable_image"
 *     android:layout_width="match_parent"
 *     android:layout_height="match_parent" />
 * 
 * // 在代码中使用
 * TpImageView imageView = findViewById(R.id.zoomable_image);
 * TpImageLoader.loadFullImage(imagePath, imageView);
 * }</pre>
 */
public class TpImageView extends AppCompatImageView {
    private static final String TAG = "TpImageView";
    
    /**
     * 手势状态枚举
     */
    private enum GestureState {
        IDLE,       // 空闲状态
        SCALING,    // 缩放中
        PANNING     // 平移中
    }
    
    /**
     * 缩放信息类
     */
    private static class ScaleInfo {
        float currentScale = 1.0f;      // 当前缩放比例
        float fitScreenScale = 1.0f;    // 适应屏幕的缩放比例
        float minScale = 0.1f;        // 最小缩放比例（初始值，会在updateFitScreenScale中更新）
        float maxScale = 10.0f;         // 最大缩放比例
        boolean userSetScaleRange = false; // 用户是否手动设置了缩放范围

        void updateFitScreenScale(float viewWidth, float viewHeight, float imageWidth, float imageHeight) {
            if (imageWidth > 0 && imageHeight > 0 && viewWidth > 0 && viewHeight > 0) {
                float scaleX = viewWidth / imageWidth;
                float scaleY = viewHeight / imageHeight;
                fitScreenScale = Math.min(scaleX, scaleY);

                // 无论用户是否设置了缩放范围，最小缩放都应该是适应屏幕的缩放
                // 这确保双击时能回到完整显示图像的状态
                minScale = fitScreenScale;

                // 只有在用户没有手动设置缩放范围时，才使用默认的最大缩放
                if (!userSetScaleRange) {
                    maxScale = Math.max(10.0f, fitScreenScale * 10.0f);
                }

                Log.d(TAG, "适应屏幕缩放计算: 图片=" + imageWidth + "x" + imageHeight +
                      ", 视图=" + viewWidth + "x" + viewHeight +
                      ", scaleX=" + scaleX + ", scaleY=" + scaleY +
                      ", fitScreenScale=" + fitScreenScale +
                      ", 缩放范围: [" + minScale + ", " + maxScale + "]");
            }
        }

        void setUserMaxScale(float userMaxScale) {
            maxScale = userMaxScale;
            userSetScaleRange = true;
        }
        
        float getNextDoubleTapScale() {
            // 智能双击缩放：始终返回到能让整个图像完全显示在屏幕上的最小缩放比例
            // 这确保了无论图像尺寸多大，双击后都能看到完整图像
            return fitScreenScale;
        }
    }
    
    // BoundaryInfo类已移除，边界计算由TransformUtils处理

    // 核心组件
    private Matrix mMatrix = new Matrix();
    private ScaleInfo mScaleInfo = new ScaleInfo();
    private GestureState mGestureState = GestureState.IDLE;
    
    // 手势检测器
    private ScaleGestureDetector mScaleGestureDetector;
    private GestureDetector mGestureDetector;
    
    // 触摸状态
    private float mLastTouchX = 0f;
    private float mLastTouchY = 0f;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mResetGestureStateRunnable;
    
    // 配置选项
    private boolean mZoomEnabled = true;
    private boolean mPanEnabled = true;
    private boolean mDoubleTapEnabled = true;
    
    // 监听器
    private OnZoomChangeListener mZoomChangeListener;
    
    /**
     * 缩放变化监听器
     */
    public interface OnZoomChangeListener {
        void onZoomChanged(float scale, float focusX, float focusY);
    }
    
    public TpImageView(Context context) {
        super(context);
        init(context);
    }
    
    public TpImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public TpImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    /**
     * 初始化组件
     */
    private void init(Context context) {
        // 设置ScaleType为MATRIX，支持自定义变换
        setScaleType(ScaleType.MATRIX);
        
        // 初始化手势检测器
        initGestureDetectors(context);
        
        Log.d(TAG, "TpZoomableImageView初始化完成 - 系统相册风格");
    }
    
    /**
     * 初始化手势检测器
     */
    private void initGestureDetectors(Context context) {
        // 创建优化的缩放手势检测器（使用反射降低最小识别距离）
        mScaleGestureDetector = createOptimizedScaleGestureDetector(context);
        
        // 通用手势检测器（用于双击等）
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (!mDoubleTapEnabled || !mZoomEnabled) return false;

                float targetScale = mScaleInfo.getNextDoubleTapScale();
                float scaleFactor = targetScale / mScaleInfo.currentScale;

                Log.d(TAG, "双击缩放 - 从 " + mScaleInfo.currentScale + " 到 " + targetScale);

                return performScale(scaleFactor, e.getX(), e.getY());
            }

            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                performClick();
                return true;
            }
        });
    }

    /**
     * 创建优化的缩放手势检测器
     * 使用反射修改系统mMinSpan值，解决双指距离较短时无法缩放的问题
     */
    private ScaleGestureDetector createOptimizedScaleGestureDetector(Context context) {
        // 创建标准的缩放手势检测器
        ScaleGestureDetector detector = new ScaleGestureDetector(context, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                if (!mZoomEnabled) return false;

                float scaleFactor = detector.getScaleFactor();
                float focusX = detector.getFocusX();
                float focusY = detector.getFocusY();

                return performScale(scaleFactor, focusX, focusY);
            }

            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                mGestureState = GestureState.SCALING;
                cancelResetGestureState();
                Log.d(TAG, "图片缩放开始");
                return true;
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                scheduleResetGestureState();
                Log.d(TAG, "图片缩放结束");
            }
        });

        // 使用反射修改mMinSpan，解决双指距离较短时无法缩放的问题
        try {
            Field field = detector.getClass().getDeclaredField("mMinSpan");
            field.setAccessible(true);
            field.set(detector, 1); // 设置为1像素，几乎没有限制
            Log.d(TAG, "成功通过反射优化ScaleGestureDetector敏感度");
        } catch (Exception e) {
            Log.w(TAG, "反射优化ScaleGestureDetector失败，使用默认配置", e);
        }

        return detector;
    }

    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean handled = false;

        // 使用优化后的缩放手势检测器
        if (mScaleGestureDetector != null) {
            handled = mScaleGestureDetector.onTouchEvent(event) || handled;
        }

        // 使用通用手势检测器（双击、单击、长按）
        if (mGestureDetector != null) {
            handled = mGestureDetector.onTouchEvent(event) || handled;
        }

        // 处理平移手势（只在非缩放状态下）
        if (mGestureState != GestureState.SCALING && mPanEnabled) {
            handled = handlePanGesture(event) || handled;
        }

        return handled || super.onTouchEvent(event);
    }

    /**
     * 处理平移手势
     */
    private boolean handlePanGesture(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mGestureState = GestureState.PANNING;
                mLastTouchX = event.getX();
                mLastTouchY = event.getY();
                return true;

            case MotionEvent.ACTION_MOVE:
                if (event.getPointerCount() == 1 && mGestureState == GestureState.PANNING) {
                    float deltaX = event.getX() - mLastTouchX;
                    float deltaY = event.getY() - mLastTouchY;

                    mLastTouchX = event.getX();
                    mLastTouchY = event.getY();

                    // 使用TransformUtils进行平移（与缩放保持一致）
                    TpViewTransform.applyPan(this, mMatrix, deltaX, deltaY);
                    return true;
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mGestureState = GestureState.IDLE;
                break;
        }

        return false;
    }

    /**
     * 执行缩放操作（优化版本，防止黑边闪烁，支持大倍数缩放）
     */
    private boolean performScale(float scaleFactor, float focusX, float focusY) {
        if (!mZoomEnabled || getDrawable() == null) return false;

        // 计算新的缩放比例
        float newScale = mScaleInfo.currentScale * scaleFactor;

        // 限制缩放范围（使用用户设置的最小/最大值）
        if (newScale < mScaleInfo.minScale) {
            scaleFactor = mScaleInfo.minScale / mScaleInfo.currentScale;
            newScale = mScaleInfo.minScale;
        } else if (newScale > mScaleInfo.maxScale) {
            scaleFactor = mScaleInfo.maxScale / mScaleInfo.currentScale;
            newScale = mScaleInfo.maxScale;
        }

        // 对于大倍数缩放，添加性能检查
        if (newScale > 20.0f) {
            Log.d(TAG, "大倍数缩放警告: " + newScale + "倍，可能影响性能");
        }

        // 使用TransformUtils进行缩放（现在包含了TpImageView的成熟实现）
        TpViewTransform.applyZoom(this, mMatrix, scaleFactor, focusX, focusY);

        // 更新当前缩放比例
        mScaleInfo.currentScale = getCurrentScale();

        // 通知监听器
        if (mZoomChangeListener != null) {
            mZoomChangeListener.onZoomChanged(mScaleInfo.currentScale, focusX, focusY);
        }

        Log.d(TAG, "缩放: " + mScaleInfo.currentScale + ", 焦点: (" + focusX + ", " + focusY + ")");
        return true;
    }
    
    /**
     * 获取当前缩放比例
     */
    public float getCurrentScale() {
        float[] values = new float[9];
        mMatrix.getValues(values);
        return values[Matrix.MSCALE_X];
    }

    /**
     * 手势状态重置相关方法
     */
    private void scheduleResetGestureState() {
        cancelResetGestureState();
        mResetGestureStateRunnable = () -> {
            if (mGestureState == GestureState.SCALING) {
                mGestureState = GestureState.IDLE;
                Log.d(TAG, "手势状态重置为IDLE");
            }
        };
        mHandler.postDelayed(mResetGestureStateRunnable, 200);
    }

    private void cancelResetGestureState() {
        if (mResetGestureStateRunnable != null) {
            mHandler.removeCallbacks(mResetGestureStateRunnable);
            mResetGestureStateRunnable = null;
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        resetMatrix();
    }

    @Override
    public void setImageDrawable(@Nullable Drawable drawable) {
        super.setImageDrawable(drawable);
        resetMatrix();
    }

    /**
     * 重置Matrix到初始状态
     */
    public void resetMatrix() {
        cancelResetGestureState();
        mGestureState = GestureState.IDLE;

        Drawable drawable = getDrawable();
        if (drawable == null || getWidth() == 0 || getHeight() == 0) {
            mMatrix.reset();
            setImageMatrix(mMatrix);
            return;
        }

        // 计算适应屏幕的缩放和居中
        float imageWidth = drawable.getIntrinsicWidth();
        float imageHeight = drawable.getIntrinsicHeight();
        float viewWidth = getWidth();
        float viewHeight = getHeight();

        // 更新缩放信息
        mScaleInfo.updateFitScreenScale(viewWidth, viewHeight, imageWidth, imageHeight);

        // 重置Matrix并应用适应屏幕的变换
        mMatrix.reset();

        // 应用缩放
        mMatrix.postScale(mScaleInfo.fitScreenScale, mScaleInfo.fitScreenScale);

        // 居中显示
        float dx = (viewWidth - imageWidth * mScaleInfo.fitScreenScale) / 2;
        float dy = (viewHeight - imageHeight * mScaleInfo.fitScreenScale) / 2;
        mMatrix.postTranslate(dx, dy);

        setImageMatrix(mMatrix);
        mScaleInfo.currentScale = mScaleInfo.fitScreenScale;

        Log.d(TAG, "Matrix重置完成 - 适应屏幕缩放: " + mScaleInfo.fitScreenScale);
    }

    // ==================== 公共API方法（保持兼容性） ====================

    /**
     * 设置最大缩放倍数（推荐API）
     * <p>
     * 最小缩放固定为适应屏幕大小，最大缩放由用户指定。
     * 这种设计符合专业图像查看应用的实际使用需求。
     * </p>
     *
     * @param maxScale 最大缩放倍数，建议范围：5.0f - 50.0f
     */
    public void setMaxScale(float maxScale) {
        if (maxScale > 0) {
            mScaleInfo.setUserMaxScale(maxScale);
            Log.d(TAG, "最大缩放设置为: " + maxScale + " (最小缩放固定为适应屏幕缩放)");
        }
    }

    /**
     * 设置是否启用缩放功能
     */
    public void setZoomEnabled(boolean enabled) {
        this.mZoomEnabled = enabled;
        Log.d(TAG, "缩放功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置是否启用平移功能
     */
    public void setPanEnabled(boolean enabled) {
        this.mPanEnabled = enabled;
        Log.d(TAG, "平移功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置是否启用双击缩放功能
     */
    public void setDoubleTapEnabled(boolean enabled) {
        this.mDoubleTapEnabled = enabled;
        Log.d(TAG, "双击缩放功能" + (enabled ? "启用" : "禁用"));
    }

    /**
     * 设置缩放变化监听器
     */
    public void setOnZoomChangeListener(OnZoomChangeListener listener) {
        this.mZoomChangeListener = listener;
    }

    /**
     * 获取是否启用缩放功能
     */
    public boolean isZoomEnabled() {
        return mZoomEnabled;
    }

    /**
     * 获取是否启用平移功能
     */
    public boolean isPanEnabled() {
        return mPanEnabled;
    }

    /**
     * 获取是否启用双击缩放功能
     */
    public boolean isDoubleTapEnabled() {
        return mDoubleTapEnabled;
    }

    /**
     * 获取当前设置的最大缩放倍数
     *
     * @return 最大缩放倍数
     */
    public float getMaxScale() {
        return mScaleInfo.maxScale;
    }

    /**
     * 获取适应屏幕的缩放比例
     * <p>
     * 这是能让整个图像完全显示在屏幕上的最小缩放比例，
     * 也是双击缩放的目标比例。
     * </p>
     *
     * @return 适应屏幕的缩放比例
     */
    public float getFitScreenScale() {
        return mScaleInfo.fitScreenScale;
    }

    /**
     * 获取当前设置的最小缩放倍数
     *
     * @return 最小缩放倍数
     */
    public float getMinScale() {
        return mScaleInfo.minScale;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="8" />
    <option name="CUSTOM_SCOPE_NAME" value="未命名" />
  </component>
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/TouptekSDK/src/main/res/layout/dialog_tp_test.xml">
        <config>
          <theme>@android:style/Theme.Material.Light</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_main.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_tp_video_player_new.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/decoder.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/dialog_image_format_settings.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/dialog_smb_settings.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/dialog_tp_settings.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/dialog_tp_test.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/encoder.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_tp_network_settings.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_tp_smb_settings.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_tp_tv_mode_settings.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/image_viewer.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_settings_menu.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/media_browser.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/media_browser_integrated.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/media_item.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/network_settings.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/popup_menu.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/spinner_item.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/tp_speed_dropdown_menu.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/tp_speed_selection_dialog.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/tp_video_player_controls.xml">
        <config>
          <theme>@style/Theme.MediacodecNew</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e9cc7cb9-5e39-4459-9c6e-4fd56e5c5adb" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=PhysicalDevice, isTemplate=false, identifier=serial=336e4d823721adb2)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/app">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="VideoTest" type="f1a62948:ProjectNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/app/src/main/java/com/android/rockchip/camera2/view/moyu-chat" />
  </component>
  <component name="NamedScopeManager">
    <scope name="未命名" pattern="((((((src[mediacodecNew.video.main]:*..*||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/service//*)&amp;&amp;!file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/service/StreamingSocketService.java||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/video//*||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/view//*)&amp;&amp;!file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/util/touptek_serial_rk.java||file[VideoTest.CodecUtils.main]:res//*)&amp;&amp;!file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/view/TpCustomProgressBar.java||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/view/TpImageView.java||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/view/TpRoiView.java||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/view/TpTextureView.java||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/view/TpVideoPlayerView.java)&amp;&amp;!file[VideoTest.CodecUtils.main]:*/||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/video/TpVideoConfig.java||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/video/TpVideoSystem.java||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/util/*||file[VideoTest.CodecUtils.main]:java/com/android/rockchip/camera2/util//*)&amp;&amp;!file[VideoTest.TouptekSDK*]:*//*&amp;&amp;!file[VideoTest.CodecUtils*]:*//*||file[VideoTest.TouptekSDK.main]:java/com/touptek/ui//*)&amp;&amp;!file[VideoTest.TouptekSDK.main]:java/com/touptek/ui/internal//*||file[VideoTest.TouptekSDK.main]:java/com/touptek/utils//*||file[VideoTest.TouptekSDK.main]:java/com/touptek/video/*" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2uR0Ug3GFpmQlmlRehurqUki1rI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Android App.app.executor": "Run",
    "ResourceManagerPrefKey.ModuleName": "VideoTest.app.main",
    "ResourceManagerPrefKey.ResourceType": "LAYOUT",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "git-widget-placeholder": ".invalid",
    "last_opened_file_path": "C:/hhx/rk3588/AndroidStudio/VideoTest/app/src/main",
    "project.structure.last.edited": "Dependencies",
    "project.structure.proportion": "0.17",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "AndroidSdkUpdater",
    "show.do.not.copy.http.proxy.settings.to.gradle": "true",
    "two.files.diff.last.used.folder": "C:/hhx/rk3588/AndroidStudio/VideoTest/app/src/main/java/com/touptek/view"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main" />
      <recent name="C:\hhx\rk3588\AndroidStudio\VideoTest\app\libs" />
      <recent name="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\java\com\touptek" />
      <recent name="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\java\com" />
      <recent name="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\java" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\hhx\rk3588\AndroidStudio\VideoTest\app\libs" />
      <recent name="C:\Users\<USER>\Desktop\test\net1.0\1.0Aakeyishixian\VideoTest\app\src\main" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.touptek.video" />
      <recent name="com.android.rockchip.camera2.video" />
      <recent name="com.android.rockchip.camera2.view" />
      <recent name="com.android.rockchip.camera2" />
      <recent name="com.android.rockchip.camera2.util" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="VideoTest.app.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e9cc7cb9-5e39-4459-9c6e-4fd56e5c5adb" name="更改" comment="" />
      <created>1742196096541</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742196096541</updated>
    </task>
    <servers />
  </component>
</project>
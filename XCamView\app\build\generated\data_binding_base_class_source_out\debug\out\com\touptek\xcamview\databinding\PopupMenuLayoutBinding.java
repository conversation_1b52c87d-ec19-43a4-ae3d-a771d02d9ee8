// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PopupMenuLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnColorAdjustment;

  @NonNull
  public final ImageButton btnExposure;

  @NonNull
  public final ImageButton btnFlip;

  @NonNull
  public final ImageButton btnImageProcessing;

  @NonNull
  public final ImageButton btnPowerFrequency;

  @NonNull
  public final ImageButton btnScene;

  @NonNull
  public final ImageButton btnWhiteBalance;

  private PopupMenuLayoutBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnColorAdjustment, @NonNull ImageButton btnExposure,
      @NonNull ImageButton btnFlip, @NonNull ImageButton btnImageProcessing,
      @NonNull ImageButton btnPowerFrequency, @NonNull ImageButton btnScene,
      @NonNull ImageButton btnWhiteBalance) {
    this.rootView = rootView;
    this.btnColorAdjustment = btnColorAdjustment;
    this.btnExposure = btnExposure;
    this.btnFlip = btnFlip;
    this.btnImageProcessing = btnImageProcessing;
    this.btnPowerFrequency = btnPowerFrequency;
    this.btnScene = btnScene;
    this.btnWhiteBalance = btnWhiteBalance;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PopupMenuLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PopupMenuLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.popup_menu_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PopupMenuLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_color_adjustment;
      ImageButton btnColorAdjustment = ViewBindings.findChildViewById(rootView, id);
      if (btnColorAdjustment == null) {
        break missingId;
      }

      id = R.id.btn_exposure;
      ImageButton btnExposure = ViewBindings.findChildViewById(rootView, id);
      if (btnExposure == null) {
        break missingId;
      }

      id = R.id.btn_flip;
      ImageButton btnFlip = ViewBindings.findChildViewById(rootView, id);
      if (btnFlip == null) {
        break missingId;
      }

      id = R.id.btn_image_processing;
      ImageButton btnImageProcessing = ViewBindings.findChildViewById(rootView, id);
      if (btnImageProcessing == null) {
        break missingId;
      }

      id = R.id.btn_power_frequency;
      ImageButton btnPowerFrequency = ViewBindings.findChildViewById(rootView, id);
      if (btnPowerFrequency == null) {
        break missingId;
      }

      id = R.id.btn_scene;
      ImageButton btnScene = ViewBindings.findChildViewById(rootView, id);
      if (btnScene == null) {
        break missingId;
      }

      id = R.id.btn_white_balance;
      ImageButton btnWhiteBalance = ViewBindings.findChildViewById(rootView, id);
      if (btnWhiteBalance == null) {
        break missingId;
      }

      return new PopupMenuLayoutBinding((LinearLayout) rootView, btnColorAdjustment, btnExposure,
          btnFlip, btnImageProcessing, btnPowerFrequency, btnScene, btnWhiteBalance);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

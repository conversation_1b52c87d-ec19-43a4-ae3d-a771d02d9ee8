// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTouptekBtnBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnAbout;

  @NonNull
  public final ImageButton btnDraw;

  @NonNull
  public final ImageButton btnFolder;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final ImageButton btnPause;

  @NonNull
  public final ImageButton btnRecordVideo;

  @NonNull
  public final ImageButton btnSettings;

  @NonNull
  public final ImageButton btnTakePhoto;

  @NonNull
  public final ImageButton btnZoomIn;

  @NonNull
  public final ImageButton btnZoomOut;

  private ActivityTouptekBtnBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnAbout,
      @NonNull ImageButton btnDraw, @NonNull ImageButton btnFolder, @NonNull ImageButton btnMenu,
      @NonNull ImageButton btnPause, @NonNull ImageButton btnRecordVideo,
      @NonNull ImageButton btnSettings, @NonNull ImageButton btnTakePhoto,
      @NonNull ImageButton btnZoomIn, @NonNull ImageButton btnZoomOut) {
    this.rootView = rootView;
    this.btnAbout = btnAbout;
    this.btnDraw = btnDraw;
    this.btnFolder = btnFolder;
    this.btnMenu = btnMenu;
    this.btnPause = btnPause;
    this.btnRecordVideo = btnRecordVideo;
    this.btnSettings = btnSettings;
    this.btnTakePhoto = btnTakePhoto;
    this.btnZoomIn = btnZoomIn;
    this.btnZoomOut = btnZoomOut;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTouptekBtnBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTouptekBtnBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_touptek_btn, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTouptekBtnBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_about;
      ImageButton btnAbout = ViewBindings.findChildViewById(rootView, id);
      if (btnAbout == null) {
        break missingId;
      }

      id = R.id.btn_draw;
      ImageButton btnDraw = ViewBindings.findChildViewById(rootView, id);
      if (btnDraw == null) {
        break missingId;
      }

      id = R.id.btn_folder;
      ImageButton btnFolder = ViewBindings.findChildViewById(rootView, id);
      if (btnFolder == null) {
        break missingId;
      }

      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_pause;
      ImageButton btnPause = ViewBindings.findChildViewById(rootView, id);
      if (btnPause == null) {
        break missingId;
      }

      id = R.id.btn_record_video;
      ImageButton btnRecordVideo = ViewBindings.findChildViewById(rootView, id);
      if (btnRecordVideo == null) {
        break missingId;
      }

      id = R.id.btn_settings;
      ImageButton btnSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSettings == null) {
        break missingId;
      }

      id = R.id.btn_take_photo;
      ImageButton btnTakePhoto = ViewBindings.findChildViewById(rootView, id);
      if (btnTakePhoto == null) {
        break missingId;
      }

      id = R.id.btn_zoom_in;
      ImageButton btnZoomIn = ViewBindings.findChildViewById(rootView, id);
      if (btnZoomIn == null) {
        break missingId;
      }

      id = R.id.btn_zoom_out;
      ImageButton btnZoomOut = ViewBindings.findChildViewById(rootView, id);
      if (btnZoomOut == null) {
        break missingId;
      }

      return new ActivityTouptekBtnBinding((LinearLayout) rootView, btnAbout, btnDraw, btnFolder,
          btnMenu, btnPause, btnRecordVideo, btnSettings, btnTakePhoto, btnZoomIn, btnZoomOut);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

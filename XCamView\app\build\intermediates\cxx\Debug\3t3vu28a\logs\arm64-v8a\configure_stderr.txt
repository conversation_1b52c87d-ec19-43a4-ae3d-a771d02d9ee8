CMake Warning (dev) in CMakeLists.txt:
  No project() command is present.  The top-level CMakeLists.txt file must
  contain a literal, direct call to the project() command.  Add a line of
  code such as

    project(ProjectName)

  near the top of the file, but after cmake_minimum_required().

  <PERSON><PERSON><PERSON> is pretending there is a "project(Project)" command on the first
  line.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning at C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake:63 (message):
  Using custom NDK path (ANDROID_NDK is set):
  C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
Call Stack (most recent call first):
  C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake:124 (include)
  CMakeLists.txt


CMake Warning:
  Manually-specified variables were not used by the project:

    CMAKE_EXPORT_COMPILE_COMMANDS
    CMAKE_RUNTIME_OUTPUT_DIRECTORY



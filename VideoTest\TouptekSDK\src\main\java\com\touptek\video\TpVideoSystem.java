package com.touptek.video;

import android.content.Context;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.ViewGroup;

import androidx.appcompat.app.AppCompatActivity;

import com.touptek.utils.TpFileManager;
import com.touptek.video.internal.TpCameraManager;
import com.touptek.video.internal.TpCaptureImage;
import com.touptek.video.internal.TpImageLoader;
import com.touptek.video.internal.TpTvPreview;
import com.touptek.video.internal.TpVideoDecoder;
import com.touptek.video.internal.TpVideoEncoder;
import com.touptek.video.internal.service.TpStreamingService;

/**
 * TpVideoSystem - ToupTek视频系统类
 * <p>
 * 基于VideoEncoderActivity的成功实现，提供统一的视频预览、录制和图像捕获功能。
 * 使用TpVideoConfig配置系统，支持灵活的视频参数配置，确保高质量的录制性能。
 * 采用双层API设计：外层简单API满足80%客户需求，内层专业API满足20%高级需求。
 * </p>
 *
 * <p><strong>⚠️ 重要：推流功能的正确使用方式</strong></p>
 * <p>为了支持RTSP推流功能，必须在Activity的onCreate()方法中创建TpVideoSystem实例：</p>
 * <pre>{@code
 * public class MainActivity extends AppCompatActivity {
 *     private TpVideoSystem videoSystem;
 *
 *     @Override
 *     protected void onCreate(Bundle savedInstanceState) {
 *         super.onCreate(savedInstanceState);
 *
 *         // ✅ 正确：在onCreate中创建，支持推流功能
 *         TpVideoConfig config = TpVideoConfig.createDefault1080P();
 *         videoSystem = new TpVideoSystem(this, config);
 *
 *         // 后续在TextureView准备好后调用initialize()
 *     }
 * }
 * }</pre>
 *
 * <p><strong>原因：</strong>RTSP推流功能需要注册ActivityResultLauncher来申请屏幕录制权限，
 * 而Android要求这种注册必须在Activity进入STARTED状态之前完成。</p>
 */
public class TpVideoSystem {
    private static final String TAG = "TpVideoSystem";
    
    // 核心组件
    private final Context context;
    private final AppCompatActivity activity; // 需要Activity用于StreamingService
    private TpVideoConfig videoConfig;
    private TpVideoEncoder tpVideoEncoder;
    private TpCameraManager tpCameraManager;
    private TpCaptureImage tpCaptureImage;

    // 推流组件
    private TpStreamingService tpStreamingService;

    // 状态管理
    private boolean isInitialized = false;
    private boolean isCameraStarted = false;
    private boolean isRecording = false;

    // 流类型管理
    private TpStreamingService.StreamType currentStreamType = TpStreamingService.StreamType.CAMERA; // 默认摄像头流
    private boolean isStreaming = false;

    // TV模式相关组件（懒加载）
    private TpTvPreview tpTvPreview;
    private boolean isTvMode = false;
    private ViewGroup tvContainer; // TV预览容器

    // 预览暂停状态
    private boolean isPreviewPaused = false;

    // ===== 播放功能字段 =====



    /**
     * 当前活动的视频播放器（简化API使用）
     * <p>用于简化API的内部管理，避免用户手动管理VideoDecoder实例</p>
     */
    private TpVideoDecoder currentTpVideoDecoder;
    private String currentVideoPath;

    // 回调接口
    private TpVideoSystemListener listener;

    // ===== 推流功能相关类型定义 =====

    /**
     * 流类型枚举
     * <p>完全封装的流类型定义，隐藏内部StreamingService实现细节</p>
     */
    public enum StreamType {
        /** 摄像头流 */
        CAMERA,
        /** 屏幕流 */
        SCREEN;

        /**
         * 转换为内部类型（内部使用）
         * @return 对应的StreamingService.StreamType
         */
        TpStreamingService.StreamType toInternalType() {
            return this == CAMERA ?
                TpStreamingService.StreamType.CAMERA :
                TpStreamingService.StreamType.SCREEN;
        }

        /**
         * 从内部类型转换（内部使用）
         * @param internalType 内部类型
         * @return 对应的TpVideoSystem.StreamType
         */
        static StreamType fromInternalType(TpStreamingService.StreamType internalType) {
            return internalType == TpStreamingService.StreamType.CAMERA ?
                CAMERA : SCREEN;
        }
    }
    
    /**
     * 视频系统监听器接口
     * <p>使用默认方法支持，只需要实现关心的事件</p>
     */
    public interface TpVideoSystemListener {
        /**
         * 发生错误（核心方法，建议实现）
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
        
        /**
         * 相机启动完成
         */
        default void onCameraStarted() {
            // 默认空实现
        }
        
        /**
         * 录制开始
         * @param outputPath 输出文件路径
         */
        default void onRecordingStarted(String outputPath) {
            // 默认空实现
        }
        
        /**
         * 录制停止
         * @param outputPath 输出文件路径
         */
        default void onRecordingStopped(String outputPath) {
            // 默认空实现
        }
        
        /**
         * 图像捕获完成
         * @param imagePath 图像文件路径
         */
        default void onImageCaptured(String imagePath) {
            // 默认空实现
        }

        /**
         * 推流状态变化回调
         * @param isStreaming 是否正在推流
         * @param rtspUrl 推流地址
         */
        default void onStreamingStatusChanged(boolean isStreaming, String rtspUrl) {
            // 默认空实现
        }


        // ===== 播放相关回调 =====

        /**
         * 视频播放开始回调
         * <p>当通过playVideo()方法成功开始播放视频时调用</p>
         *
         * @param videoPath 开始播放的视频文件路径
         */
        default void onVideoPlaybackStarted(String videoPath) {
            // 默认空实现
        }

        /**
         * 视频播放完成回调
         * <p>当视频播放到结尾自然完成时调用</p>
         *
         * @param videoPath 播放完成的视频文件路径
         */
        default void onVideoPlaybackCompleted(String videoPath) {
            // 默认空实现
        }

        /**
         * 视频播放停止回调
         * <p>当通过stopVideoPlayback()方法主动停止播放时调用</p>
         */
        default void onVideoPlaybackStopped() {
            // 默认空实现
        }

    }
    
    /**
     * 视频系统监听器适配器
     * <p>提供所有方法的默认实现，使用者只需重写关心的方法</p>
     */
    public static class TpVideoSystemAdapter implements TpVideoSystemListener {
        @Override
        public void onError(String errorMessage) {
            // 提供默认的错误处理 - 记录日志
            Log.e(TAG, "TpVideoSystem Error: " + errorMessage);
        }
        
        // 其他方法使用接口的默认实现
        // 子类可以选择性重写需要的方法
    }
    
    /**
     * 构造函数 - 使用默认1080P配置
     * @param activity Activity实例（推流功能需要）
     */
    public TpVideoSystem(AppCompatActivity activity) {
        this(activity, TpVideoConfig.createDefault1080P());
    }
    
    /**
     * 构造函数 - 使用自定义配置
     * @param activity Activity实例（推流功能需要）
     * @param config 视频配置
     */
    public TpVideoSystem(AppCompatActivity activity, TpVideoConfig config) {
        this.context = activity;
        this.activity = activity;
        this.videoConfig = config;

        // 立即进行StreamingService的第一步初始化（必须在onCreate中完成）
        initStreamingServiceEarly();

        Log.d(TAG, "TpVideoSystem created with config: " + config.toString());
    }

    /**
     * 设置监听器
     * @param listener 监听器
     */
    public void setListener(TpVideoSystemListener listener) {
        this.listener = listener;
    }
    
    // ===== 外层简单API（80%客户使用） =====
    
    /**
     * 初始化视频系统
     * @param previewSurface 预览Surface
     */
    public void initialize(Surface previewSurface) {
        if (isInitialized) {
            Log.w(TAG, "系统已经初始化");
            return;
        }
        
        try {
            Log.d(TAG, "开始初始化视频系统，配置: " + videoConfig.toString());
            
            // 初始化图像捕获助手
            initCaptureHelper();
            
            // 初始化视频编码器 - 使用TpVideoConfig
            initVideoEncoder(previewSurface);
            
            isInitialized = true;
            Log.d(TAG, "视频系统初始化完成");

        } catch (Exception e) {
            Log.e(TAG, "视频系统初始化失败", e);
            notifyListener(l -> l.onError("初始化失败: " + e.getMessage()));
        }
    }
    
    /**
     * 开始录制
     * @param outputPath 输出路径，如果为null则使用默认路径
     */
    public void startRecording(String outputPath) {
        // 统一的前置条件检查
        if (!checkCameraOperationPreconditions("录制功能")) {
            return;
        }

        if (isRecording) {
            Log.w(TAG, "已经在录制中");
            return;
        }

        // 直接调用核心组件
        if (outputPath == null) {
            outputPath = TpFileManager.createVideoPath(context);
        }

        try {
            tpVideoEncoder.startRecording(outputPath);
            isRecording = true;
            Log.d(TAG, "开始录制: " + outputPath);

            String finalOutputPath = outputPath;
            notifyListener(l -> l.onRecordingStarted(finalOutputPath));
        } catch (Exception e) {
            Log.e(TAG, "开始录制失败", e);
            notifyListener(l -> l.onError("开始录制失败: " + e.getMessage()));
        }
    }
    
    /**
     * 停止录制
     */
    public void stopRecording() {
        if (!isRecording) {
            Log.w(TAG, "未在录制中");
            return;
        }

        // 直接调用核心组件
        try {
            tpVideoEncoder.stopRecording();
            // isRecording状态会在onSaveComplete回调中设置为false
            Log.d(TAG, "停止录制");
        } catch (Exception e) {
            Log.e(TAG, "停止录制失败", e);
            isRecording = false;
            notifyListener(l -> l.onError("停止录制失败: " + e.getMessage()));
        }
    }
    
    /**
     * 捕获图像
     * @param outputPath 输出路径，如果为null则使用默认路径
     *  captureImage方法会根据路径的后缀名抓取对应格式的图片。
     */
    public void captureImage(String outputPath) {
        // 统一的前置条件检查
        if (!checkCameraOperationPreconditions("图像捕获功能")) {
            return;
        }

        // 直接调用核心组件
        if (outputPath == null) {
            outputPath = TpFileManager.createImagePath(context, "capture", true, "jpg");
        }

        try {
            Size imageSize = new Size(3840, 2160);
            tpCaptureImage.requestCapture(imageSize, outputPath);
            Log.d(TAG, "请求捕获图像: " + outputPath);
        } catch (Exception e) {
            Log.e(TAG, "捕获图像失败", e);
            notifyListener(l -> l.onError("捕获图像失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取录制状态
     */
    public boolean isRecording() {
        return isRecording;
    }
    
    /**
     * 获取相机启动状态
     */
    public boolean isCameraStarted() {
        return isCameraStarted;
    }
    
    /**
     * 获取初始化状态
     */
    public boolean isInitialized() {
        return isInitialized;
    }

    // ===== 图像处理功能 =====

    /**
     * 加载图像缩略图
     * <p>用于列表显示，小尺寸、低质量，节省内存和加载时间</p>
     *
     * @param imagePath 图像或视频文件路径
     * @param imageView 目标ImageView
     */
    public void loadThumbnail(String imagePath, android.widget.ImageView imageView) {
        if (imagePath == null || imageView == null) {
            Log.e(TAG, "loadThumbnail: 无效参数，路径或ImageView为null");
            notifyListener(l -> l.onError("图像加载失败：参数无效"));
            return;
        }

        try {
            TpImageLoader.loadThumbnail(imagePath, imageView);
            Log.d(TAG, "加载缩略图: " + imagePath);
        } catch (Exception e) {
            Log.e(TAG, "加载缩略图失败", e);
            notifyListener(l -> l.onError("缩略图加载失败: " + e.getMessage()));
        }
    }

    /**
     * 加载高质量图像
     * <p>用于详细查看，高质量显示</p>
     *
     * @param imagePath 图像文件路径
     * @param imageView 目标ImageView
     */
    public void loadFullImage(String imagePath, android.widget.ImageView imageView) {
        if (imagePath == null || imageView == null) {
            Log.e(TAG, "loadFullImage: 无效参数，路径或ImageView为null");
            notifyListener(l -> l.onError("图像加载失败：参数无效"));
            return;
        }

        try {
            TpImageLoader.loadFullImage(imagePath, imageView);
            Log.d(TAG, "加载高质量图像: " + imagePath);
        } catch (Exception e) {
            Log.e(TAG, "加载高质量图像失败", e);
            notifyListener(l -> l.onError("图像加载失败: " + e.getMessage()));
        }
    }

    // ===== 配置管理API =====

    /**
     * 获取当前视频配置
     */
    public TpVideoConfig getVideoConfig() {
        return videoConfig;
    }

    /**
     * 更新视频配置（仅在未初始化时允许）
     */
    public void updateVideoConfig(TpVideoConfig config) throws IllegalStateException {
        if (isInitialized) {
            throw new IllegalStateException("Cannot update config after initialization");
        }

        this.videoConfig = config;
        Log.d(TAG, "Video config updated: " + config.toString());
    }

    // ===== 80%简单API：分辨率设置方法 =====

    /**
     * 设置比特率
     * <p>
     * 设置视频比特率。支持初始化前配置和运行时动态调整。
     * 运行时调整适用于根据网络状况或存储需求动态调整视频质量。
     * </p>
     *
     * @param bitRate 比特率（bps），例如：8_000_000 表示8Mbps
     * @throws IllegalArgumentException 如果比特率无效（必须大于0）
     * @return 是否成功设置比特率
     */
    public boolean updateBitRate(int bitRate) throws IllegalArgumentException {
        if (bitRate <= 0) {
            throw new IllegalArgumentException("比特率必须大于0: " + bitRate);
        }

        // 运行时调整：直接调用VideoEncoder的API
        if (isInitialized && tpVideoEncoder != null) {
            Log.d(TAG, "运行时调整比特率: " + bitRate + " bps");
            boolean success = tpVideoEncoder.setBitrate(bitRate);

            if (success) {
                // 同步更新配置对象，保持状态一致
                updateConfigBitRate(bitRate);
                Log.d(TAG, "运行时比特率调整成功");
            } else {
                Log.e(TAG, "运行时比特率调整失败");
            }

            return success;
        }

        // 初始化前配置：修改TpVideoConfig
        else {
            Log.d(TAG, "初始化前设置比特率: " + bitRate + " bps");

            // 基于当前配置创建新配置，只修改比特率
            TpVideoConfig currentConfig = getVideoConfig();
            TpVideoConfig newConfig = new TpVideoConfig.Builder(
                    currentConfig.getWidth(),
                    currentConfig.getHeight())
                .setBitRate(bitRate)
                .setCodec(currentConfig.getCodec())
                .setBitrateMode(currentConfig.getBitrateMode())
                .build();

            updateVideoConfig(newConfig);
            Log.d(TAG, "初始化前比特率配置完成");
            return true;
        }
    }

    /**
     * 同步更新配置中的比特率（内部方法）
     * 用于运行时比特率调整后保持配置对象与实际状态一致
     */
    private void updateConfigBitRate(int bitRate) {
        if (videoConfig != null) {
            // 创建新的配置对象，只更新比特率
            TpVideoConfig updatedConfig = new TpVideoConfig.Builder(
                    videoConfig.getWidth(),
                    videoConfig.getHeight())
                .setBitRate(bitRate)
                .setCodec(videoConfig.getCodec())
                .setBitrateMode(videoConfig.getBitrateMode())
                .build();

            this.videoConfig = updatedConfig;
            Log.d(TAG, "配置对象比特率已同步更新: " + bitRate);
        }
    }

    /**
     * 设置分辨率
     * <p>
     * 通过HDMI重置机制实现分辨率动态调整。此方法会：
     * <ul>
     * <li>保存新的分辨率配置</li>
     * <li>执行HDMI重置（模拟拔出再插入）</li>
     * <li>在HDMI重新连接时自动应用新分辨率</li>
     * </ul>
     *
     * <p><b>⚠️ 注意事项：</b></p>
     * <ul>
     * <li>此操作会短暂中断视频预览（约1-2秒）</li>
     * <li>如果正在录制，会自动停止录制</li>
     * <li>如果正在推流，会自动停止推流</li>
     * <li>操作完成后需要重新开始录制或推流</li>
     * </ul>
     *
     * @param width 新的视频宽度
     * @param height 新的视频高度
     * @return 是否成功启动分辨率调整流程
     */
    public boolean updateResolution(int width, int height) {
        if (!isInitialized) {
            Log.w(TAG, "系统未初始化，将在初始化时应用分辨率设置");
            // 直接更新配置，在初始化时生效
            TpVideoConfig currentConfig = getVideoConfig();
            TpVideoConfig newConfig = new TpVideoConfig.Builder(width, height)
                .setBitRate(currentConfig.getBitRate())
                .setCodec(currentConfig.getCodec())
                .setBitrateMode(currentConfig.getBitrateMode())
                .build();
            this.videoConfig = newConfig;
            return true;
        }

        Log.d(TAG, "开始运行时分辨率调整: " + width + "x" + height);

        // 保存新的分辨率配置，供HDMI重新连接时使用
        TpVideoConfig currentConfig = getVideoConfig();
        TpVideoConfig newConfig = new TpVideoConfig.Builder(width, height)
            .setBitRate(currentConfig.getBitRate())
            .setCodec(currentConfig.getCodec())
            .setBitrateMode(currentConfig.getBitrateMode())
            .build();

        this.videoConfig = newConfig;

        // 停止当前的录制和推流
        if (isRecording) {
            Log.d(TAG, "停止录制以进行分辨率调整");
            stopRecording();
        }

        if (isStreaming()) {
            Log.d(TAG, "停止推流以进行分辨率调整");
            stopStreaming();
        }

        // 执行HDMI重置
        if (tpCameraManager != null) {
            Log.d(TAG, "执行HDMI重置...");
            boolean resetSuccess = tpCameraManager.resetHdmiRxViaScript();

            if (resetSuccess) {
                Log.d(TAG, "HDMI重置成功，等待重新连接并应用新分辨率");
                return true;
            } else {
                Log.e(TAG, "HDMI重置失败");
                return false;
            }
        } else {
            Log.e(TAG, "CameraManagerHelper未初始化，无法执行HDMI重置");
            return false;
        }
    }

    // ===== RTSP推流功能API =====

    /**
     * 启动推流（使用默认配置）
     * @return true表示启动成功
     */
    public boolean startStreaming() {
        return startStreaming(StreamType.CAMERA, null);
    }

    /**
     * 启动推流
     * @param streamType 流类型（CAMERA或SCREEN）
     * @return true表示启动成功
     */
    public boolean startStreaming(StreamType streamType) {
        return startStreaming(streamType, null);
    }

    /**
     * 启动推流
     * @param streamType 流类型
     * @param networkInterface 网络接口（null使用默认）
     * @return true表示启动成功
     */
    public boolean startStreaming(StreamType streamType, String networkInterface) {
        if (tpStreamingService == null) {
            Log.e(TAG, "推流服务不可用");
            notifyListener(l -> l.onError("推流服务未初始化"));
            return false;
        }

        Log.d(TAG, "启动推流: " + streamType + ", 网络接口: " + networkInterface);

        // 内部处理线程管理和错误处理
        return executeStreamingOperation(() -> {
            // 转换为内部类型
            TpStreamingService.StreamType internalType = streamType.toInternalType();
            return tpStreamingService.startRtspManually(internalType, networkInterface);
        });
    }

    /**
     * 停止推流
     * @return true表示停止成功
     */
    public boolean stopStreaming() {
        if (tpStreamingService == null) {
            Log.w(TAG, "推流服务不可用");
            return false;
        }

        Log.d(TAG, "停止推流");

        return executeStreamingOperation(() -> {
            return tpStreamingService.stopRtspManually();
        });
    }

    /**
     * 获取推流状态
     */
    public boolean isStreaming() {
        return tpStreamingService != null && tpStreamingService.isStreaming();
    }

    /**
     * 获取推流URL
     * @return 推流URL，如果未推流则返回null
     */
    public String getStreamUrl() {
        return tpStreamingService != null ? tpStreamingService.getStreamUrl() : null;
    }

    /**
     * 获取当前流类型
     * @return 当前流类型
     */
    public StreamType getCurrentStreamType() {
        if (tpStreamingService == null) {
            return null;
        }
        // 从内部类型转换为封装类型
        TpStreamingService.StreamType internalType = tpStreamingService.getCurrentStreamType();
        return internalType != null ? StreamType.fromInternalType(internalType) : null;
    }

    // ===== 流类型管理API =====

    /**
     * 设置推流类型
     * @param streamType 流类型（CAMERA或SCREEN）
     */
    public void setStreamType(StreamType streamType) {
        if (streamType == null) {
            Log.e(TAG, "流类型不能为null");
            return;
        }

        Log.d(TAG, "设置推流类型: " + streamType);
        // 转换为内部类型并保存
        TpStreamingService.StreamType internalType = streamType.toInternalType();
        this.currentStreamType = internalType;

        // 如果StreamingService已初始化，同步设置推流类型
        if (tpStreamingService != null) {
            tpStreamingService.setStreamType(internalType);
        }
    }


    // ===== TV模式核心API（简单层 - 80%客户使用） =====

    /**
     * 切换到TV模式
     * <p>
     * 切换到TV模式后，将停止相机预览并启动TV预览。
     * 在TV模式下，录制和图像捕获功能将被禁用。
     * </p>
     *
     * @throws IllegalStateException 如果当前状态不允许切换（如正在录制）
     */
    public void switchToTvMode() throws IllegalStateException {
        Log.d(TAG, "请求切换到TV模式");

        // 状态检查
        if (isTvMode) {
            Log.d(TAG, "已经是TV模式，无需切换");
            return;
        }

        if (isRecording) {
            throw new IllegalStateException("正在录制时不能切换到TV模式，请先停止录制");
        }

        try {
            // 停止相机预览
            stopCameraPreview();

            // 启动TV预览
            startTvPreview();

            // 更新状态
            isTvMode = true;

            Log.d(TAG, "成功切换到TV模式");


        } catch (Exception e) {
            Log.e(TAG, "切换到TV模式失败", e);
            notifyListener(l -> l.onError("切换到TV模式失败: " + e.getMessage()));
            throw new IllegalStateException("切换到TV模式失败: " + e.getMessage(), e);
        }
    }

    /**
     * 切换到Camera模式
     * <p>
     * 切换到Camera模式后，将停止TV预览并启动相机预览。
     * 在Camera模式下，录制和图像捕获功能将被启用。
     * </p>
     *
     * @throws IllegalStateException 如果当前状态不允许切换
     */
    public void switchToCameraMode() throws IllegalStateException {
        Log.d(TAG, "请求切换到Camera模式");

        // 状态检查
        if (!isTvMode) {
            Log.d(TAG, "已经是Camera模式，无需切换");
            return;
        }

        try {
            // 停止TV预览
            stopTvPreview();

            // 启动相机预览
            startCameraPreview();

            // 更新状态
            isTvMode = false;

            Log.d(TAG, "成功切换到Camera模式");


        } catch (Exception e) {
            Log.e(TAG, "切换到Camera模式失败", e);
            notifyListener(l -> l.onError("切换到Camera模式失败: " + e.getMessage()));
            throw new IllegalStateException("切换到Camera模式失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取当前是否为TV模式
     *
     * @return true表示TV模式，false表示Camera模式
     */
    public boolean isTvMode() {
        return isTvMode;
    }


    // ===== TV模式配置API =====

    /**
     * 设置TV预览容器
     * <p>
     * 必须在使用TV模式功能之前调用此方法设置TV预览的容器视图。
     * </p>
     *
     * @param container TV预览容器视图
     */
    public void setTvContainer(ViewGroup container) {
        this.tvContainer = container;
        Log.d(TAG, "TV容器已设置: " + (container != null ? container.getClass().getSimpleName() : "null"));

        // 如果TV预览助手已存在，需要重新初始化
        if (tpTvPreview != null) {
            tpTvPreview.release();
            tpTvPreview = null;
        }
    }

    // ===== 视频播放功能API =====





    // ===== 简化的视频播放API =====

    /**
     * 开始视频播放（简化版）
     * <p>
     * 提供简化的视频播放接口，内部自动管理VideoDecoder实例。
     * 如果已有视频在播放，会先停止当前播放再开始新的播放。
     * </p>
     *
     * @param videoPath 视频文件路径
     * @param surface 播放表面
     * @return 是否成功开始播放
     */
    public boolean playVideo(String videoPath, Surface surface) {
        if (videoPath == null || videoPath.trim().isEmpty()) {
            Log.e(TAG, "播放视频失败：视频路径为空");
            notifyListener(l -> l.onError("播放视频失败：视频路径不能为空"));
            return false;
        }

        if (surface == null) {
            Log.e(TAG, "播放视频失败：Surface为null");
            notifyListener(l -> l.onError("播放视频失败：Surface不能为null"));
            return false;
        }

        try {
            // 停止当前播放
            releaseVideo();

            Log.d(TAG, "开始播放视频: " + videoPath);

            // 直接创建VideoDecoder
            TpVideoDecoder tpVideoDecoder = new TpVideoDecoder(videoPath, surface);

            // 设置播放完成监听器
            tpVideoDecoder.setPlaybackListener(new TpVideoDecoder.VideoDecoderListener() {
                @Override
                public void onPlaybackCompleted() {
                    Log.d(TAG, "视频播放完成: " + videoPath);
                    notifyListener(l -> l.onVideoPlaybackCompleted(videoPath));
                }
            });

            // 开始解码
            tpVideoDecoder.startDecoding();

            // 保存为当前解码器
            currentTpVideoDecoder = tpVideoDecoder;
            currentVideoPath = videoPath;

            Log.d(TAG, "简化API：视频播放开始 - " + videoPath);
            notifyListener(l -> l.onVideoPlaybackStarted(videoPath));
            return true;

        } catch (Exception e) {
            Log.e(TAG, "简化API：开始视频播放异常", e);
            notifyListener(l -> l.onError("开始视频播放失败: " + e.getMessage()));
            return false;
        }
    }

    /**
     * 暂停视频播放（简化版）
     */
    public void pauseVideo() {
        if (currentTpVideoDecoder != null && !currentTpVideoDecoder.isPaused()) {
            currentTpVideoDecoder.togglePlayPause();
            Log.d(TAG, "简化API：视频播放暂停");
        }
    }

    /**
     * 恢复视频播放（简化版）
     */
    public void resumeVideo() {
        if (currentTpVideoDecoder != null && currentTpVideoDecoder.isPaused()) {
            currentTpVideoDecoder.togglePlayPause();
            Log.d(TAG, "简化API：视频播放恢复");
        }
    }

    /**
     * 停止视频播放（简化版）
     */
    public void releaseVideo() {
        if (currentTpVideoDecoder != null) {
            try {
                Log.d(TAG, "停止视频播放");
                currentTpVideoDecoder.stopDecoding();
                Log.d(TAG, "视频播放已停止");
                notifyListener(l -> l.onVideoPlaybackStopped());
            } catch (Exception e) {
                Log.e(TAG, "停止视频播放失败", e);
                notifyListener(l -> l.onError("停止视频播放失败: " + e.getMessage()));
            } finally {
                currentTpVideoDecoder = null;
                currentVideoPath = null;
                Log.d(TAG, "简化API：视频播放停止");
            }
        }
    }

    /**
     * 跳转到指定位置（简化版）
     *
     * @param position 目标位置（毫秒）
     */
    public void seekVideoTo(long position) {
        if (currentTpVideoDecoder != null) {
            currentTpVideoDecoder.seekTo(position * 1000); // 转换为微秒
            Log.d(TAG, "简化API：视频跳转到 " + position + "ms");
        }
    }

    /**
     * 检查是否正在播放视频（简化版）
     *
     * @return true表示正在播放
     */
    public boolean isVideoPlaying() {
        return currentTpVideoDecoder != null && !currentTpVideoDecoder.isPaused();
    }

    /**
     * 获取当前播放位置（简化版）
     *
     * @return 当前位置（毫秒）
     */
    public long getCurrentVideoPosition() {
        if (currentTpVideoDecoder != null) {
            return currentTpVideoDecoder.getCurrentPosition() / 1000; // 转换为毫秒
        }
        return 0;
    }

    /**
     * 获取视频总时长（简化版）
     *
     * @return 视频总时长（毫秒）
     */
    public long getVideoDuration() {
        if (currentTpVideoDecoder != null) {
            return currentTpVideoDecoder.getVideoDuration() / 1000; // 转换为毫秒
        }
        return 0;
    }

    /**
     * 获取当前播放的视频路径（简化版）
     *
     * @return 当前视频路径，如果没有播放则返回null
     */
    public String getCurrentVideoPath() {
        return currentVideoPath;
    }

    // ===== 高级播放控制API（简化版） =====

    /**
     * 设置当前视频的播放速度（简化版）
     * <p>
     * 为当前正在播放的视频设置播放速度。支持变速播放功能，
     * 常用于快速浏览或慢动作分析。
     * </p>
     *
     * @param speed 播放速度倍率（0.25f - 2.0f），1.0f表示正常速度
     * @return 是否设置成功
     */
    public boolean setCurrentVideoPlaybackSpeed(float speed) {
        if (currentTpVideoDecoder != null) {
            currentTpVideoDecoder.setPlaybackSpeed(speed);
            Log.d(TAG, "简化API：设置播放速度 " + speed + "x");
            return true;
        } else {
            Log.w(TAG, "简化API：无法设置播放速度，视频未播放");
            return false;
        }
    }

    /**
     * 获取当前视频的播放速度（简化版）
     *
     * @return 当前播放速度倍率，如果没有视频播放则返回1.0f
     */
    public float getCurrentVideoPlaybackSpeed() {
        if (currentTpVideoDecoder != null) {
            return currentTpVideoDecoder.getPlaybackSpeed();
        }
        return 1.0f; // 默认正常速度
    }

    /**
     * 当前视频逐帧播放（简化版）
     * <p>
     * 解码并显示当前视频的下一帧，然后暂停。
     * 适用于精确的帧分析。
     * </p>
     *
     * @return 是否执行成功
     */
    public boolean stepCurrentVideoFrame() {
        if (currentTpVideoDecoder != null) {
            currentTpVideoDecoder.stepFrame();
            Log.d(TAG, "简化API：逐帧播放");
            return true;
        } else {
            Log.w(TAG, "简化API：无法逐帧播放，视频未播放");
            return false;
        }
    }

    /**
     * 当前视频相对跳转（简化版）
     * <p>
     * 基于当前播放位置进行时间偏移跳转。
     * </p>
     *
     * @param deltaMs 时间偏移量（毫秒），正数前进，负数后退
     * @return 是否跳转成功
     */
    public boolean seekCurrentVideoRelative(long deltaMs) {
        if (currentTpVideoDecoder != null) {
            currentTpVideoDecoder.seekRelative(deltaMs);
            Log.d(TAG, "简化API：相对跳转 " + deltaMs + "ms");
            return true;
        } else {
            Log.w(TAG, "简化API：无法相对跳转，视频未播放");
            return false;
        }
    }



    /**
     * 检查当前视频播放是否已完成（简化版）
     *
     * @return true表示播放完成
     */
    public boolean isCurrentVideoPlaybackCompleted() {
        if (currentTpVideoDecoder != null) {
            return currentTpVideoDecoder.isPlaybackCompleted();
        }
        return false;
    }

    /**
     * 重置当前视频到开始位置（简化版）
     * <p>
     * 将当前视频的播放位置重置到开头。
     * </p>
     *
     * @return 是否重置成功
     */
    public boolean resetCurrentVideoToStart() {
        if (currentTpVideoDecoder != null) {
            currentTpVideoDecoder.resetToStart();
            Log.d(TAG, "简化API：重置到开始位置");
            return true;
        } else {
            Log.w(TAG, "简化API：无法重置，视频未播放");
            return false;
        }
    }

    // ===== 预览暂停/恢复功能 =====

    /**
     * 暂停预览
     */
    public void pausePreview() {
        if (isPreviewPaused || isTvMode) return; // TV模式直接返回

        stopCameraPreview();
        isPreviewPaused = true;
        Log.d(TAG, "预览已暂停");
    }

    /**
     * 恢复预览
     */
    public void resumePreview() {
        if (!isPreviewPaused || isTvMode) return; // TV模式直接返回

        startCameraPreview();
        isPreviewPaused = false;
        Log.d(TAG, "预览已恢复");
    }

    /**
     * 切换预览暂停状态
     */
    public void togglePreviewPause() {
        if (isPreviewPaused) {
            resumePreview();
        } else {
            pausePreview();
        }
    }

    /**
     * 获取预览暂停状态
     * @return true表示预览已暂停
     */
    public boolean isPreviewPaused() {
        return isPreviewPaused;
    }

    // ===== 内层专业API（20%客户使用） =====

    /**
     * 获取视频编码器（高级用户）
     * <p>⚠️ 高级用户专用API</p>
     * <p>提供对VideoEncoder的直接访问，用于高级视频编码控制。
     * 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
     * 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简单API。</p>
     *
     * @return VideoEncoder实例，如果未初始化则返回null
     */
    public TpVideoEncoder getVideoEncoder() {
        return getAdvancedComponent(tpVideoEncoder, "TpVideoEncoder");
    }

    /**
     * 获取相机管理器（高级用户）
     * <p>⚠️ 高级用户专用API</p>
     * <p>提供对CameraManagerHelper的直接访问，用于高级相机控制和参数调整。
     * 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
     * 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简单API。</p>
     *
     * @return CameraManagerHelper实例，如果未初始化则返回null
     */
    public TpCameraManager getCameraManager() {
        return getAdvancedComponent(tpCameraManager, "TpCameraManager");
    }

    /**
     * 获取图像捕获器（高级用户）
     * <p>⚠️ 高级用户专用API</p>
     * <p>提供对CaptureImageHelper的直接访问，用于高级图像捕获控制。
     * 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
     * 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简单API。</p>
     *
     * @return CaptureImageHelper实例，如果未初始化则返回null
     */
    public TpCaptureImage getImageCapture() {
        return getAdvancedComponent(tpCaptureImage, "TpCaptureImage");
    }

    /**
     * 获取视频解码器（高级用户）
     * <p>⚠️ 高级用户专用API</p>
     * <p>提供对当前VideoDecoder的直接访问，用于高级播放控制。
     * 此方法返回通过playVideo()方法创建的当前VideoDecoder实例。
     * 高级用户可以通过此实例进行精确的播放控制，如变速播放、逐帧播放、精确跳转等。
     * 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
     * 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简化API。</p>
     *
     * <p><b>使用示例：</b></p>
     * <pre>{@code
     * // 先通过简化API创建播放器
     * videoSystem.playVideo(videoPath, surface);
     *
     * // 高级用户获取VideoDecoder进行精确控制
     * TpVideoDecoder decoder = videoSystem.getVideoDecoder();
     * if (decoder != null) {
     *     decoder.setPlaybackSpeed(1.5f);        // 1.5倍速播放
     *     decoder.stepFrame();                    // 逐帧播放
     *     decoder.seekRelative(5000);             // 相对跳转5秒
     *     decoder.seekTo(30000000);               // 跳转到30秒位置
     *
     *     // 状态查询
     *     boolean isPaused = decoder.isPaused();
     *     long duration = decoder.getVideoDuration();
     *     long position = decoder.getCurrentPosition();
     * }
     * }</pre>
     *
     * @return 当前通过playVideo()创建的VideoDecoder实例，如果没有则返回null
     */
    public TpVideoDecoder getVideoDecoder() {
        if (currentTpVideoDecoder == null) {
            Log.w(TAG, "没有可用的VideoDecoder实例，请先调用playVideo()方法");
        }
        return getAdvancedComponent(currentTpVideoDecoder, "TpVideoDecoder");
    }

    /**
     * 获取推流服务（高级用户）
     * <p>⚠️ 高级用户专用API</p>
     * <p>提供对StreamingService的直接访问，用于高级推流控制。
     * 此方法提供对内部组件的直接访问，需要深入了解组件工作原理。
     * 不当使用可能导致系统不稳定。建议优先使用TpVideoSystem的简单API。</p>
     *
     * @return StreamingService实例，如果未初始化则返回null
     */
    public TpStreamingService getStreamingService() {
        return getAdvancedComponent(tpStreamingService, "TpStreamingService");
    }

    /**
     * 获取TV预览助手实例（高级功能）
     * <p>
     * 此方法用于高级用户直接访问TV预览助手进行深度定制。
     * 如果TV预览助手尚未初始化，此方法会自动初始化它。
     * </p>
     *
     * @return TvPreviewHelper实例，如果TV容器未设置则返回null
     */
    public TpTvPreview getTvPreviewHelper() {
        initTvPreviewHelperIfNeeded();
        return tpTvPreview;
    }



    /**
     * 释放资源
     */
    public void release() {
        Log.d(TAG, "释放视频系统资源");

        // 新增：清理播放相关资源
        releaseVideo(); // 停止当前简化API的播放

        // 停止录制
        if (isRecording) {
            stopRecording();
        }

        // 停止推流 - 按照VideoEncoderActivity的模式
        if (tpStreamingService != null && tpStreamingService.isStreaming()) {
            try {
                tpStreamingService.stopRtspManually();
            } catch (Exception e) {
                Log.w(TAG, "停止推流服务时出现异常", e);
            }
        }

        // 释放相机资源
        if (tpCameraManager != null) {
            tpCameraManager.releaseCamera();
            tpCameraManager = null;
        }

        // 释放视频编码器
        if (tpVideoEncoder != null) {
            tpVideoEncoder.release();
            tpVideoEncoder = null;
        }

        // 释放TV预览助手
        if (tpTvPreview != null) {
            tpTvPreview.release();
            tpTvPreview = null;
        }

        // 重置状态
        isInitialized = false;
        isCameraStarted = false;
        isRecording = false;
        isStreaming = false;
        isTvMode = false;
        isPreviewPaused = false;

        Log.d(TAG, "视频系统资源释放完成");
    }

    // ===== 私有工具方法 =====

    /**
     * 统一的监听器通知方法
     */
    private void notifyListener(ListenerAction action) {
        if (listener != null) {
            action.execute(listener);
        }
    }

    /**
     * 监听器操作函数式接口
     */
    @FunctionalInterface
    private interface ListenerAction {
        void execute(TpVideoSystemListener listener);
    }

    /**
     * 检查相机操作前置条件
     */
    private boolean checkCameraOperationPreconditions(String operationName) {
        if (!isInitialized || !isCameraStarted) {
            String errorMsg = "系统未初始化或相机未启动";
            Log.e(TAG, errorMsg);
            notifyListener(l -> l.onError(errorMsg));
            return false;
        }

        if (isTvMode) {
            String errorMsg = "TV模式下不支持" + operationName + "，请切换到Camera模式";
            Log.e(TAG, errorMsg);
            notifyListener(l -> l.onError(errorMsg));
            return false;
        }

        return true;
    }

    /**
     * 高级API组件获取的统一方法
     */
    private <T> T getAdvancedComponent(T component, String componentName) {
        if (!isInitialized) {
            Log.w(TAG, "系统未初始化，" + componentName + "可能不可用");
        }
        return component;
    }

    // ===== 私有实现方法 =====

    /**
     * 初始化图像捕获助手
     */
    private void initCaptureHelper() {
        tpCaptureImage = TpCaptureImage.builder(new Size(3840, 2160))
                .onImageSaved(filePath -> {
                    Log.d(TAG, "图片已保存: " + filePath);
                    notifyListener(l -> l.onImageCaptured(filePath));
                })
                .onError(errorMessage -> {
                    Log.e(TAG, "图片捕获失败: " + errorMessage);
                    notifyListener(l -> l.onError("图片捕获失败: " + errorMessage));
                })
                .build();
    }

    /**
     * 初始化视频编码器
     */
    private void initVideoEncoder(Surface textureSurface) {
        Log.d(TAG, "初始化视频编码器，使用TpVideoConfig: " + videoConfig.toString());

        tpVideoEncoder = TpVideoEncoder.builder()
            .setPreviewSurface(textureSurface)
            .setTpVideoConfig(videoConfig)
            .onSurfaceAvailable(encoderSurface -> {
                // 使用与VideoEncoderActivity相同的相机初始化流程
                tpCameraManager = TpCameraManager.builder(context)
                    .onCameraOpened(camera -> {
                        // 相机打开后配置输出
                        tpCameraManager.configCameraOutputs(
                            camera,
                            encoderSurface,
                            tpCaptureImage.getImageReader().getSurface()
                        );

                        // 初始化推流服务
                        initializeStreamingService();

                        isCameraStarted = true;
                        Log.d(TAG, "相机启动完成，使用配置: " + videoConfig.toString());

                        notifyListener(TpVideoSystemListener::onCameraStarted);
                    })
                    .onCameraDisconnected(camera -> {
                        camera.close();
                        isCameraStarted = false;
                        Log.d(TAG, "相机已断开");
                    })
                    .onCameraError((camera, error) -> {
                        camera.close();
                        isCameraStarted = false;
                        Log.e(TAG, "相机错误: " + error);
                        notifyListener(l -> l.onError("相机错误: " + error));
                    })
                    .build();

                // 打开相机
                tpCameraManager.openCamera();
            })
            .onStorageFull(() -> {
                Log.w(TAG, "存储空间不足，录制已停止");
                stopRecording();
                notifyListener(l -> l.onError("存储空间不足，录制已停止"));
            })
            .onError((errorType, e) -> {
                Log.e(TAG, "录制错误: " + errorType, e);
                stopRecording();
                notifyListener(l -> l.onError("录制错误: " + errorType));
            })
            .onFileSizeLimitReached(() -> {
                Log.w(TAG, "文件大小达到限制，录制已停止");
                stopRecording();
                notifyListener(l -> l.onError("文件大小达到限制，录制已停止"));
            })
            .onSaveComplete(filePath -> {
                Log.d(TAG, "录制完成: " + filePath);
                isRecording = false;
                notifyListener(l -> l.onRecordingStopped(filePath));
            })
            .build();
    }

    /**
     * 第一步：提前初始化StreamingService（在构造函数中调用）
     */
    private void initStreamingServiceEarly() {
        // 如果没有Activity，跳过推流服务初始化
        if (activity == null) {
            Log.w(TAG, "跳过推流服务初始化：没有Activity实例");
            return;
        }

        try {
            Log.d(TAG, "初始化StreamingService...");

            // 按照VideoEncoderActivity的模式：在onCreate中调用initCore
            tpStreamingService = TpStreamingService.initCore(activity, createHeartbeatListener());

            Log.d(TAG, "StreamingService初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "StreamingService初始化失败", e);
            tpStreamingService = null;
        }
    }

    /**
     * 第二步：完成推流服务初始化
     * 完全按照VideoEncoderActivity.setupHeartbeatServiceComponents()的模式
     */
    private void initializeStreamingService() {
        // 如果没有Activity或StreamingService，跳过
        if (activity == null || tpStreamingService == null) {
            Log.w(TAG, "跳过推流服务完成初始化：Activity或StreamingService不可用");
            return;
        }

        try {
            Log.d(TAG, "完成StreamingService初始化...");

            // 按照VideoEncoderActivity的确切模式，使用当前设置的流类型
            tpStreamingService.initStreaming(
                    tpVideoEncoder,
                    tpCaptureImage,
                currentStreamType
            );

            // 按照VideoEncoderActivity的模式：启动服务
            tpStreamingService.startService();

            Log.d(TAG, "StreamingService完整初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "StreamingService完成初始化失败", e);
            notifyListener(l -> l.onError("推流服务初始化失败: " + e.getMessage()));
        }
    }

    /**
     * 创建HeartbeatListener
     * 完全按照VideoEncoderActivity.createHeartbeatListener()的模式
     */
    private TpStreamingService.HeartbeatListener createHeartbeatListener() {
        return new TpStreamingService.HeartbeatListener() {
            @Override
            public void onStreamStatusChanged(boolean isStreaming, String url) {
                // 通知外部监听器，让MainActivity更新UI状态
                notifyListener(l -> l.onStreamingStatusChanged(isStreaming, url));
            }

            @Override
            public void onStreamError(String errorMessage) {
                notifyListener(l -> l.onError("推流错误: " + errorMessage));
            }
        };
    }

    // ===== TV模式私有实现方法 =====

    /**
     * 初始化TV预览助手（懒加载）
     */
    private void initTvPreviewHelperIfNeeded() {
        if (tpTvPreview == null && tvContainer != null) {
            tpTvPreview = new TpTvPreview(context, tvContainer);
            Log.d(TAG, "TV预览助手已初始化");
        }
    }

    /**
     * 启动TV预览
     */
    private void startTvPreview() {
        Log.d(TAG, "启动TV预览");

        // 确保TV预览助手已初始化
        initTvPreviewHelperIfNeeded();

        if (tpTvPreview == null) {
            throw new IllegalStateException("TV预览助手未初始化，请先调用setTvContainer()设置TV容器");
        }

        // 启动TV预览
        tpTvPreview.startPreview();
        Log.d(TAG, "TV预览已启动");
    }

    /**
     * 停止TV预览
     */
    private void stopTvPreview() {
        Log.d(TAG, "停止TV预览");

        if (tpTvPreview != null) {
            tpTvPreview.stopPreview();
            Log.d(TAG, "TV预览已停止");
        }
    }

    /**
     * 停止相机预览
     */
    private void stopCameraPreview() {
        Log.d(TAG, "停止相机预览");

        // 如果正在录制，先停止录制
        if (isRecording) {
            stopRecording();
        }

        // 释放相机资源
        if (tpCameraManager != null) {
            tpCameraManager.releaseCamera();
            isCameraStarted = false;
        }

        Log.d(TAG, "相机预览已停止");
    }

    /**
     * 启动相机预览
     */
    private void startCameraPreview() {
        Log.d(TAG, "启动相机预览");

        if (!isInitialized) {
            throw new IllegalStateException("系统未初始化，无法启动相机预览");
        }

        // 重新启动相机
        if (!isCameraStarted && tpCameraManager != null) {
            try {
                Log.d(TAG, "重新启动相机");

                // 调用openCamera重新启动相机
                tpCameraManager.openCamera();
                isCameraStarted = true;

                Log.d(TAG, "相机预览已重新启动");

                notifyListener(TpVideoSystemListener::onCameraStarted);
            } catch (Exception e) {
                Log.e(TAG, "重新启动相机预览失败", e);
                throw new IllegalStateException("重新启动相机预览失败: " + e.getMessage(), e);
            }
        }
    }



    // ===== 推流功能内部辅助方法 =====

    /**
     * 执行推流操作（内部线程管理）
     */
    private boolean executeStreamingOperation(StreamingOperation operation) {
        try {
            // 直接执行操作（StreamingService内部已处理线程管理）
            return operation.execute();
        } catch (Exception e) {
            Log.e(TAG, "推流操作失败", e);
            notifyListener(l -> l.onError("推流操作失败: " + e.getMessage()));
            return false;
        }
    }



    // ===== 高级功能方法（20%客户使用） =====

    /**
     * 推流操作函数式接口
     */
    @FunctionalInterface
    private interface StreamingOperation {
        boolean execute();
    }
}

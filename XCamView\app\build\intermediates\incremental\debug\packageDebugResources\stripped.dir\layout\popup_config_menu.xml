<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/menu_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/popup_background"
    android:padding="4dp"
    android:minWidth="120dp"
    android:elevation="8dp">

    <!-- 复制菜单项 -->
    <LinearLayout
        android:id="@+id/menu_copy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_copy"
            android:tint="@color/popup_text_color"/>

        <TextView
            android:id="@+id/text_copy"
            android:tag="menu_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/menu_copy"
            android:textColor="@color/popup_text_color"
            android:layout_marginStart="8dp"/>
    </LinearLayout>

    <!-- 剪切菜单项 -->
    <LinearLayout
        android:id="@+id/menu_cut"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_cut"
            android:tint="@color/popup_text_color"/>

        <TextView
            android:tag="menu_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/menu_cut"
            android:textColor="@color/popup_text_color"
            android:layout_marginStart="8dp"/>
    </LinearLayout>

    <!-- 粘贴菜单项 -->
    <LinearLayout
        android:id="@+id/menu_paste"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_paste"
            android:tint="@color/popup_text_color"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/menu_paste"
            android:textColor="@color/popup_text_color"
            android:layout_marginStart="8dp"/>
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#444444"
        android:layout_marginVertical="4dp"/>

    <!-- 图片对比菜单项 -->
    <LinearLayout
        android:id="@+id/menu_compare"
        android:layout_width="140dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="8dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_compare"
            android:tint="@color/popup_text_color" />

        <TextView
            android:tag="menu_text_compare"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="图片对比"
            android:textColor="@color/popup_text_color" />
    </LinearLayout>



    <!-- 详细信息菜单项 -->
    <LinearLayout
        android:id="@+id/menu_details"
        android:layout_width="140dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="8dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_details"
            android:tint="@color/popup_text_color" />

        <TextView
            android:tag="menu_text_details"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/menu_details"
            android:textColor="@color/popup_text_color" />
    </LinearLayout>
</LinearLayout>
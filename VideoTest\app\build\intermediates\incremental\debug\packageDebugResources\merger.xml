<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res"><file name="tp_speed_dialog_enter" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\anim\tp_speed_dialog_enter.xml" qualifiers="" type="anim"/><file name="tp_speed_dialog_exit" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\anim\tp_speed_dialog_exit.xml" qualifiers="" type="anim"/><file name="tp_speed_dropdown_enter" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\anim\tp_speed_dropdown_enter.xml" qualifiers="" type="anim"/><file name="tp_speed_dropdown_exit" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\anim\tp_speed_dropdown_exit.xml" qualifiers="" type="anim"/><file name="tp_video_button_press" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\animator\tp_video_button_press.xml" qualifiers="" type="animator"/><file name="tp_video_button_release" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\animator\tp_video_button_release.xml" qualifiers="" type="animator"/><file name="tp_video_button_text_color" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\color\tp_video_button_text_color.xml" qualifiers="" type="color"/><file name="dialog_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="ic_fast_forward_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_fast_forward_white_24.xml" qualifiers="" type="drawable"/><file name="ic_fast_rewind_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_fast_rewind_white_24.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_pause_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_pause_white_24.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_play_arrow_white_24.xml" qualifiers="" type="drawable"/><file name="ic_settings_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_settings_white_24.xml" qualifiers="" type="drawable"/><file name="ic_skip_next_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_skip_next_white_24.xml" qualifiers="" type="drawable"/><file name="ic_skip_previous_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_skip_previous_white_24.xml" qualifiers="" type="drawable"/><file name="ic_step_frame_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\ic_step_frame_white_24.xml" qualifiers="" type="drawable"/><file name="tp_speed_dropdown_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_speed_dropdown_background.xml" qualifiers="" type="drawable"/><file name="tp_speed_item_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_speed_item_background.xml" qualifiers="" type="drawable"/><file name="tp_speed_item_selected_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_speed_item_selected_background.xml" qualifiers="" type="drawable"/><file name="tp_speed_menu_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_speed_menu_background.xml" qualifiers="" type="drawable"/><file name="tp_video_button_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_video_button_background.xml" qualifiers="" type="drawable"/><file name="tp_video_controls_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_video_controls_background.xml" qualifiers="" type="drawable"/><file name="tp_video_play_button_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_video_play_button_background.xml" qualifiers="" type="drawable"/><file name="tp_video_progress_drawable" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_video_progress_drawable.xml" qualifiers="" type="drawable"/><file name="tp_video_progress_thumb" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_video_progress_thumb.xml" qualifiers="" type="drawable"/><file name="tp_video_settings_button_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\drawable\tp_video_settings_button_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_tp_video_player_new" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\activity_tp_video_player_new.xml" qualifiers="" type="layout"/><file name="decoder" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\decoder.xml" qualifiers="" type="layout"/><file name="dialog_image_format_settings" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\dialog_image_format_settings.xml" qualifiers="" type="layout"/><file name="dialog_smb_settings" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\dialog_smb_settings.xml" qualifiers="" type="layout"/><file name="dialog_tp_settings" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\dialog_tp_settings.xml" qualifiers="" type="layout"/><file name="encoder" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\encoder.xml" qualifiers="" type="layout"/><file name="fragment_tp_network_settings" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\fragment_tp_network_settings.xml" qualifiers="" type="layout"/><file name="fragment_tp_smb_settings" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\fragment_tp_smb_settings.xml" qualifiers="" type="layout"/><file name="fragment_tp_tv_mode_settings" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\fragment_tp_tv_mode_settings.xml" qualifiers="" type="layout"/><file name="image_viewer" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\image_viewer.xml" qualifiers="" type="layout"/><file name="item_settings_menu" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\item_settings_menu.xml" qualifiers="" type="layout"/><file name="media_browser" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\media_browser.xml" qualifiers="" type="layout"/><file name="media_browser_integrated" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\media_browser_integrated.xml" qualifiers="" type="layout"/><file name="media_item" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\media_item.xml" qualifiers="" type="layout"/><file name="network_settings" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\network_settings.xml" qualifiers="" type="layout"/><file name="popup_menu" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\popup_menu.xml" qualifiers="" type="layout"/><file name="spinner_item" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\spinner_item.xml" qualifiers="" type="layout"/><file name="tp_speed_dropdown_menu" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\tp_speed_dropdown_menu.xml" qualifiers="" type="layout"/><file name="tp_speed_selection_dialog" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\tp_speed_selection_dialog.xml" qualifiers="" type="layout"/><file name="tp_video_player_controls" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\tp_video_player_controls.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="image_format_options">
        <item>JPEG</item>
        <item>PNG</item>
        <item>BMP</item>
    </string-array></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="TpImageView">
        <attr format="float" name="maxScale"/>
        <attr format="boolean" name="zoomEnabled"/>
        <attr format="boolean" name="panEnabled"/>
        <attr format="boolean" name="doubleTapEnabled"/>
    </declare-styleable><declare-styleable name="TpVideoPlayerView">
        <attr format="boolean" name="autoPlay"/>
    </declare-styleable></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">VideoTest</string></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.MediacodecNew" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.MediacodecNew" parent="Base.Theme.MediacodecNew"/><style name="FullScreenDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values\tp_video_styles.xml" qualifiers=""><style name="TpVideoControlButton" parent="Widget.AppCompat.ImageButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@drawable/tp_video_button_background</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:layout_marginEnd">12dp</item>
        <item name="android:scaleType">center</item>
        <item name="android:contentDescription">控制按钮</item>
    </style><style name="TpVideoPlayButton" parent="TpVideoControlButton">
        <item name="android:layout_width">64dp</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:background">@drawable/tp_video_play_button_background</item>
        <item name="android:elevation">6dp</item>
        <item name="android:contentDescription">播放/暂停</item>
    </style><style name="TpVideoSettingsButton" parent="TpVideoControlButton">
        <item name="android:background">@drawable/tp_video_settings_button_background</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:contentDescription">设置</item>
    </style><style name="TpVideoTimeDisplay" parent="Widget.AppCompat.TextView">
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:shadowColor">#80000000</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">2</item>
    </style><style name="TpVideoProgressBar" parent="Widget.AppCompat.SeekBar">
        <item name="android:progressDrawable">@drawable/tp_video_progress_drawable</item>
        <item name="android:thumb">@drawable/tp_video_progress_thumb</item>
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">24dp</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:thumbOffset">0dp</item>
    </style><style name="TpVideoSpeedDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowAnimationStyle">@style/TpVideoSpeedDialogAnimation</item>
    </style><style name="TpVideoSpeedDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/tp_speed_dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/tp_speed_dialog_exit</item>
    </style><style name="TpVideoSpeedDropdownAnimation">
        <item name="android:windowEnterAnimation">@anim/tp_speed_dropdown_enter</item>
        <item name="android:windowExitAnimation">@anim/tp_speed_dropdown_exit</item>
    </style><style name="TpVideoSpeedItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style><style name="TpVideoSpeedDropdownItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:layout_marginBottom">2dp</item>
        <item name="android:paddingHorizontal">12dp</item>
    </style></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values\video_player_styles.xml" qualifiers=""><style name="VideoControlButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/tp_video_button_background</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">12sp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:padding">8dp</item>
    </style><style name="VideoSpeedButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">10sp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:padding">4dp</item>
    </style></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.MediacodecNew" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\values-night\tp_video_colors.xml" qualifiers="night-v8"><color name="tp_video_button_bg_normal">#FF1A1A1A</color><color name="tp_video_button_bg_pressed">#FF2A2A2A</color><color name="tp_video_button_bg_disabled">#FF0A0A0A</color><color name="tp_video_play_button_bg_normal">#FF000000</color><color name="tp_video_play_button_bg_pressed">#FF333333</color><color name="tp_video_button_stroke_normal">#4DFFFFFF</color><color name="tp_video_button_stroke_pressed">#66FFFFFF</color><color name="tp_video_button_stroke_disabled">#33FFFFFF</color><color name="tp_video_text_normal">#FFFFFFFF</color><color name="tp_video_text_disabled">#66FFFFFF</color><color name="tp_video_progress_bg">#33FFFFFF</color><color name="tp_video_progress_secondary">#66FFFFFF</color><color name="tp_video_progress_primary">#FFFFFFFF</color><color name="tp_video_progress_thumb">#FFFFFFFF</color><color name="tp_video_progress_thumb_stroke">#FF000000</color></file><file name="backup_rules" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="dialog_tp_test" path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\res\layout\dialog_tp_test.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\app\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="TpImageView">
        <attr format="float" name="maxScale"/>
        <attr format="boolean" name="zoomEnabled"/>
        <attr format="boolean" name="panEnabled"/>
        <attr format="boolean" name="doubleTapEnabled"/>
    </declare-styleable><declare-styleable name="TpVideoPlayerView">
        <attr format="boolean" name="autoPlay"/>
    </declare-styleable></configuration></mergedItems></merger>
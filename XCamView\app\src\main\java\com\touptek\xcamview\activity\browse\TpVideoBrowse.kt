package com.touptek.xcamview.activity.browse

import FolderAdapter
import android.Manifest
import android.app.ProgressDialog
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.storage.StorageManager
import android.text.InputFilter
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.touptek.xcamview.R
import com.touptek.xcamview.activity.MainActivity
import com.touptek.xcamview.activity.compare.TpImageCompareActivity
import com.touptek.xcamview.activity.compare.TpImageCompareMultiActivity
import com.touptek.xcamview.databinding.BrowseLayoutBinding
import com.touptek.xcamview.databinding.RightPanelLayoutBinding
import com.touptek.utils.TpFileManager
import com.touptek.xcamview.util.dpToPx
import com.touptek.xcamview.util.getStorageDCIMPath
import com.touptek.xcamview.util.getStoragePicturePath
import com.touptek.xcamview.util.getStorageVideoPath
import java.io.File
import java.nio.file.Files
import java.nio.file.LinkOption
import java.nio.file.Paths
import java.nio.file.attribute.BasicFileAttributes
import java.util.Locale
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.media.MediaMetadataRetriever
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import android.media.MediaMetadataRetriever.METADATA_KEY_DURATION
import android.media.MediaMetadataRetriever.METADATA_KEY_BITRATE
import android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH
import android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT
import android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_FRAME_COUNT
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment
import com.touptek.xcamview.activity.browse.videomanagement.TpVideoDecoderDialogFragment
import java.io.FileInputStream
import java.io.IOException
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.Date
import kotlin.apply
import kotlin.collections.contentEquals
import kotlin.collections.filter
import kotlin.collections.forEach
import kotlin.collections.isNotEmpty
import kotlin.collections.joinToString
import kotlin.collections.map
import kotlin.collections.sortedBy
import kotlin.collections.sortedByDescending
import kotlin.collections.sortedWith
import kotlin.collections.sumOf
import kotlin.collections.take
import kotlin.collections.toList
import kotlin.io.extension
import kotlin.io.nameWithoutExtension
import kotlin.io.resolve
import kotlin.io.use
import kotlin.isInitialized
import kotlin.jvm.java
import kotlin.jvm.javaClass
import kotlin.let
import kotlin.math.log10
import kotlin.math.pow
import kotlin.ranges.until
import kotlin.text.any
import kotlin.text.contains
import kotlin.text.format
import kotlin.text.isBlank
import kotlin.text.isNotEmpty
import kotlin.text.toFloatOrNull
import kotlin.text.toIntOrNull
import kotlin.text.toLongOrNull
import kotlin.text.toLowerCase
import kotlin.text.trim

class TpVideoBrowse : AppCompatActivity() , TpCopyDirDialogFragment.OnMoveCompleteListener{
    private val TAG = "TpVideoBrowse"
    private lateinit var mainbinding: BrowseLayoutBinding
    private lateinit var rightPanelBinding: RightPanelLayoutBinding
    private lateinit var imageFiles: List<File>
    private lateinit var imageLabels: List<String>

    // 图片对比功能
    private lateinit var menuCompareLayout: LinearLayout
    private lateinit var adapter: TpThumbGridAdapter
    private var currentPage = 1
    private val pageSize = 12
    private lateinit var allImageFiles: List<File>
    private lateinit var folder: File
    private var totalPages = 1
    private var currentFolder: File? = null
    private lateinit var initialRootFolder: File
    private lateinit var folderAdapter: FolderAdapter

    // 弹出菜单相关变量
    private var isMenuShowing = false
    private lateinit var menuPopupWindow: PopupWindow
    private var isOperationActive = false
    private lateinit var menuCopyLayout: View
    private lateinit var menuCutLayout: View
    private lateinit var menuDetailsLayout: View
    private var areActionsDisabled = false
    private var DirPages = 0

    private val toolIcons = intArrayOf(
        R.drawable.ic_picture,
        R.drawable.ic_video,
        R.drawable.ic_settings,
        R.drawable.ic_folder,
        R.drawable.ic_return
    )

    override fun onMoveComplete(success: Boolean) {
        // 无论移动成功与否，都清除选中项
        if (::adapter.isInitialized) {
            runOnUiThread {
                adapter.deleteSelectedItems()
            }
        }

        // 显示移动结果
        Toast.makeText(this,
            if (success) "移动成功" else "移动操作完成",
            Toast.LENGTH_SHORT
        ).show()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mainbinding = BrowseLayoutBinding.inflate(layoutInflater)
        // 初始化右侧面板
        rightPanelBinding = RightPanelLayoutBinding.inflate(layoutInflater)
        mainbinding.rightPanel.addView(rightPanelBinding.root)

        setContentView(mainbinding.root)

        initToolbar()

        // 初始化文件夹视图
        if (checkStoragePermission()) {
            loadImageData()
            initViews()
        } else {
            requestStoragePermission()
        }

        initFolderView()
        showFolders()

        // 设置右侧面板中的返回按钮点击事件
        setupBackButton()
        //初始化弹出窗口
        initPopupMenu()
    }

    private fun setupBackButton() {
        // 使用 View Binding 直接访问按钮
        rightPanelBinding.btnBack.setOnClickListener {
            // 处理返回逻辑
            btnReturnClicked()
        }
    }

    private fun initFolderView() {
        val usbRoot = getUsbRootDirectory() ?: return
        val dcimPath = File(usbRoot, getStorageDCIMPath())
        currentFolder = dcimPath
        initialRootFolder = dcimPath

        rightPanelBinding.rvFolderList.layoutManager = LinearLayoutManager(this)

        folderAdapter = FolderAdapter(emptyList()) { folder ->
            if (folder == null) {
                goToParentFolder()
                showFolders()
                DirPages = 0
            } else {
                // 进入子文件夹
                loadFolderContents(folder)
            }
        }

        loadFolderContents(dcimPath)

        rightPanelBinding.rvFolderList.adapter = folderAdapter
        updateFolderTitle(dcimPath)

        rightPanelBinding.btnCreateFolder.setOnClickListener {
            startCreateFolder()
        }
    }
    private fun initToolbar() {
        val toolClick = View.OnClickListener { view ->
            when(view.id) {
                R.id.browse_config -> TopBtnClick(6)
            }
        }
        mainbinding.browseConfig.setOnClickListener(toolClick)

        // 初始化底部操作按钮栏
        mainbinding.btnContainer1.setOnClickListener {
            if (::adapter.isInitialized && adapter.isSelectionMode) {
                adapter.exitSelectionMode()
                updateActionsState()
                updateSelectAllButtonText()
                Toast.makeText(this, "已取消选择模式", Toast.LENGTH_SHORT).show()
            }
        }
        mainbinding.btnContainer2.setOnClickListener {
            Toast.makeText(this, "全选点击", Toast.LENGTH_SHORT).show()

            if (::adapter.isInitialized) {
                // 判断当前是否已全选
                val isAllSelected = adapter.getSelectedCount() == adapter.itemCount
                // 切换全选状态（如果已全选则取消，否则全选）
                adapter.toggleSelectAll(!isAllSelected)

                // 更新按钮文本（可选）
                updateSelectAllButtonText()
            }
        }
        mainbinding.btnContainer3.setOnClickListener {
            Toast.makeText(this, "操作3点击", Toast.LENGTH_SHORT).show()
        }
        mainbinding.btnContainer4.setOnClickListener {
            Toast.makeText(this, "删除点击", Toast.LENGTH_SHORT).show()

            if (::adapter.isInitialized) {
                val selectedFiles = adapter.getSelectedFiles()
                if (selectedFiles.isEmpty()) {
                    Toast.makeText(this, "请先选择要删除的文件", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                // 显示确认对话框
                AlertDialog.Builder(this)
                    .setTitle("确认删除")
                    .setMessage("确定要删除选中的${selectedFiles.size}个文件吗？")
                    .setPositiveButton("删除") { _, _ ->
                        performDeleteOperation(selectedFiles)
                    }
                    .setNegativeButton("取消", null)
                    .show()
            }
        }
        mainbinding.btnContainer5.setOnClickListener {
            Toast.makeText(this, "更多点击", Toast.LENGTH_SHORT).show()
            if (!isMenuShowing) {
                // 显示菜单
                menuPopupWindow.showAsDropDown(mainbinding.browseConfig, 900, 800)
                isMenuShowing = true
            }
        }

        mainbinding.browseSelectAll.visibility = View.GONE
        mainbinding.browseDelete.visibility = View.GONE
        mainbinding.browseConfig.visibility = View.GONE
    }


    private fun initPopupMenu() {
        val inflater = LayoutInflater.from(this)
        val menuView = inflater.inflate(R.layout.popup_config_menu, null)

        // 设置菜单项的点击事件
        menuView.findViewById<View>(R.id.menu_copy).setOnClickListener { menuItemClicked("复制") }
        menuView.findViewById<View>(R.id.menu_cut).setOnClickListener { menuItemClicked("剪切") }
        menuView.findViewById<View>(R.id.menu_paste).setOnClickListener { menuItemClicked("粘贴") }
        menuView.findViewById<View>(R.id.menu_compare).setOnClickListener { menuItemClicked("图片对比") }
        menuView.findViewById<View>(R.id.menu_details).setOnClickListener { menuItemClicked("详细信息") }

        // 创建PopupWindow
        menuPopupWindow = PopupWindow(
            menuView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ).apply {
            isOutsideTouchable = true
            isFocusable = true
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

            // 点击外部关闭菜单
            setOnDismissListener { isMenuShowing = false }
        }

        //
        menuCopyLayout = menuView.findViewById(R.id.menu_copy)
        menuCutLayout = menuView.findViewById(R.id.menu_cut)
        menuCompareLayout = menuView.findViewById(R.id.menu_compare)
        menuDetailsLayout = menuView.findViewById(R.id.menu_details)


        menuCopyLayout.setOnClickListener {
            if (!areActionsDisabled) {
                menuPopupWindow.dismiss()
                performCopy()
            }
        }

        menuCutLayout.setOnClickListener {
            if (!areActionsDisabled) {
                menuPopupWindow.dismiss()
                performCut()
            }
        }

        menuCompareLayout.setOnClickListener {
            if (!areActionsDisabled) {
                menuPopupWindow.dismiss()
                performImageCompare()
            }
        }

        menuDetailsLayout.setOnClickListener {
            if (!areActionsDisabled) {
                menuPopupWindow.dismiss()
                showFileDetails()
            }
        }

//        menuView.findViewById<View>(R.id.menu_paste).setOnClickListener {
//            menuPopupWindow.dismiss()
//            performPaste()
//        }

        // 获取详细信息菜单项

        // 初始化时更新状态
        updateActionsState()

        menuView.findViewById<View>(R.id.menu_paste).visibility = View.GONE
    }

    private fun updateActionsState() {
        // 在这里添加你的业务逻辑来判断是否需要禁用操作
        // 示例：当没有选择文件时禁用操作

        val shouldDisableActions = !adapter.isSelectionMode
        // 根据状态执行操作
        areActionsDisabled = shouldDisableActions

        if (::menuCopyLayout.isInitialized) {
            updateCopyButtonState(menuCopyLayout, shouldDisableActions)
        }
        if (::menuCutLayout.isInitialized) {
            updateCutButtonState(menuCutLayout, shouldDisableActions)
        }
        if (::menuPopupWindow.isInitialized) {
            updateDetailsButtonState(menuDetailsLayout, shouldDisableActions)
        }

        updateLabelState(shouldDisableActions)
    }

    private fun updateCopyButtonState(menuItem: View, disabled: Boolean) {
        // 找到文本视图 (TextView)
        val textView = menuItem.findViewWithTag<TextView>("menu_text")
            ?: menuItem.findViewById<TextView>(R.id.menu_copy)
        // 设置文本颜色
        textView?.setTextColor(
            ContextCompat.getColor(this,
                if (disabled) R.color.gray_text_disabled else R.color.popup_text_color
            )
        )
        menuItem.isEnabled = !disabled
    }


    private fun updateCutButtonState(menuItem: View, disabled: Boolean) {
        // 找到文本视图 (TextView)
        val textView = menuItem.findViewWithTag<TextView>("menu_text")
            ?: menuItem.findViewById<TextView>(R.id.menu_cut)
        // 设置文本颜色
        textView?.setTextColor(
            ContextCompat.getColor(this,
                if (disabled) R.color.gray_text_disabled else R.color.popup_text_color
            )
        )
        menuItem.isEnabled = !disabled
    }

    private fun updateDetailsButtonState(menuItem: View, disabled: Boolean) {
        // 找到文本视图 (TextView)
        val textView = (menuItem as? ViewGroup)?.let {
            for (i in 0 until it.childCount) {
                val child = it.getChildAt(i)
                if (child is TextView && child.text == getString(R.string.menu_details)) {
                    return@let child
                }
            }
            null
        }

        // 设置文本颜色
        textView?.setTextColor(
            ContextCompat.getColor(this,
                if (disabled) R.color.gray_text_disabled else R.color.popup_text_color
            )
        )

        menuItem.isEnabled = !disabled
    }

    private fun DisplayExpandFunctions() {
        if (isMenuShowing) {
            menuPopupWindow.dismiss()
        } else {
            // 计算菜单位置（在config按钮下方）
            val configButton = mainbinding.browseConfig

            // 获取按钮的位置
            val location = IntArray(2)
            configButton.getLocationOnScreen(location)

            // 显示菜单
            menuPopupWindow.showAtLocation(
                configButton,
                Gravity.NO_GRAVITY,
                location[0] - configButton.width,  // 向左偏移使其在按钮下方居中
                location[1] + configButton.height
            )
        }
        isMenuShowing = !isMenuShowing
    }

    private fun menuItemClicked(action: String) {
        menuPopupWindow.dismiss()
        isMenuShowing = false

        when (action) {
            "复制" -> performCopy()
            "剪切" -> performCut()
            "粘贴" -> performPaste()
            "图片对比" -> performImageCompare()
            "详细信息" -> showFileDetails()
        }
    }

    private fun performCopy() {
        if (!::adapter.isInitialized || !adapter.isSelectionMode) {
            Toast.makeText(this, "请先选择要复制的文件", Toast.LENGTH_SHORT).show()
            return
        }

        val selectedFiles = adapter.getSelectedFiles()
        if (selectedFiles.isEmpty()) {
            Toast.makeText(this, "没有选中文件", Toast.LENGTH_SHORT).show()
            return
        }

        try {
            val storagePath = TpFileManager.getExternalStoragePath(this)
            val rootPath = File(storagePath, getStorageDCIMPath()).path
            val rootDir = File(rootPath)
            val paths = selectedFiles.map { it.absolutePath }

            runOnUiThread {
                try {
                    if (!isFinishing && !isDestroyed) {
                        val dialog = TpCopyDirDialogFragment.newInstance(
                            rootDir,
                            rootDir,
                            paths,
                            TpCopyDirDialogFragment.OPERATION_COPY
                        )

                        if (!supportFragmentManager.isDestroyed) {
                            dialog.show(supportFragmentManager, "folder_dialog")
                        } else {
                            Toast.makeText(this, "无法显示对话框，请稍后再试", Toast.LENGTH_SHORT).show()
                        }
                    }
                } catch (e: IllegalStateException) {
                    e.printStackTrace()
                    Toast.makeText(this, "对话框显示失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "复制操作失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun performCut() {
        // 实现剪切功能
        Toast.makeText(this, "剪切功能已执行", Toast.LENGTH_SHORT).show()
        val storagePath = TpFileManager.getExternalStoragePath(this)
        val rootPath = File(storagePath, getStorageDCIMPath()).path
        val rootDir = File(rootPath)

        val selectedFiles = adapter.getSelectedFiles()
        val paths = selectedFiles.map { it.absolutePath }

        val dialog = TpCopyDirDialogFragment.newInstance(
            rootDir,
            rootDir,
            paths,
            TpCopyDirDialogFragment.OPERATION_CUT // 指定剪切操作
        )

        dialog.setOnMoveCompleteListener(this)
        dialog.show(supportFragmentManager, "folder_dialog")
    }

    private fun performPaste() {
        // 实现粘贴功能
        Toast.makeText(this, "粘贴功能已执行", Toast.LENGTH_SHORT).show()
    }

    private fun showFileDetails() {
        // 获取选中的文件
        val selectedFiles = adapter.getSelectedFiles()
        if (selectedFiles.isEmpty()) {
            Toast.makeText(this, "请选择文件查看详情", Toast.LENGTH_SHORT).show()
            return
        }

        // 创建弹窗视图
        val inflater = LayoutInflater.from(this)
        val dialogView = inflater.inflate(R.layout.dialog_file_details, null)

        // 初始化视图组件
        val fileName = dialogView.findViewById<TextView>(R.id.tv_file_name)
        val fileSize = dialogView.findViewById<TextView>(R.id.tv_file_size)
        val modifiedDate = dialogView.findViewById<TextView>(R.id.tv_modified_date)
        val videoDuration = dialogView.findViewById<TextView>(R.id.tv_video_duration)
        val videoBitrate = dialogView.findViewById<TextView>(R.id.tv_video_bitrate)
        val videoResolution = dialogView.findViewById<TextView>(R.id.tv_video_resolution)
        val videoFramerate = dialogView.findViewById<TextView>(R.id.tv_video_framerate)
        val videoInfoGroup = dialogView.findViewById<ViewGroup>(R.id.video_info_group)

        if (selectedFiles.size == 1) {
            // 单个文件显示详细信息
            val file = selectedFiles[0]
            val fileType = getFileType(file)

            // 设置通用文件信息
            fileName.text = "文件名: ${file.name}"
            fileSize.text = "大小: ${formatFileSize(file.length())}"
            modifiedDate.text = "修改时间: ${
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(
                    Date(
                        file.lastModified()
                    )
                )}"

            // 根据文件类型显示/隐藏视频信息
            if (fileType == "MP4") {
                videoInfoGroup.visibility = View.VISIBLE
                extractVideoMetadata(file) { metadata ->
                    runOnUiThread {
                        videoDuration.text = "时长: ${metadata.duration}"
                        videoBitrate.text = "比特率: ${metadata.bitrate}"
                        videoResolution.text = "分辨率: ${metadata.resolution}"
                        videoFramerate.text = "帧率: ${metadata.frameRate}"
                    }
                }
            } else {
                videoInfoGroup.visibility = View.GONE
            }
        } else {
            // 多个文件显示数量和总大小
            val totalSize = selectedFiles.sumOf { it.length() }
            fileName.text = "已选择 ${selectedFiles.size} 个文件"
            fileSize.text = "总大小: ${formatFileSize(totalSize)}"
            modifiedDate.text = ""
            videoInfoGroup.visibility = View.GONE
        }

        // 创建并显示对话框
        AlertDialog.Builder(this)
            .setTitle(if (selectedFiles.size == 1) "文件详细信息" else "文件概览")
            .setView(dialogView)
            .setPositiveButton("确定", null)
            .create()
            .apply {
                window?.let {
                    it.setBackgroundDrawableResource(R.drawable.dialog_background)
                    // 设置固定宽高
                    val metrics = resources.displayMetrics
                    it.setLayout((90 * metrics.density).toInt(), (160 * metrics.density).toInt())
                }
                show()
            }

    }

    private fun TopBtnClick(position: Int) {
        when(position) {
            1 -> showPreview()
            5 -> showFolders()
            6 -> DisplayExpandFunctions()
        }
    }

    private fun loadImageData() {
        val storageManager = getSystemService(STORAGE_SERVICE) as StorageManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            storageManager.storageVolumes.forEach { volume ->
                // 判断是否为 USB 设备
                if (volume.isRemovable) {
                    try {
                        val getPathMethod = volume.javaClass.getMethod("getPath")
                        val path = getPathMethod.invoke(volume) as? String
                        path?.let {
                            Log.d("USB_PATH", "检测到 USB 挂载路径: $it")
                            if (File(it).canRead()) {
                                // 执行文件操作...
                                folder = File(it, getStoragePicturePath())
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }

        // 加载所有文件
        allImageFiles = folder.listFiles()
            ?.filter { it.isFile }
            ?.sortedBy { it.name }
            ?: emptyList()

        // 计算总页数
        totalPages = if (allImageFiles.isEmpty()) 1 else (allImageFiles.size - 1) / pageSize + 1

        // 初始化第一页数据
        updatePageData()
    }

    private fun updatePageData() {
        imageFiles = allImageFiles
        imageLabels = imageFiles.map { it.nameWithoutExtension }
    }

    private fun initViews() {
        mainbinding.recyclerContainer.setOnClickListener {
            // 新增处理选择模式
            if (adapter.isSelectionMode) {
                adapter.exitSelectionMode()
            } else {
                adapter.clearSelections()
            }
        }

        adapter = TpThumbGridAdapter(
            emptyList(), // 初始为空
            emptyList(),
            { position -> // 单击
                val selectedItem = adapter.getFileAt(position)
                if (selectedItem.isDirectory) {
                    // 进入文件夹
                    loadFolderContents(selectedItem)
                } else {
                    // 处理文件
                    if (getFileType(selectedItem) == "MP4") {
                        StartVideoDecode(selectedItem)
                    } else {
                        // 获取当前文件夹下所有图片文件
                        val imageFiles = currentFolder?.listFiles()
                            ?.filter {
                                it.isFile &&
                                        (getFileType(it) == "JPEG image" ||
                                                getFileType(it) == "PNG image" ||
                                                getFileType(it) == "BMP image" ||
                                                getFileType(it) == "TIFF image")
                            } // ✅ 添加TIFF支持
                            ?.sortedByDescending { it.name } ?: emptyList()

                        val currentPosition = imageFiles.indexOf(selectedItem)
                        if (currentPosition != -1) {
                            StartImageDecode(imageFiles, currentPosition)
                        }
                    }
                }
            },
            { position -> // 双击
                val selectedItem = adapter.getFileAt(position)
                if (selectedItem.isDirectory) {
                    loadFolderContents(selectedItem)
                } else {
                    if (getFileType(selectedItem) == "MP4") {
                        StartVideoDecode(selectedItem)
                    }
                }
            },
            onUpdate = { this.onUpdate() }
        )

        mainbinding.recyclerView.apply {
            layoutManager = GridLayoutManager(this@TpVideoBrowse, 4)
            adapter = <EMAIL>
            addItemDecoration(TpThumbSpacingDecoration(4, 16.dpToPx(), true))
            setHasFixedSize(true)
            itemAnimator = null
        }
    }

    private fun checkStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.READ_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestStoragePermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
            STORAGE_PERMISSION_CODE
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == STORAGE_PERMISSION_CODE && grantResults.isNotEmpty() &&
            grantResults[0] == PackageManager.PERMISSION_GRANTED
        ) {
            loadImageData()
            mainbinding.recyclerView.adapter?.notifyDataSetChanged()
        } else {
            Toast.makeText(this, "需要存储权限才能显示图片", Toast.LENGTH_SHORT).show()
        }
    }

    companion object {
        private const val STORAGE_PERMISSION_CODE = 1001
    }

    private fun getFileType(file: File): String {
        // 方法1：通过扩展名快速判断
        val extension = file.extension.toLowerCase(Locale.US)
        return when (extension) {
            "png" -> "PNG image"
            "jpg", "jpeg" -> "JPEG image"
            "gif" -> "GIF image"
            "bmp" -> "BMP image"
            "tiff", "tif" -> "TIFF image"  // ✅ 添加TIFF支持
            "mp4", "mov", "avi", "mkv" -> "MP4"
            else -> {
                // 方法2：通过文件头魔数精准判断（示例仅实现PNG检测）
                if (isRealPng(file)) "PNG image"
                else "File"
            }
        }
    }

    // 检测是否为真实PNG文件
    private fun isRealPng(file: File): Boolean {
        return try {
            FileInputStream(file).use { stream ->
                val header = ByteArray(8)
                stream.read(header)
                header.contentEquals(byteArrayOf(0x89.toByte(), 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A))
            }
        } catch (e: Exception) {
            false
        }
    }

    private fun StartVideoDecode(file: File) {
        val fragment = TpVideoDecoderDialogFragment.newInstance(file.absolutePath)
        fragment.show(supportFragmentManager, "video_dialog")
    }

    private fun StartImageDecode(imageFiles: List<File>, currentPosition: Int) {
        val dialogFragment = TpImageDecodeDialogFragment.newInstance(
            imageFiles.map { it.absolutePath },
            currentPosition
        )
        dialogFragment.show(supportFragmentManager, "image_dialog")
    }

    private fun showPreview() {
        val root = getUsbRootDirectory() ?: File(TpFileManager.getExternalStoragePath(this))
        val dcimPath = root.resolve(getStorageDCIMPath())

        allImageFiles = dcimPath.listFiles()
            ?.filter { it.isDirectory }
            ?.sortedBy { it.name }
            ?: emptyList()

        updatePageData()

        adapter.updateData(imageFiles, imageLabels)
    }


    private fun showPictures() {
        folder = File(TpFileManager.getExternalStoragePath(this), getStoragePicturePath())
        allImageFiles = folder.listFiles()
            ?.filter { it.isFile }
            ?.sortedBy { it.name }
            ?: emptyList()

        updatePageData()

        adapter.updateData(imageFiles, imageLabels)
    }

    private fun showVideos() {
        folder = File(TpFileManager.getExternalStoragePath(this), getStorageVideoPath())
        allImageFiles = folder.listFiles()
            ?.filter { it.isFile }
            ?.sortedBy { it.name }
            ?: emptyList()

        updatePageData()
        adapter.updateData(imageFiles, imageLabels)
    }

    private fun showFolders() {
        val root = getUsbRootDirectory() ?: File(TpFileManager.getExternalStoragePath(this))
        val dcimPath = root.resolve(getStorageDCIMPath())
        loadFolderContents(dcimPath)


    }
    private fun startCreateFolder() {

        val usbpath = File(TpFileManager.getExternalStoragePath(this))
        val targetFolder = usbpath.resolve(getStorageDCIMPath())

        if (!checkStoragePermission()) {
            requestStoragePermission()
            return
        }
        if (!targetFolder.canWrite()) {
            Toast.makeText(this, "该位置不可写入", Toast.LENGTH_SHORT).show()
            return
        }

        // 1. 创建并保留 EditText 引用
        val input = EditText(this).apply {
            hint = "文件夹名称"
            filters = arrayOf(InputFilter { source, start, end, dest, dstart, dend ->
                if (source.any { it in "\\/:*?\"<>|" }) "" else null
            })
        }

        val inputDialog = AlertDialog.Builder(this).apply {
            setTitle("新建文件夹")
            setMessage("请输入文件夹名称")
            setView(input)  // 添加 EditText
            setPositiveButton("创建") { dialog, which ->
                val folderName = input.text.toString().trim()
//                val folderName = "ABC"
                if (folderName.isNotEmpty()) {

                    currentFolder?.let { createNewFolder(it, folderName) }
                } else {
                    Toast.makeText(this@TpVideoBrowse, "文件夹名称不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            setNegativeButton("取消", null)
        }.create()

        // 2. 使用保存的引用访问 EditText
        inputDialog.setOnShowListener {
            input.postDelayed({
                input.requestFocus()
                showSoftInput(input)
            }, 200)
        }

        inputDialog.show()

        // 3. RK3588 特殊处理：确保键盘不会遮挡视图
        if (Build.MODEL.contains("RK3588")) {
            inputDialog.window?.let { window ->
                window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            }
        }

    }

    private fun createNewFolder(parent: File, folderName: String) {
        // 检查名称是否合法
        if (folderName.isBlank()) {
            Toast.makeText(this, "文件夹名称不能为空", Toast.LENGTH_SHORT).show()
            return
        }

        // 检查名称是否包含非法字符
        if (folderName.any { it in "\\/:*?\"<>|" }) {
            Toast.makeText(this, "名称包含非法字符: \\/:*?\"<>|", Toast.LENGTH_SHORT).show()
            return
        }

        // 检查文件夹是否已存在
        val newFolder = File(parent, folderName)
        if (newFolder.exists()) {
            Toast.makeText(this, "文件夹已存在", Toast.LENGTH_SHORT).show()
            return
        }

        // 尝试创建文件夹
        if (newFolder.mkdirs()) {
            Toast.makeText(this, "文件夹创建成功", Toast.LENGTH_SHORT).show()

            // 刷新当前视图
            when {
//                mainbinding.browsePicture.visibility == View.VISIBLE -> showPictures()
//                mainbinding.browseFolder.visibility == View.VISIBLE -> showFolders()
                else -> showFolders()
            }

            //刷新文件夹列表
            refreshFolderList(parent)

        } else {
            Toast.makeText(this, "文件夹创建失败", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showSoftInput(editText: EditText) {
        val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT)
    }

    private fun btnReturnClicked() {
        if (adapter.isSelectionMode) {
            adapter.exitSelectionMode()
        }

        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }

    private fun getUsbRootDirectory(): File? {
        val storageManager = getSystemService(STORAGE_SERVICE) as StorageManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            storageManager.storageVolumes.forEach { volume ->
                if (volume.isRemovable) {
                    try {
                        val getPathMethod = volume.javaClass.getMethod("getPath")
                        val path = getPathMethod.invoke(volume) as? String
                        path?.let {
                            if (File(it).canRead()) {
                                return File(it)
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
        return null
    }

    fun File.getCreationTime(): Long {
        return try {
            // 使用 NIO 获取文件属性
            val path = Paths.get(absolutePath)
            val attr = Files.readAttributes(path, BasicFileAttributes::class.java, LinkOption.NOFOLLOW_LINKS)
            attr.creationTime().toMillis()
        } catch (e: Exception) {
            // 失败时回退到最后修改时间
            lastModified()
        }
    }

    //文件夹加载
    private fun getExternalStoragePath(): String {
        return TpFileManager.getExternalStoragePath(this) ?: ""
    }

    private fun getRootFolders(): List<File> {

        val storagePath = TpFileManager.getExternalStoragePath(this)
        val rootPath = File(storagePath, getStorageDCIMPath()).path
        val rootDir = File(rootPath)
        val folderList = rootDir.listFiles { file -> file.isDirectory }?.toList() ?: emptyList()
        return rootDir.listFiles { file -> file.isDirectory }?.toList() ?: emptyList()
    }

    private fun getSubFolders(parent: File): List<File> {
        return parent.listFiles { file ->
            file.isDirectory && !file.isHidden
        }?.sortedByDescending { it.lastModified() } ?: emptyList()
    }
    private fun loadFolderContents(folder: File) {
        // 获取当前文件夹的父文件夹（用于返回上一级）
        val parentFolder = if (folder != initialRootFolder) folder.parentFile else null

        // 加载该文件夹下的内容（文件夹+文件）
        val items = folder.listFiles()
            ?.filter { !it.isHidden }
            ?.sortedWith(compareBy(
                // 文件夹在前
                { if (it.isDirectory) 0 else 1 },
                // 按修改时间排序
                { -it.lastModified() }
            )) ?: emptyList()

        // 更新当前文件夹引用
        currentFolder = folder

        // 更新主列表适配器数据
        adapter.updateData(items.toList(), items.map { it.name })

        // 退出选择模式（如果处于选择状态）
        if (::adapter.isInitialized && adapter.isSelectionMode) {
            adapter.exitSelectionMode()
        }

        // 更新文件夹列表（左侧）
        val subFolders = items.filter { it.isDirectory }
        folderAdapter.updateFolders(subFolders, parentFolder)

        // 更新标题
        updateFolderTitle(folder)
    }

    private fun goToParentFolder() {
        currentFolder?.parentFile?.let { parent ->
            if (parent.exists() && parent.canRead()) {
                loadFolderContents(parent)
            }
        }
    }

    private fun updateFolderTitle(folder: File) {
        // 判断当前目录是否为根目录
        val isRoot = folder == initialRootFolder

        val titlePrefix = if (isRoot) "U盘文件夹" else folder.name
        val count = folder.listFiles()?.size ?: 0
        rightPanelBinding.tvFolderTitle.text = "$titlePrefix ($count)"
    }

    private fun refreshFolderList(parent: File) {
        currentFolder?.let {
            val subFolders = it.listFiles()
                ?.filter { file -> file.isDirectory && !file.isHidden }
                ?.sortedByDescending { file -> file.lastModified() }
                ?: emptyList()

            val parentFolder = if (it != initialRootFolder) it.parentFile else null
            folderAdapter.updateFolders(subFolders, parentFolder)
        }
    }

    fun onUpdate(){
        updateActionsState()
        updateSelectAllButtonText()
    }

    private fun openCopyDir() {
        val storagePath = TpFileManager.getExternalStoragePath(this)
        val rootPath = File(storagePath, getStorageDCIMPath()).path
        val rootDir = File(rootPath)

        val selectedFiles = adapter.getSelectedFiles()
        val paths = selectedFiles.map { it.absolutePath }
        try {
            val dialog = TpCopyDirDialogFragment.newInstance(
                rootDir,
                rootDir,
                paths,
                TpCopyDirDialogFragment.OPERATION_COPY
            )

            if (supportFragmentManager.isDestroyed) {
                Toast.makeText(this, "无法显示对话框，请稍后再试", Toast.LENGTH_SHORT).show()
                return
            }

            dialog.show(supportFragmentManager, "folder_dialog")
        } catch (e: IllegalStateException) {
            e.printStackTrace()
            Toast.makeText(this, "对话框显示失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun getSelectedNames() {
        if (::adapter.isInitialized && adapter.isSelectionMode) {
            val selectedNames = adapter.getSelectedFileNames()
            // 处理选中文件名称列表，例如显示在Toast或打印到日志
            Toast.makeText(this, "选中文件: ${selectedNames.joinToString(", ")}", Toast.LENGTH_SHORT).show()
            Log.d("SelectedFiles", "选中文件: $selectedNames")
        }
    }



    private fun updateLabelState(hidden: Boolean) {
        val bottomActionBar = mainbinding.root.findViewById<LinearLayout>(R.id.bottom_action_bar)
        if(!hidden){
            bottomActionBar.visibility = View.VISIBLE
        }else{
            bottomActionBar.visibility = View.GONE
        }
    }


    private fun performDeleteOperation(selectedFiles: List<File>) {
        // 创建并显示进度对话框
        val progressDialog = ProgressDialog(this).apply {
            setTitle("正在删除")
            setMessage("准备删除...")
            setProgressStyle(ProgressDialog.STYLE_HORIZONTAL)
            setCancelable(false)
            max = 100
            setButton(DialogInterface.BUTTON_NEGATIVE, "取消") { dialog, _ ->
                dialog.dismiss()
            }
            show()
        }

        var successCount = 0
        var failedCount = 0
        val errors = mutableListOf<String>()

        // 在工作线程执行删除操作
        Thread {
            val totalItems = selectedFiles.size
            var processedItems = 0

            for (file in selectedFiles) {
                // 更新当前文件名显示
                runOnUiThread {
                    progressDialog.setMessage("正在删除: ${file.name}")
                    progressDialog.progress = (processedItems * 100) / totalItems
                }

                try {
                    if (file.isDirectory) {
                        // 递归删除文件夹及其内容
                        if (deleteDirectory(file)) {
                            successCount++
                        } else {
                            throw IOException("删除文件夹失败")
                        }
                    } else {
                        // 删除文件
                        if (file.delete()) {
                            successCount++
                        } else {
                            throw IOException("删除文件失败")
                        }
                    }
                } catch (e: Exception) {
                    errors.add("${if (file.isDirectory) "文件夹" else "文件"}删除失败: ${file.name} (${e.message})")
                    failedCount++
                }

                processedItems++
                // 更新进度
                runOnUiThread {
                    progressDialog.progress = (processedItems * 100) / totalItems
                }
            }

            // 在主线程更新UI
            runOnUiThread {
                // 关闭进度对话框
                progressDialog.dismiss()

                // 刷新文件列表
                currentFolder?.let { loadFolderContents(it) }

                // 清除选中项
                adapter.deleteSelectedItems()

                // 显示结果
                val message = buildString {
                    append("删除完成\n")
                    append("成功: $successCount, 失败: $failedCount")
                    if (errors.isNotEmpty()) {
                        append("\n\n错误详情:")
                        errors.take(5).forEach { append("\n- $it") }
                        if (errors.size > 5) append("\n...还有${errors.size - 5}个错误未显示")
                    }
                }

                Toast.makeText(this, message, Toast.LENGTH_LONG).show()
            }
        }.start()
    }

    /**
     * 递归删除文件夹及其内容
     * @param directory 要删除的文件夹
     * @return 是否删除成功
     */
    private fun deleteDirectory(directory: File): Boolean {
        if (directory.isDirectory) {
            val children = directory.listFiles()
            children?.forEach { child ->
                if (child.isDirectory) {
                    deleteDirectory(child)
                } else {
                    if (!child.delete()) {
                        return false
                    }
                }
            }
        }
        return directory.delete()
    }

    //Details
    // 视频元数据类
    private data class VideoMetadata(
        var duration: String = "N/A",
        var bitrate: String = "N/A",
        var resolution: String = "N/A",
        var frameRate: String = "N/A"
    )

    // 提取视频元数据
    private fun extractVideoMetadata(file: File, callback: (VideoMetadata) -> Unit) {
        val metadata = VideoMetadata()
        val retriever = MediaMetadataRetriever()

        try {
            retriever.setDataSource(file.absolutePath)

            // 获取视频时长
            val durationMs = retriever.extractMetadata(METADATA_KEY_DURATION)?.toLongOrNull() ?: 0
            metadata.duration = formatDuration(durationMs)

            // 获取比特率
            val bitrate = retriever.extractMetadata(METADATA_KEY_BITRATE)?.toIntOrNull() ?: 0
            metadata.bitrate = if (bitrate > 0) "${bitrate / 1000} kbps" else "N/A"

            // 获取分辨率
            val width = retriever.extractMetadata(METADATA_KEY_VIDEO_WIDTH) ?: "N/A"
            val height = retriever.extractMetadata(METADATA_KEY_VIDEO_HEIGHT) ?: "N/A"
            metadata.resolution = "${width}x${height}"

            // 获取帧率
            val frameRate = retriever.extractMetadata(METADATA_KEY_VIDEO_FRAME_COUNT)?.toFloatOrNull()
            metadata.frameRate = if (frameRate != null && frameRate > 0)
                "${"%.2f".format(frameRate)} fps" else "N/A"

        } catch (e: Exception) {
            Log.e(TAG, "Error extracting video metadata", e)
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing MediaMetadataRetriever", e)
            }
        }

        callback(metadata)
    }

    // 格式化文件大小
    private fun formatFileSize(size: Long): String {
        if (size <= 0) return "0 B"
        val units = arrayOf("B", "KB", "MB", "GB")
        val digitGroups = (log10(size.toDouble()) / log10(1024.0)).toInt()
        return DecimalFormat("#,##0.#").format(size / 1024.0.pow(digitGroups.toDouble())) + " " + units[digitGroups]
    }

    // 格式化视频时长
    private fun formatDuration(millis: Long): String {
        val seconds = millis / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        return when {
            hours > 0 -> String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60)
            minutes > 0 -> String.format("%d:%02d", minutes, seconds % 60)
            else -> String.format("0:%02d", seconds)
        }
    }

    private fun updateSelectAllButtonText() {
        if (::adapter.isInitialized) {
            val isAllSelected = adapter.getSelectedCount() == adapter.itemCount
            mainbinding.browseSelectAll.text = if (isAllSelected) "取消全选" else "全选"
        }
    }

    // ===== 图片对比功能相关方法 =====

    /**
     * 执行图片对比 - 智能判断启动双图或多图对比
     */
    private fun performImageCompare() {
        if (!::adapter.isInitialized || !adapter.isSelectionMode) {
            Toast.makeText(this, "请先选择要对比的图片", Toast.LENGTH_SHORT).show()
            return
        }

        val selectedFiles = adapter.getSelectedFiles()
        when {
            selectedFiles.size < 2 -> {
                Toast.makeText(this, "请至少选择2张图片进行对比", Toast.LENGTH_SHORT).show()
            }
            selectedFiles.size > 4 -> {
                Toast.makeText(this, "最多只能选择4张图片进行对比", Toast.LENGTH_SHORT).show()
            }
            else -> {
                // 检查是否都是图片文件
                val imageFiles = selectedFiles.filter { isImageFile(it) }
                when {
                    imageFiles.size < 2 -> {
                        Toast.makeText(this, "请选择至少2张图片文件进行对比", Toast.LENGTH_SHORT).show()
                    }
                    imageFiles.size == 2 -> {
                        // 启动双图对比
                        TpImageCompareActivity.start(this, imageFiles[0].absolutePath, imageFiles[1].absolutePath)
                    }
                    imageFiles.size in 3..4 -> {
                        // 启动多图对比
                        val imagePaths = imageFiles.map { it.absolutePath }
                        TpImageCompareMultiActivity.start(this, imagePaths)
                    }
                }
            }
        }
    }



    /**
     * 执行多图对比
     */
    private fun performMultiImageCompare() {
        if (!::adapter.isInitialized || !adapter.isSelectionMode) {
            Toast.makeText(this, "请先选择要对比的图片", Toast.LENGTH_SHORT).show()
            return
        }

        val selectedFiles = adapter.getSelectedFiles()
        when {
            selectedFiles.size < 2 -> {
                Toast.makeText(this, "请至少选择2张图片进行对比", Toast.LENGTH_SHORT).show()
            }
            selectedFiles.size > 4 -> {
                Toast.makeText(this, "最多只能选择4张图片进行对比", Toast.LENGTH_SHORT).show()
            }
            else -> {
                // 检查是否都是图片文件
                val imageFiles = selectedFiles.filter { isImageFile(it) }
                if (imageFiles.size >= 2) {
                    val imagePaths = imageFiles.map { it.absolutePath }
                    TpImageCompareMultiActivity.start(this, imagePaths)
                } else {
                    Toast.makeText(this, "请选择至少2张图片文件进行对比", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    /**
     * 检查文件是否为图片格式
     */
    private fun isImageFile(file: File): Boolean {
        val imageExtensions = listOf("jpg", "jpeg", "png", "bmp", "gif", "webp", "tif", "tiff")
        return imageExtensions.contains(file.extension.lowercase())
    }
}
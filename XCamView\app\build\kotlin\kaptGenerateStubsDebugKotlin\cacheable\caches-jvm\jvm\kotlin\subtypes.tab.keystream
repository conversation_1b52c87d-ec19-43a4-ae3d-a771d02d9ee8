&com.touptek.xcamview.util.BaseFragment1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder6com.touptek.xcamview.view.MeasurementOverlayView.Shape(androidx.appcompat.app.AppCompatActivity$androidx.fragment.app.DialogFragment,com.touptek.xcamview.util.BaseDialogFragmentandroid.view.Viewkotlin.Enumandroidx.fragment.app.FragmentRcom.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListener&com.touptek.xcamview.util.BaseActivity-android.view.View.OnAttachStateChangeListenerDcom.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener8androidx.recyclerview.widget.RecyclerView.ItemDecorationScom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListener,androidx.appcompat.widget.AppCompatImageView androidx.viewbinding.ViewBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
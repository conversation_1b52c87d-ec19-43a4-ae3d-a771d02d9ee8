package com.touptek.xcamview.activity;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\t\u001a\u00020\nJ\u0018\u0010\u000b\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u0006R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/touptek/xcamview/activity/StatusBanner;", "", "view", "Landroid/widget/TextView;", "(Landroid/widget/TextView;)V", "fadeDuration", "", "hideRunnable", "Ljava/lang/Runnable;", "hide", "", "show", "text", "", "duration", "app_debug"})
public final class StatusBanner {
    private final android.widget.TextView view = null;
    private java.lang.Runnable hideRunnable;
    private final long fadeDuration = 300L;
    
    public StatusBanner(@org.jetbrains.annotations.NotNull
    android.widget.TextView view) {
        super();
    }
    
    public final void show(@org.jetbrains.annotations.NotNull
    java.lang.String text, long duration) {
    }
    
    public final void hide() {
    }
}
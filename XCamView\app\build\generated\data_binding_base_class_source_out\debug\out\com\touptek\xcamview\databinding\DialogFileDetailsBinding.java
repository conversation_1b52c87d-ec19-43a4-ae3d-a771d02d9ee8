// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogFileDetailsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView tvFileName;

  @NonNull
  public final TextView tvFileSize;

  @NonNull
  public final TextView tvModifiedDate;

  @NonNull
  public final TextView tvVideoBitrate;

  @NonNull
  public final TextView tvVideoDuration;

  @NonNull
  public final TextView tvVideoFramerate;

  @NonNull
  public final TextView tvVideoResolution;

  @NonNull
  public final LinearLayout videoInfoGroup;

  private DialogFileDetailsBinding(@NonNull LinearLayout rootView, @NonNull TextView tvFileName,
      @NonNull TextView tvFileSize, @NonNull TextView tvModifiedDate,
      @NonNull TextView tvVideoBitrate, @NonNull TextView tvVideoDuration,
      @NonNull TextView tvVideoFramerate, @NonNull TextView tvVideoResolution,
      @NonNull LinearLayout videoInfoGroup) {
    this.rootView = rootView;
    this.tvFileName = tvFileName;
    this.tvFileSize = tvFileSize;
    this.tvModifiedDate = tvModifiedDate;
    this.tvVideoBitrate = tvVideoBitrate;
    this.tvVideoDuration = tvVideoDuration;
    this.tvVideoFramerate = tvVideoFramerate;
    this.tvVideoResolution = tvVideoResolution;
    this.videoInfoGroup = videoInfoGroup;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogFileDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogFileDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_file_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogFileDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_file_name;
      TextView tvFileName = ViewBindings.findChildViewById(rootView, id);
      if (tvFileName == null) {
        break missingId;
      }

      id = R.id.tv_file_size;
      TextView tvFileSize = ViewBindings.findChildViewById(rootView, id);
      if (tvFileSize == null) {
        break missingId;
      }

      id = R.id.tv_modified_date;
      TextView tvModifiedDate = ViewBindings.findChildViewById(rootView, id);
      if (tvModifiedDate == null) {
        break missingId;
      }

      id = R.id.tv_video_bitrate;
      TextView tvVideoBitrate = ViewBindings.findChildViewById(rootView, id);
      if (tvVideoBitrate == null) {
        break missingId;
      }

      id = R.id.tv_video_duration;
      TextView tvVideoDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvVideoDuration == null) {
        break missingId;
      }

      id = R.id.tv_video_framerate;
      TextView tvVideoFramerate = ViewBindings.findChildViewById(rootView, id);
      if (tvVideoFramerate == null) {
        break missingId;
      }

      id = R.id.tv_video_resolution;
      TextView tvVideoResolution = ViewBindings.findChildViewById(rootView, id);
      if (tvVideoResolution == null) {
        break missingId;
      }

      id = R.id.video_info_group;
      LinearLayout videoInfoGroup = ViewBindings.findChildViewById(rootView, id);
      if (videoInfoGroup == null) {
        break missingId;
      }

      return new DialogFileDetailsBinding((LinearLayout) rootView, tvFileName, tvFileSize,
          tvModifiedDate, tvVideoBitrate, tvVideoDuration, tvVideoFramerate, tvVideoResolution,
          videoInfoGroup);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

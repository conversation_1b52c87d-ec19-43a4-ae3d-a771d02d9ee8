package com.touptek.video.internal;

import android.content.Context;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CaptureRequest;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.view.Surface;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * TpCameraManager 类用于管理摄像头的打开、预览和资源释放。
 * <p>
 * 此类提供了摄像头的初始化、配置输出、启动预览以及释放资源的功能。
 * 它支持后台线程操作以避免阻塞主线程。
 * </p>
 */
public class TpCameraManager
{
    private static final String TAG = "TpCameraManager";

    /* 摄像头设备实例 */
    private CameraDevice cameraDevice;

    /* 摄像头捕获会话实例 */
    private CameraCaptureSession captureSession;

    /* 摄像头管理器，用于管理摄像头设备 */
    private final android.hardware.camera2.CameraManager cameraManager;

    /* ImageReader，用于接收摄像头输出的图像数据 */
    private ImageReader imageReader;

    /* 后台线程的 Handler，用于处理摄像头操作 */
    private Handler backgroundHandler;

    /* 后台线程，用于避免阻塞主线程 */
    private HandlerThread backgroundThread;

    // 回调处理器
    private Consumer<CameraDevice> onCameraOpenedHandler;
    private Consumer<CameraDevice> onCameraDisconnectedHandler;
    private BiConsumer<CameraDevice, Integer> onCameraErrorHandler;

    private TpCameraManager(Builder builder) {
        this.cameraManager = (android.hardware.camera2.CameraManager) builder.context.getSystemService(Context.CAMERA_SERVICE);
        this.onCameraOpenedHandler = builder.onCameraOpenedHandler;
        this.onCameraDisconnectedHandler = builder.onCameraDisconnectedHandler;
        this.onCameraErrorHandler = builder.onCameraErrorHandler;
    }

    /**
     * 创建一个CameraManagerHelper构建器
     * <p>
     * 使用构建器模式创建CameraManagerHelper实例，允许链式配置各种回调。
     * </p>
     * 
     * @param context 应用上下文
     * @return 新的Builder实例，用于配置CameraManagerHelper
     */
    public static Builder builder(Context context) {
        return new Builder(context);
    }

    /**
     * CameraManagerHelper的构建器类
     * <p>
     * 使用构建器模式配置CameraManagerHelper的各种回调处理器。
     * 支持链式调用方法来设置相机打开、断开连接和错误处理的回调。
     * </p>
     */
    public static class Builder {
        private final Context context;
        private Consumer<CameraDevice> onCameraOpenedHandler;
        private Consumer<CameraDevice> onCameraDisconnectedHandler;
        private BiConsumer<CameraDevice, Integer> onCameraErrorHandler;
        private Consumer<CameraCaptureSession> onCameraConfigFailedHandler;

        private Builder(Context context) {
            this.context = context;
        }

        /**
         * 设置相机打开回调
         * 
         * @param handler 相机打开时的回调处理器
         * @return 当前Builder实例，用于链式调用
         */
        public Builder onCameraOpened(Consumer<CameraDevice> handler) {
            this.onCameraOpenedHandler = handler;
            return this;
        }

        /**
         * 设置相机断开连接回调
         * 
         * @param handler 相机断开连接时的回调处理器
         * @return 当前Builder实例，用于链式调用
         */
        public Builder onCameraDisconnected(Consumer<CameraDevice> handler) {
            this.onCameraDisconnectedHandler = handler;
            return this;
        }

        /**
         * 设置相机错误回调
         * 
         * @param handler 相机出错时的回调处理器
         * @return 当前Builder实例，用于链式调用
         */
        public Builder onCameraError(BiConsumer<CameraDevice, Integer> handler) {
            this.onCameraErrorHandler = handler;
            return this;
        }

        /**
         * 构建CameraManagerHelper实例
         * <p>
         * 使用当前配置的回调处理器创建一个新的CameraManagerHelper实例。
         * </p>
         * 
         * @return 配置好的CameraManagerHelper实例
         */
        public TpCameraManager build() {
            return new TpCameraManager(this);
        }
    }

    /**
     * 打开摄像头。
     * <p>
     * 此方法会打开指定的摄像头，并通过回调接口处理摄像头的打开、断开和错误事件。
     * 默认情况下会打开第一个摄像头。
     * </p>
     */
    public void openCamera() {
        try {
            /* 获取摄像头 ID 列表，并打开第一个摄像头 */
            String[] cameraIds = cameraManager.getCameraIdList();
            if (cameraIds.length == 0) {
                Log.e(TAG, "没有找到可用的相机设备");
                return; // 或者提供一个合适的错误处理
            }
            String cameraId = cameraIds[0];
            cameraManager.openCamera(cameraId, new CameraDevice.StateCallback() {
                @Override
                public void onOpened(CameraDevice camera) {
                    if (onCameraOpenedHandler != null) {
                        onCameraOpenedHandler.accept(camera);
                    }
                }

                @Override
                public void onDisconnected(CameraDevice camera) {
                    if (onCameraDisconnectedHandler != null) {
                        onCameraDisconnectedHandler.accept(camera);
                    }
                    camera.close();
                }

                @Override
                public void onError(CameraDevice camera, int error) {
                    if (onCameraErrorHandler != null) {
                        onCameraErrorHandler.accept(camera, error);
                    }
                    camera.close();
                }
            }, null);
        }
        catch (CameraAccessException e) {
            Log.e(TAG, "Error opening camera", e);
        }
    }

    /**
     * 配置摄像头输出的 Surface。
     * <p>
     * 此方法会将摄像头的输出配置到指定的编码 Surface 和 ImageReader 的 Surface 上。
     * 它还会启动后台线程以处理摄像头操作。
     * </p>
     *
     * @param cameraDevice
     *        摄像头设备实例。
     * @param encoderSurface
     *        用于编码的 Surface。
     * @param imageReaderSurface
     *        用于接收图像数据的 ImageReader 的 Surface。
     */
    public void configCameraOutputs(CameraDevice cameraDevice, Surface encoderSurface, Surface imageReaderSurface)
    {
        try
        {
            Log.d(TAG, "Starting camera preview");
            this.cameraDevice = cameraDevice;

            /* 启动后台线程 */
            startBackgroundThread();

            /* 创建捕获请求 */
            CaptureRequest.Builder builder = cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            builder.addTarget(encoderSurface); // 添加编码 Surface
            builder.addTarget(imageReaderSurface); // 添加 ImageReader 的 Surface

            /* 创建捕获会话 */
            cameraDevice.createCaptureSession(
                List.of(encoderSurface, imageReaderSurface),
                new CameraCaptureSession.StateCallback()
                {
                    @Override
                    public void onConfigured(CameraCaptureSession session)
                    {
                        captureSession = session;
                        try
                        {
                            /* 设置重复请求以持续预览 */
                            captureSession.setRepeatingRequest(builder.build(), null, backgroundHandler);
                            Log.d(TAG, "Camera preview started successfully");
                        }
                        catch (CameraAccessException e)
                        {
                            Log.e(TAG, "Error starting camera preview", e);
                        }
                    }

                    @Override
                    public void onConfigureFailed(CameraCaptureSession session)
                    {
                        Log.e(TAG, "Camera configuration failed");
                        resetHdmiRxViaScript();
                    }
                },
                backgroundHandler
            );
        }
        catch (CameraAccessException e)
        {
            Log.e(TAG, "Error starting camera preview", e);
        }
    }

    /**
     * 通过执行外部脚本重置HDMI RX
     *
     * @return 脚本是否成功执行
     */
    public boolean resetHdmiRxViaScript() {
        try {
            Log.d(TAG, "尝试执行HDMI重置脚本...");

            // 执行脚本
            Process process = Runtime.getRuntime().exec("/data/touptek/resetHdmiRx.sh");

            // 获取执行结果
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                Log.d(TAG, "HDMI重置脚本执行成功");
                return true;
            } else {
                Log.e(TAG, "HDMI重置脚本执行失败，退出码: " + exitCode);
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "执行HDMI重置脚本时出错", e);
            return false;
        }
    }

    /**
     * 启动后台线程。
     * <p>
     * 此方法会启动一个后台线程，并创建一个 Handler，用于处理摄像头的异步操作。
     * </p>
     */
    private void startBackgroundThread()
    {
        backgroundThread = new HandlerThread("CameraBackground");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
    }

    /**
     * 停止后台线程。
     * <p>
     * 此方法会安全地停止后台线程，并释放相关资源。
     * </p>
     */
    private void stopBackgroundThread()
    {
        if (backgroundThread != null)
        {
            backgroundThread.quitSafely();
            try
            {
                backgroundThread.join();
                backgroundThread = null;
                backgroundHandler = null;
            }
            catch (InterruptedException e)
            {
                Log.e(TAG, "Error stopping background thread", e);
            }
        }
    }

    /**
     * 释放摄像头资源。
     * <p>
     * 此方法会关闭摄像头捕获会话、摄像头设备以及 ImageReader，并停止后台线程。
     * </p>
     */
    public void releaseCamera()
    {
        /* 关闭捕获会话 */
        if (captureSession != null)
        {
            captureSession.close();
            captureSession = null;
        }

        /* 关闭摄像头设备 */
        if (cameraDevice != null)
        {
            cameraDevice.close();
            cameraDevice = null;
        }

        /* 关闭 ImageReader */
        if (imageReader != null)
        {
            imageReader.close();
            imageReader = null;
        }

        /* 停止后台线程 */
        stopBackgroundThread();
    }
}

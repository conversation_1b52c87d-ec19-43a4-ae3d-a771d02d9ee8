<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Tue Jul 29 13:56:06 CST 2025 -->
<title>TpFileManager</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-29">
<meta name="description" content="declaration: package: com.touptek.utils, class: TpFileManager">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.touptek.utils</a></div>
<h1 title="类 TpFileManager" class="title">类 TpFileManager</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.touptek.utils.TpFileManager</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TpFileManager</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">TpFileManager 类提供文件存储相关的工具方法。
 <p>
 此类提供了文件存储操作的各种实用方法，包括USB设备监控、媒体文件路径创建、
 存储空间检查和文件系统类型识别等功能。
 </p>
 
 <p><b>主要功能：</b></p>
 <ul>
   <li>监控USB驱动器的插入和移除</li>
   <li>为视频和图片创建合适的存储路径</li>
   <li>获取存储设备的可用空间</li>
   <li>检测文件系统类型</li>
   <li>获取外部和内部存储路径</li>
 </ul>
 
 <p><b>使用示例：</b></p>
 <pre><code>
 // 监听USB设备插拔
 TpFileManager.startUsbDriveMonitor(context, new TpFileManager.StorageListener() {
     @Override
     public void onUsbDriveConnected(String rootPath) {
         Log.d(TAG, "USB驱动器已连接: " + rootPath);
         // 处理USB连接事件
     }
     
     @Override
     public void onUsbDriveDisconnected(String rootPath) {
         Log.d(TAG, "USB驱动器已断开: " + rootPath);
         // 处理USB断开事件
     }
 });
 
 // 创建视频和图片文件路径
 String videoPath = TpFileManager.createVideoPath(context);
 String imagePath = TpFileManager.createImagePath(context);
 
 // 获取存储空间信息
 long availableSpace = TpFileManager.getAvailableStorageSpace(videoPath);
 String fsType = TpFileManager.getFileSystemType(videoPath);
 </code></pre></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TpFileManager.StorageListener.html" class="type-name-link" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a></code></div>
<div class="col-last even-row-color">
<div class="block">存储设备变化监听接口。</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TpFileManager</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createImagePath(android.content.Context)" class="member-name-link">createImagePath</a><wbr>(android.content.Context&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">创建带日期后缀的图像文件路径。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createImagePath(android.content.Context,java.lang.String,boolean,java.lang.String)" class="member-name-link">createImagePath</a><wbr>(android.content.Context&nbsp;context,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;prefix,
 boolean&nbsp;includeDateTime,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;extension)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">创建自定义图像文件路径。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createVideoPath(android.content.Context)" class="member-name-link">createVideoPath</a><wbr>(android.content.Context&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">创建带日期后缀的视频文件路径。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#generateUniqueFileName(java.lang.String)" class="member-name-link">generateUniqueFileName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;originalPath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">生成唯一的文件名，避免文件覆盖。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getAvailableStorageSpace(java.lang.String)" class="member-name-link">getAvailableStorageSpace</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取指定路径的可用存储空间。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getExternalStoragePath(android.content.Context)" class="member-name-link">getExternalStoragePath</a><wbr>(android.content.Context&nbsp;context)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取外部存储设备根目录。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getFileSystemType(java.lang.String)" class="member-name-link">getFileSystemType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">判断文件系统的格式。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getInternalStoragePath(android.content.Context)" class="member-name-link">getInternalStoragePath</a><wbr>(android.content.Context&nbsp;context)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取内部存储设备根目录。</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#startUsbDriveMonitor(android.content.Context,com.touptek.utils.TpFileManager.StorageListener)" class="member-name-link">startUsbDriveMonitor</a><wbr>(android.content.Context&nbsp;context,
 <a href="TpFileManager.StorageListener.html" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">开始监听U盘插拔事件。</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stopUsbDriveMonitor(android.content.Context)" class="member-name-link">stopUsbDriveMonitor</a><wbr>(android.content.Context&nbsp;context)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">停止监听U盘插拔事件。</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TpFileManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TpFileManager</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="startUsbDriveMonitor(android.content.Context,com.touptek.utils.TpFileManager.StorageListener)">
<h3>startUsbDriveMonitor</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startUsbDriveMonitor</span><wbr><span class="parameters">(android.content.Context&nbsp;context,
 <a href="TpFileManager.StorageListener.html" title="com.touptek.utils中的接口">TpFileManager.StorageListener</a>&nbsp;listener)</span></div>
<div class="block">开始监听U盘插拔事件。
 <p>
 此方法会注册一个BroadcastReceiver来监听存储设备的变化。
 当检测到U盘插入或拔出时，会通过监听器回调相应方法。
 如果已有监听器在运行，会先停止之前的监听器，确保只有一个活动的监听实例。
 方法会立即检查当前状态，如果已有U盘插入则立即触发回调。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文，用于注册广播接收器</dd>
<dd><code>listener</code> - 存储设备变化监听器，用于接收U盘插拔事件回调</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stopUsbDriveMonitor(android.content.Context)">
<h3>stopUsbDriveMonitor</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stopUsbDriveMonitor</span><wbr><span class="parameters">(android.content.Context&nbsp;context)</span></div>
<div class="block">停止监听U盘插拔事件。
 <p>
 此方法会注销之前注册的BroadcastReceiver。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createVideoPath(android.content.Context)">
<h3>createVideoPath</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">createVideoPath</span><wbr><span class="parameters">(android.content.Context&nbsp;context)</span></div>
<div class="block">创建带日期后缀的视频文件路径。
 <p>
 此方法会根据当前日期和时间生成唯一的文件名，格式为"video_yyyyMMdd_HHmmss.mp4"。
 优先将文件存储在可拆卸的外部存储设备（如U盘）的DCIM/Videos目录中，
 如果未检测到U盘，则会使用设备内部存储的应用私有目录作为后备存储位置。
 此方法会自动创建必要的目录结构。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文，用于访问外部存储目录</dd>
<dt>返回:</dt>
<dd>生成的视频文件的绝对路径，可直接用于MediaRecorder或FileOutputStream</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createImagePath(android.content.Context)">
<h3>createImagePath</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">createImagePath</span><wbr><span class="parameters">(android.content.Context&nbsp;context)</span></div>
<div class="block">创建带日期后缀的图像文件路径。
 <p>
 此方法会根据当前日期和时间生成唯一的文件名，格式为"image_yyyyMMdd_HHmmss.jpg"。
 优先将文件存储在可拆卸的外部存储设备（如U盘）的DCIM/Images目录中，
 如果没有检测到外部存储设备，则使用默认主外部存储中的Pictures目录。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文</dd>
<dt>返回:</dt>
<dd>返回完整的图像文件路径</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createImagePath(android.content.Context,java.lang.String,boolean,java.lang.String)">
<h3>createImagePath</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">createImagePath</span><wbr><span class="parameters">(android.content.Context&nbsp;context,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;prefix,
 boolean&nbsp;includeDateTime,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;extension)</span></div>
<div class="block">创建自定义图像文件路径。
 <p>
 此方法允许指定文件名前缀、是否包含日期时间以及文件格式。
 优先将文件存储在可拆卸的外部存储设备（如U盘）的DCIM/Images目录中，
 如果没有检测到外部存储设备，则使用默认主外部存储中的Pictures目录。
 内置智能文件名冲突检测，自动避免文件覆盖。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文</dd>
<dd><code>prefix</code> - 文件名前缀，如"image"</dd>
<dd><code>includeDateTime</code> - 是否在文件名中包含日期时间</dd>
<dd><code>extension</code> - 文件扩展名，不包含点，如"jpg"、"png"或"bmp"</dd>
<dt>返回:</dt>
<dd>返回唯一的图像文件路径，保证不会覆盖现有文件</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAvailableStorageSpace(java.lang.String)">
<h3>getAvailableStorageSpace</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getAvailableStorageSpace</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">获取指定路径的可用存储空间。
 <p>
 此方法会通过 StatFs 类获取指定路径的剩余存储空间大小。
 如果路径无效或发生异常，则返回 -1。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>path</code> - 文件路径，用于检查其可用存储空间。</dd>
<dt>返回:</dt>
<dd>返回可用存储空间的大小（以字节为单位）。如果发生异常，则返回 -1。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileSystemType(java.lang.String)">
<h3>getFileSystemType</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getFileSystemType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">判断文件系统的格式。
 <p>
 此方法通过执行系统命令 "mount" 来解析指定路径的文件系统类型。
 如果路径无效或发生异常，则返回 "Unknown"。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>path</code> - 文件路径，用于检查其文件系统类型。</dd>
<dt>返回:</dt>
<dd>返回文件系统格式（如 FAT32（vfat）、exFAT、NTFS 等）。如果发生异常，则返回 "Unknown"。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExternalStoragePath(android.content.Context)">
<h3>getExternalStoragePath</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getExternalStoragePath</span><wbr><span class="parameters">(android.content.Context&nbsp;context)</span></div>
<div class="block">获取外部存储设备根目录。
 <p>
 此方法会尝试获取可拆卸的外部存储设备（如 U 盘）的路径。
 如果未检测到外部存储设备，则返回 null。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文，用于访问外部存储目录。</dd>
<dt>返回:</dt>
<dd>返回外部存储设备的根路径。如果未检测到外部存储设备，则返回 null。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInternalStoragePath(android.content.Context)">
<h3>getInternalStoragePath</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getInternalStoragePath</span><wbr><span class="parameters">(android.content.Context&nbsp;context)</span></div>
<div class="block">获取内部存储设备根目录。
 <p>
 此方法会返回设备的内部存储路径根目录。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>context</code> - 应用上下文，用于访问内部存储目录。</dd>
<dt>返回:</dt>
<dd>返回内部存储设备的根路径。如果无法获取，则返回 null。</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateUniqueFileName(java.lang.String)">
<h3>generateUniqueFileName</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">generateUniqueFileName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;originalPath)</span></div>
<div class="block">生成唯一的文件名，避免文件覆盖。
 <p>
 如果目标文件已存在，会自动添加递增后缀（如：image_1.jpg, image_2.jpg）。
 如果尝试100次仍有冲突，则使用时间戳确保唯一性。
 此方法是线程安全的，适用于并发场景。
 </p></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>originalPath</code> - 原始文件路径</dd>
<dt>返回:</dt>
<dd>唯一的文件路径，保证不会覆盖现有文件</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>

package com.android.rockchip.camera2.separated;

import android.graphics.Matrix;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.appcompat.app.AppCompatActivity;

import com.touptek.video.TpImageLoader;
import com.android.rockchip.mediacodecnew.R;
import com.touptek.ui.internal.TpViewTransform;

/**
 * ImageViewerActivity 类用于显示选定的图片，并支持缩放和平移。
 */
public class ImageViewerActivity extends AppCompatActivity {

    private ImageView imageView;
    private ScaleGestureDetector scaleGestureDetector;
    private Matrix matrix = new Matrix();
    private float lastX = 0f;
    private float lastY = 0f;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        /* 设置全屏显示 */
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);

        setContentView(R.layout.image_viewer);

        /* 获取 ImageView */
        imageView = findViewById(R.id.image_view);

        /* 设置FIT_XY模式，确保图片撑满全屏，消除白边 */
        imageView.setScaleType(ImageView.ScaleType.MATRIX);

        /* 获取图片路径 */
        String imagePath = getIntent().getStringExtra("imagePath");

        /* 使用 GlideImageLoader 加载高质量图片 */
//        ImageDecoder.loadImage(imagePath, imageView);
        TpImageLoader.loadFullImage(imagePath,imageView);

        /* 初始化缩放手势检测器 */
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                TpViewTransform.applyZoom(imageView, matrix, detector.getScaleFactor(), detector.getFocusX(), detector.getFocusY());
                return true;
            }
        });

        /* 设置触摸监听器以支持平移 */
        imageView.setOnTouchListener((v, event) -> {
            switch (event.getActionMasked()) {
                case MotionEvent.ACTION_DOWN:
                    lastX = event.getX();
                    lastY = event.getY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    float deltaX = event.getX() - lastX;
                    float deltaY = event.getY() - lastY;
                    TpViewTransform.applyPan(imageView, matrix, deltaX, deltaY);
                    lastX = event.getX();
                    lastY = event.getY();
                    break;
            }
            scaleGestureDetector.onTouchEvent(event);
            return true;
        });
    }
}

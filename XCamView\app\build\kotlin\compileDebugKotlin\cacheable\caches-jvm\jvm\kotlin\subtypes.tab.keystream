1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder,com.touptek.xcamview.util.BaseDialogFragment&com.touptek.xcamview.util.BaseFragment(androidx.appcompat.app.AppCompatActivity$androidx.fragment.app.DialogFragmentandroid.view.Viewkotlin.Enum6com.touptek.xcamview.view.MeasurementOverlayView.Shape androidx.viewbinding.ViewBinding,androidx.appcompat.widget.AppCompatImageView&com.touptek.xcamview.util.BaseActivity-android.view.View.OnAttachStateChangeListenerDcom.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener8androidx.recyclerview.widget.RecyclerView.ItemDecorationScom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListenerRcom.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListenerandroidx.fragment.app.Fragment                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
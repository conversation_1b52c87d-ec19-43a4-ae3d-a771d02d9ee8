<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 未选中状态 -->
    <item android:state_checked="false">
        <shape android:shape="oval">
            <stroke
                android:width="2dp"
                android:color="#333333"/> <!-- 边框白色 -->
            <size
                android:width="24dp"
                android:height="24dp"/>
        </shape>
    </item>

    <!-- 选中状态 -->
    <item android:state_checked="true">
        <layer-list>
            <item>
                <shape android:shape="oval">
                    <solid android:color="#2196F3"/> <!-- 填充蓝色 -->
                    <size
                        android:width="24dp"
                        android:height="24dp"/>
                </shape>
            </item>
            <item
                android:width="8dp"
                android:height="8dp"
                android:gravity="center">
                <shape android:shape="oval">
                    <solid android:color="#FFFFFF"/> <!-- 中心白点 -->
                </shape>
            </item>
        </layer-list>
    </item>
</selector>
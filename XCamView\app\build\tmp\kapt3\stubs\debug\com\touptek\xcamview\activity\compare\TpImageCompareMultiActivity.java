package com.touptek.xcamview.activity.compare;

import java.lang.System;

/**
 * 多图对比Activity - 支持2-4张图片对比
 * 支持全局同步、分组同步、独立操作三种模式
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u0000 22\u00020\u0001:\u000223B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0018\u001a\u00020\u0019H\u0002J\u0010\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\b\u0010\u001d\u001a\u00020\u0019H\u0002J\u0010\u0010\u001e\u001a\u00020\r2\u0006\u0010\u001f\u001a\u00020 H\u0002J\b\u0010!\u001a\u00020\u0019H\u0002J\b\u0010\"\u001a\u00020\u0019H\u0002J\u0012\u0010#\u001a\u00020\u00192\b\u0010$\u001a\u0004\u0018\u00010%H\u0014J\b\u0010&\u001a\u00020\u0019H\u0002J\b\u0010\'\u001a\u00020\u0019H\u0002J\b\u0010(\u001a\u00020\u0019H\u0002J\u0010\u0010)\u001a\u00020\u00192\u0006\u0010*\u001a\u00020 H\u0002J\u0018\u0010+\u001a\u00020\u00192\u0006\u0010,\u001a\u00020 2\u0006\u0010-\u001a\u00020.H\u0002J\u0018\u0010/\u001a\u00020\u00192\u0006\u0010*\u001a\u00020 2\u0006\u0010-\u001a\u00020.H\u0002J\b\u00100\u001a\u00020\u0019H\u0002J\b\u00101\u001a\u00020\u0019H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00064"}, d2 = {"Lcom/touptek/xcamview/activity/compare/TpImageCompareMultiActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "btnBack", "Landroid/widget/ImageButton;", "btnReset", "btnSyncMode", "currentSyncMode", "Lcom/touptek/xcamview/activity/compare/TpImageCompareMultiActivity$SyncMode;", "imagePaths", "", "", "imageView1", "Lcom/touptek/measurerealize/TpImageView;", "imageView2", "imageView3", "imageView4", "isUpdating", "", "tvInfo1", "Landroid/widget/TextView;", "tvInfo2", "tvInfo3", "tvInfo4", "cycleSyncMode", "", "formatFileSize", "bytes", "", "getImagePaths", "getImageViewByIndex", "index", "", "initViews", "loadImages", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "resetImages", "setupClickListeners", "setupMatrixSync", "syncMatrix", "sourceIndex", "syncToAllExcept", "excludeIndex", "matrix", "Landroid/graphics/Matrix;", "syncToGroup", "updateImageInfo", "updateSyncModeButton", "Companion", "SyncMode", "app_debug"})
public final class TpImageCompareMultiActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.compare.TpImageCompareMultiActivity.Companion Companion = null;
    private static final java.lang.String TAG = "TpImageCompareMulti";
    private static final java.lang.String EXTRA_IMAGE_PATHS = "extra_image_paths";
    private android.widget.ImageButton btnBack;
    private android.widget.ImageButton btnSyncMode;
    private android.widget.ImageButton btnReset;
    private com.touptek.measurerealize.TpImageView imageView1;
    private com.touptek.measurerealize.TpImageView imageView2;
    private com.touptek.measurerealize.TpImageView imageView3;
    private com.touptek.measurerealize.TpImageView imageView4;
    private android.widget.TextView tvInfo1;
    private android.widget.TextView tvInfo2;
    private android.widget.TextView tvInfo3;
    private android.widget.TextView tvInfo4;
    private java.util.List<java.lang.String> imagePaths;
    private com.touptek.xcamview.activity.compare.TpImageCompareMultiActivity.SyncMode currentSyncMode = com.touptek.xcamview.activity.compare.TpImageCompareMultiActivity.SyncMode.GLOBAL;
    private boolean isUpdating = false;
    
    public TpImageCompareMultiActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews() {
    }
    
    private final void getImagePaths() {
    }
    
    private final void loadImages() {
    }
    
    private final void updateImageInfo() {
    }
    
    private final java.lang.String formatFileSize(long bytes) {
        return null;
    }
    
    private final void setupClickListeners() {
    }
    
    private final void cycleSyncMode() {
    }
    
    private final void updateSyncModeButton() {
    }
    
    private final void resetImages() {
    }
    
    private final void setupMatrixSync() {
    }
    
    private final void syncMatrix(int sourceIndex) {
    }
    
    private final void syncToAllExcept(int excludeIndex, android.graphics.Matrix matrix) {
    }
    
    private final void syncToGroup(int sourceIndex, android.graphics.Matrix matrix) {
    }
    
    private final com.touptek.measurerealize.TpImageView getImageViewByIndex(int index) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/touptek/xcamview/activity/compare/TpImageCompareMultiActivity$SyncMode;", "", "(Ljava/lang/String;I)V", "GLOBAL", "GROUP", "INDEPENDENT", "app_debug"})
    public static enum SyncMode {
        /*public static final*/ GLOBAL /* = new GLOBAL() */,
        /*public static final*/ GROUP /* = new GROUP() */,
        /*public static final*/ INDEPENDENT /* = new INDEPENDENT() */;
        
        SyncMode() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00040\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/touptek/xcamview/activity/compare/TpImageCompareMultiActivity$Companion;", "", "()V", "EXTRA_IMAGE_PATHS", "", "TAG", "start", "", "context", "Landroid/content/Context;", "imagePaths", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void start(@org.jetbrains.annotations.NotNull
        android.content.Context context, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> imagePaths) {
        }
    }
}
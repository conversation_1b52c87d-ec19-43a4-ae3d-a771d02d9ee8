package com.touptek.xcamview.activity.browse;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u009a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0015\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0015\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0011\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\b\u0011\u0018\u0000 }2\u00020\u00012\u00020\u0002:\u0002}~B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010)\u001a\u00020*H\u0002J\u001e\u0010+\u001a\u00020*2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010,\u001a\u00020\u0005H\u0002J\u0010\u0010-\u001a\u00020*2\u0006\u0010.\u001a\u00020\fH\u0002J\u0010\u0010/\u001a\u00020*2\u0006\u00100\u001a\u00020\u0005H\u0002J\b\u00101\u001a\u00020*H\u0002J\b\u00102\u001a\u00020\u000eH\u0002J\u0018\u00103\u001a\u00020*2\u0006\u00104\u001a\u00020\f2\u0006\u00105\u001a\u00020\u0007H\u0002J\u0010\u00106\u001a\u00020\u000e2\u0006\u00107\u001a\u00020\fH\u0002J$\u00108\u001a\u00020*2\u0006\u0010.\u001a\u00020\f2\u0012\u00109\u001a\u000e\u0012\u0004\u0012\u00020;\u0012\u0004\u0012\u00020*0:H\u0002J\u0010\u0010<\u001a\u00020\u00072\u0006\u0010=\u001a\u00020>H\u0002J\u0010\u0010?\u001a\u00020\u00072\u0006\u0010@\u001a\u00020>H\u0002J\b\u0010A\u001a\u00020\u0007H\u0002J\u0010\u0010B\u001a\u00020\u00072\u0006\u0010.\u001a\u00020\fH\u0002J\u000e\u0010C\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u0002J\b\u0010D\u001a\u00020*H\u0002J\u0016\u0010E\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u00104\u001a\u00020\fH\u0002J\n\u0010F\u001a\u0004\u0018\u00010\fH\u0002J\b\u0010G\u001a\u00020*H\u0002J\b\u0010H\u001a\u00020*H\u0002J\b\u0010I\u001a\u00020*H\u0002J\b\u0010J\u001a\u00020*H\u0002J\b\u0010K\u001a\u00020*H\u0002J\u0010\u0010L\u001a\u00020\u000e2\u0006\u0010.\u001a\u00020\fH\u0002J\u0010\u0010M\u001a\u00020\u000e2\u0006\u0010.\u001a\u00020\fH\u0002J\u0010\u0010N\u001a\u00020*2\u0006\u0010\u0011\u001a\u00020\fH\u0002J\b\u0010O\u001a\u00020*H\u0002J\u0010\u0010P\u001a\u00020*2\u0006\u0010Q\u001a\u00020\u0007H\u0002J\u0012\u0010R\u001a\u00020*2\b\u0010S\u001a\u0004\u0018\u00010TH\u0014J\u0010\u0010U\u001a\u00020*2\u0006\u0010V\u001a\u00020\u000eH\u0016J-\u0010W\u001a\u00020*2\u0006\u0010X\u001a\u00020\u00052\u000e\u0010Y\u001a\n\u0012\u0006\b\u0001\u0012\u00020\u00070Z2\u0006\u0010[\u001a\u00020\'H\u0016\u00a2\u0006\u0002\u0010\\J\u0006\u0010]\u001a\u00020*J\b\u0010^\u001a\u00020*H\u0002J\b\u0010_\u001a\u00020*H\u0002J\b\u0010`\u001a\u00020*H\u0002J\u0016\u0010a\u001a\u00020*2\f\u0010b\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u0002J\b\u0010c\u001a\u00020*H\u0002J\b\u0010d\u001a\u00020*H\u0002J\u0010\u0010e\u001a\u00020*2\u0006\u00104\u001a\u00020\fH\u0002J\b\u0010f\u001a\u00020*H\u0002J\b\u0010g\u001a\u00020*H\u0002J\b\u0010h\u001a\u00020*H\u0002J\b\u0010i\u001a\u00020*H\u0002J\b\u0010j\u001a\u00020*H\u0002J\b\u0010k\u001a\u00020*H\u0002J\u0010\u0010l\u001a\u00020*2\u0006\u0010m\u001a\u00020nH\u0002J\b\u0010o\u001a\u00020*H\u0002J\b\u0010p\u001a\u00020*H\u0002J\b\u0010q\u001a\u00020*H\u0002J\u0018\u0010r\u001a\u00020*2\u0006\u0010s\u001a\u00020\u001e2\u0006\u0010t\u001a\u00020\u000eH\u0002J\u0018\u0010u\u001a\u00020*2\u0006\u0010s\u001a\u00020\u001e2\u0006\u0010t\u001a\u00020\u000eH\u0002J\u0018\u0010v\u001a\u00020*2\u0006\u0010s\u001a\u00020\u001e2\u0006\u0010t\u001a\u00020\u000eH\u0002J\u0010\u0010w\u001a\u00020*2\u0006\u0010\u0011\u001a\u00020\fH\u0002J\u0010\u0010x\u001a\u00020*2\u0006\u0010y\u001a\u00020\u000eH\u0002J\b\u0010z\u001a\u00020*H\u0002J\b\u0010{\u001a\u00020*H\u0002J\n\u0010|\u001a\u00020>*\u00020\fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\f0\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u007f"}, d2 = {"Lcom/touptek/xcamview/activity/browse/TpVideoBrowse;", "Landroidx/appcompat/app/AppCompatActivity;", "Lcom/touptek/xcamview/activity/browse/TpCopyDirDialogFragment$OnMoveCompleteListener;", "()V", "DirPages", "", "TAG", "", "adapter", "Lcom/touptek/xcamview/activity/browse/TpThumbGridAdapter;", "allImageFiles", "", "Ljava/io/File;", "areActionsDisabled", "", "currentFolder", "currentPage", "folder", "folderAdapter", "LFolderAdapter;", "imageFiles", "imageLabels", "initialRootFolder", "isMenuShowing", "isOperationActive", "mainbinding", "Lcom/touptek/xcamview/databinding/BrowseLayoutBinding;", "menuCompareLayout", "Landroid/widget/LinearLayout;", "menuCopyLayout", "Landroid/view/View;", "menuCutLayout", "menuDetailsLayout", "menuPopupWindow", "Landroid/widget/PopupWindow;", "pageSize", "rightPanelBinding", "Lcom/touptek/xcamview/databinding/RightPanelLayoutBinding;", "toolIcons", "", "totalPages", "DisplayExpandFunctions", "", "StartImageDecode", "currentPosition", "StartVideoDecode", "file", "TopBtnClick", "position", "btnReturnClicked", "checkStoragePermission", "createNewFolder", "parent", "folderName", "deleteDirectory", "directory", "extractVideoMetadata", "callback", "Lkotlin/Function1;", "Lcom/touptek/xcamview/activity/browse/TpVideoBrowse$VideoMetadata;", "formatDuration", "millis", "", "formatFileSize", "size", "getExternalStoragePath", "getFileType", "getRootFolders", "getSelectedNames", "getSubFolders", "getUsbRootDirectory", "goToParentFolder", "initFolderView", "initPopupMenu", "initToolbar", "initViews", "isImageFile", "isRealPng", "loadFolderContents", "loadImageData", "menuItemClicked", "action", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onMoveComplete", "success", "onRequestPermissionsResult", "requestCode", "permissions", "", "grantResults", "(I[Ljava/lang/String;[I)V", "onUpdate", "openCopyDir", "performCopy", "performCut", "performDeleteOperation", "selectedFiles", "performImageCompare", "performPaste", "refreshFolderList", "requestStoragePermission", "setupBackButton", "showFileDetails", "showFolders", "showPictures", "showPreview", "showSoftInput", "editText", "Landroid/widget/EditText;", "showVideos", "startCreateFolder", "updateActionsState", "updateCopyButtonState", "menuItem", "disabled", "updateCutButtonState", "updateDetailsButtonState", "updateFolderTitle", "updateLabelState", "hidden", "updatePageData", "updateSelectAllButtonText", "getCreationTime", "Companion", "VideoMetadata", "app_debug"})
public final class TpVideoBrowse extends androidx.appcompat.app.AppCompatActivity implements com.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListener {
    private final java.lang.String TAG = "TpVideoBrowse";
    private com.touptek.xcamview.databinding.BrowseLayoutBinding mainbinding;
    private com.touptek.xcamview.databinding.RightPanelLayoutBinding rightPanelBinding;
    private java.util.List<? extends java.io.File> imageFiles;
    private java.util.List<java.lang.String> imageLabels;
    private android.widget.LinearLayout menuCompareLayout;
    private com.touptek.xcamview.activity.browse.TpThumbGridAdapter adapter;
    private int currentPage = 1;
    private final int pageSize = 12;
    private java.util.List<? extends java.io.File> allImageFiles;
    private java.io.File folder;
    private int totalPages = 1;
    private java.io.File currentFolder;
    private java.io.File initialRootFolder;
    private FolderAdapter folderAdapter;
    private boolean isMenuShowing = false;
    private android.widget.PopupWindow menuPopupWindow;
    private boolean isOperationActive = false;
    private android.view.View menuCopyLayout;
    private android.view.View menuCutLayout;
    private android.view.View menuDetailsLayout;
    private boolean areActionsDisabled = false;
    private int DirPages = 0;
    private final int[] toolIcons = null;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.browse.TpVideoBrowse.Companion Companion = null;
    private static final int STORAGE_PERMISSION_CODE = 1001;
    
    public TpVideoBrowse() {
        super();
    }
    
    @java.lang.Override
    public void onMoveComplete(boolean success) {
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupBackButton() {
    }
    
    private final void initFolderView() {
    }
    
    private final void initToolbar() {
    }
    
    private final void initPopupMenu() {
    }
    
    private final void updateActionsState() {
    }
    
    private final void updateCopyButtonState(android.view.View menuItem, boolean disabled) {
    }
    
    private final void updateCutButtonState(android.view.View menuItem, boolean disabled) {
    }
    
    private final void updateDetailsButtonState(android.view.View menuItem, boolean disabled) {
    }
    
    private final void DisplayExpandFunctions() {
    }
    
    private final void menuItemClicked(java.lang.String action) {
    }
    
    private final void performCopy() {
    }
    
    private final void performCut() {
    }
    
    private final void performPaste() {
    }
    
    private final void showFileDetails() {
    }
    
    private final void TopBtnClick(int position) {
    }
    
    private final void loadImageData() {
    }
    
    private final void updatePageData() {
    }
    
    private final void initViews() {
    }
    
    private final boolean checkStoragePermission() {
        return false;
    }
    
    private final void requestStoragePermission() {
    }
    
    @java.lang.Override
    public void onRequestPermissionsResult(int requestCode, @org.jetbrains.annotations.NotNull
    java.lang.String[] permissions, @org.jetbrains.annotations.NotNull
    int[] grantResults) {
    }
    
    private final java.lang.String getFileType(java.io.File file) {
        return null;
    }
    
    private final boolean isRealPng(java.io.File file) {
        return false;
    }
    
    private final void StartVideoDecode(java.io.File file) {
    }
    
    private final void StartImageDecode(java.util.List<? extends java.io.File> imageFiles, int currentPosition) {
    }
    
    private final void showPreview() {
    }
    
    private final void showPictures() {
    }
    
    private final void showVideos() {
    }
    
    private final void showFolders() {
    }
    
    private final void startCreateFolder() {
    }
    
    private final void createNewFolder(java.io.File parent, java.lang.String folderName) {
    }
    
    private final void showSoftInput(android.widget.EditText editText) {
    }
    
    private final void btnReturnClicked() {
    }
    
    private final java.io.File getUsbRootDirectory() {
        return null;
    }
    
    public final long getCreationTime(@org.jetbrains.annotations.NotNull
    java.io.File $this$getCreationTime) {
        return 0L;
    }
    
    private final java.lang.String getExternalStoragePath() {
        return null;
    }
    
    private final java.util.List<java.io.File> getRootFolders() {
        return null;
    }
    
    private final java.util.List<java.io.File> getSubFolders(java.io.File parent) {
        return null;
    }
    
    private final void loadFolderContents(java.io.File folder) {
    }
    
    private final void goToParentFolder() {
    }
    
    private final void updateFolderTitle(java.io.File folder) {
    }
    
    private final void refreshFolderList(java.io.File parent) {
    }
    
    public final void onUpdate() {
    }
    
    private final void openCopyDir() {
    }
    
    private final void getSelectedNames() {
    }
    
    private final void updateLabelState(boolean hidden) {
    }
    
    private final void performDeleteOperation(java.util.List<? extends java.io.File> selectedFiles) {
    }
    
    /**
     * 递归删除文件夹及其内容
     * @param directory 要删除的文件夹
     * @return 是否删除成功
     */
    private final boolean deleteDirectory(java.io.File directory) {
        return false;
    }
    
    private final void extractVideoMetadata(java.io.File file, kotlin.jvm.functions.Function1<? super com.touptek.xcamview.activity.browse.TpVideoBrowse.VideoMetadata, kotlin.Unit> callback) {
    }
    
    private final java.lang.String formatFileSize(long size) {
        return null;
    }
    
    private final java.lang.String formatDuration(long millis) {
        return null;
    }
    
    private final void updateSelectAllButtonText() {
    }
    
    /**
     * 执行图片对比 - 智能判断启动双图或多图对比
     */
    private final void performImageCompare() {
    }
    
    /**
     * 检查文件是否为图片格式
     */
    private final boolean isImageFile(java.io.File file) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0082\b\u0018\u00002\u00020\u0001B-\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J1\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0003H\u00d6\u0001R\u001a\u0010\u0004\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\t\"\u0004\b\n\u0010\u000bR\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\f\u0010\t\"\u0004\b\r\u0010\u000bR\u001a\u0010\u0006\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\t\"\u0004\b\u000f\u0010\u000bR\u001a\u0010\u0005\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\t\"\u0004\b\u0011\u0010\u000b\u00a8\u0006\u001d"}, d2 = {"Lcom/touptek/xcamview/activity/browse/TpVideoBrowse$VideoMetadata;", "", "duration", "", "bitrate", "resolution", "frameRate", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getBitrate", "()Ljava/lang/String;", "setBitrate", "(Ljava/lang/String;)V", "getDuration", "setDuration", "getFrameRate", "setFrameRate", "getResolution", "setResolution", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    static final class VideoMetadata {
        @org.jetbrains.annotations.NotNull
        private java.lang.String duration;
        @org.jetbrains.annotations.NotNull
        private java.lang.String bitrate;
        @org.jetbrains.annotations.NotNull
        private java.lang.String resolution;
        @org.jetbrains.annotations.NotNull
        private java.lang.String frameRate;
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.browse.TpVideoBrowse.VideoMetadata copy(@org.jetbrains.annotations.NotNull
        java.lang.String duration, @org.jetbrains.annotations.NotNull
        java.lang.String bitrate, @org.jetbrains.annotations.NotNull
        java.lang.String resolution, @org.jetbrains.annotations.NotNull
        java.lang.String frameRate) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public VideoMetadata() {
            super();
        }
        
        public VideoMetadata(@org.jetbrains.annotations.NotNull
        java.lang.String duration, @org.jetbrains.annotations.NotNull
        java.lang.String bitrate, @org.jetbrains.annotations.NotNull
        java.lang.String resolution, @org.jetbrains.annotations.NotNull
        java.lang.String frameRate) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getDuration() {
            return null;
        }
        
        public final void setDuration(@org.jetbrains.annotations.NotNull
        java.lang.String p0) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getBitrate() {
            return null;
        }
        
        public final void setBitrate(@org.jetbrains.annotations.NotNull
        java.lang.String p0) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getResolution() {
            return null;
        }
        
        public final void setResolution(@org.jetbrains.annotations.NotNull
        java.lang.String p0) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getFrameRate() {
            return null;
        }
        
        public final void setFrameRate(@org.jetbrains.annotations.NotNull
        java.lang.String p0) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/xcamview/activity/browse/TpVideoBrowse$Companion;", "", "()V", "STORAGE_PERMISSION_CODE", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}
// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSettingsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageButton btnClose;

  @NonNull
  public final FrameLayout contentContainer;

  @NonNull
  public final TextView itemFormat;

  @NonNull
  public final TextView itemMisc;

  @NonNull
  public final TextView itemVideo;

  @NonNull
  public final LinearLayout navigationContainer;

  private DialogSettingsBinding(@NonNull FrameLayout rootView, @NonNull ImageButton btnClose,
      @NonNull FrameLayout contentContainer, @NonNull TextView itemFormat,
      @NonNull TextView itemMisc, @NonNull TextView itemVideo,
      @NonNull LinearLayout navigationContainer) {
    this.rootView = rootView;
    this.btnClose = btnClose;
    this.contentContainer = contentContainer;
    this.itemFormat = itemFormat;
    this.itemMisc = itemMisc;
    this.itemVideo = itemVideo;
    this.navigationContainer = navigationContainer;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_close;
      ImageButton btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.content_container;
      FrameLayout contentContainer = ViewBindings.findChildViewById(rootView, id);
      if (contentContainer == null) {
        break missingId;
      }

      id = R.id.item_format;
      TextView itemFormat = ViewBindings.findChildViewById(rootView, id);
      if (itemFormat == null) {
        break missingId;
      }

      id = R.id.item_misc;
      TextView itemMisc = ViewBindings.findChildViewById(rootView, id);
      if (itemMisc == null) {
        break missingId;
      }

      id = R.id.item_video;
      TextView itemVideo = ViewBindings.findChildViewById(rootView, id);
      if (itemVideo == null) {
        break missingId;
      }

      id = R.id.navigation_container;
      LinearLayout navigationContainer = ViewBindings.findChildViewById(rootView, id);
      if (navigationContainer == null) {
        break missingId;
      }

      return new DialogSettingsBinding((FrameLayout) rootView, btnClose, contentContainer,
          itemFormat, itemMisc, itemVideo, navigationContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

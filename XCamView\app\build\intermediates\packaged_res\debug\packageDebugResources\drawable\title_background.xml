<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 白色背景 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/white"/>
            <corners android:topRightRadius="4dp"/>
        </shape>
    </item>

    <!-- 底部边框（与方框匹配） -->
    <item android:top="1dp">
        <shape android:shape="line">
            <stroke
                android:width="1dp"
                android:color="?attr/colorPrimary"/> <!-- 使用主题色 -->
            <size android:height="1dp"/>
        </shape>
    </item>
</layer-list>
<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 - 双重边框效果 + 轻微填充 -->
    <item android:state_pressed="true">
        <layer-list>
            <!-- 外层阴影边框 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="#40000000" />
                    <corners android:radius="14dp" />
                </shape>
            </item>
            <!-- 内层主边框 -->
            <item android:inset="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="#25FFFFFF" />
                    <stroke 
                        android:width="2dp" 
                        android:color="#FFFFFF" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- 禁用状态 - 半透明效果 -->
    <item android:state_enabled="false">
        <layer-list>
            <!-- 外层阴影边框 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="#20000000" />
                    <corners android:radius="14dp" />
                </shape>
            </item>
            <!-- 内层主边框 -->
            <item android:inset="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="#08FFFFFF" />
                    <stroke 
                        android:width="2dp" 
                        android:color="#60FFFFFF" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- 正常状态 - 双重边框立体效果 -->
    <item>
        <layer-list>
            <!-- 外层阴影边框 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="#60000000" />
                    <corners android:radius="14dp" />
                </shape>
            </item>
            <!-- 内层主边框 -->
            <item android:inset="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                    <stroke 
                        android:width="2dp" 
                        android:color="#FFFFFF" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>

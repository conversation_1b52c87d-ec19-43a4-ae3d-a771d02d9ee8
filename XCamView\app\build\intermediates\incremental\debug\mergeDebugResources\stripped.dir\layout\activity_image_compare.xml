<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#000000">
    
    <!-- 工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:background="#FFFFFF"
        android:elevation="8dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回"
            android:layout_marginEnd="8dp"
            android:scaleType="centerInside"
            android:padding="12dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:text="图片对比"
            android:textSize="20sp"
            android:textColor="#222222"
            android:gravity="center"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/btn_reset"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:src="@drawable/ic_refresh"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="重置"
            android:layout_marginEnd="8dp"
            android:scaleType="centerInside"
            android:padding="12dp" />

        <ImageButton
            android:id="@+id/btn_swap"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:src="@drawable/ic_swap_horiz"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="交换位置"
            android:scaleType="centerInside"
            android:padding="12dp" />

    </LinearLayout>
    
    <!-- 图片对比区域 -->
    <LinearLayout
        android:id="@+id/compare_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">
        
        <ImageView
            android:id="@+id/left_image"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:scaleType="matrix"
            android:background="#000000" />
            
        <View
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:background="#CCCCCC" />
            
        <ImageView
            android:id="@+id/right_image"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:scaleType="matrix"
            android:background="#000000" />
            
    </LinearLayout>
    
    <!-- 底部状态栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#F0F0F0"
        android:gravity="center">
        
        <TextView
            android:id="@+id/tv_left_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="图片1"
            android:textSize="12sp"
            android:textColor="#666666"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="middle" />
            
        <View
            android:layout_width="1dp"
            android:layout_height="20dp"
            android:background="#CCCCCC" />
            
        <TextView
            android:id="@+id/tv_right_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="图片2"
            android:textSize="12sp"
            android:textColor="#666666"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="middle" />
            
    </LinearLayout>
    
</LinearLayout>

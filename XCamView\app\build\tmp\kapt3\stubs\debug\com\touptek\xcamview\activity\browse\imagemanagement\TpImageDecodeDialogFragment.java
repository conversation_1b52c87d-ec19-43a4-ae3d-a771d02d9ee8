package com.touptek.xcamview.activity.browse.imagemanagement;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 92\u00020\u0001:\u00019B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020\u0013H\u0002J\b\u0010%\u001a\u00020#H\u0002J\u0010\u0010&\u001a\u00020#2\u0006\u0010\'\u001a\u00020(H\u0002J\b\u0010)\u001a\u00020#H\u0002J\u0012\u0010*\u001a\u00020#2\b\u0010+\u001a\u0004\u0018\u00010,H\u0016J$\u0010-\u001a\u00020(2\u0006\u0010.\u001a\u00020/2\b\u00100\u001a\u0004\u0018\u0001012\b\u0010+\u001a\u0004\u0018\u00010,H\u0016J\b\u00102\u001a\u00020#H\u0016J\u001a\u00103\u001a\u00020#2\u0006\u0010\'\u001a\u00020(2\b\u0010+\u001a\u0004\u0018\u00010,H\u0016J\b\u00104\u001a\u00020#H\u0002J\b\u00105\u001a\u00020#H\u0002J\b\u00106\u001a\u00020#H\u0002J\b\u00107\u001a\u00020#H\u0002J\b\u00108\u001a\u00020#H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u001d\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b \u0010!\u001a\u0004\b\u001e\u0010\u001f\u00a8\u0006:"}, d2 = {"Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment;", "Lcom/touptek/xcamview/util/BaseDialogFragment;", "()V", "CLICK_DURATION", "", "LONG_PRESS_DURATION", "SWIPE_THRESHOLD", "binding", "Lcom/touptek/xcamview/databinding/ImageViewerBinding;", "buttonsVisible", "", "currentPosition", "imagePaths", "", "", "isLongPress", "isMoved", "isSwipeGesture", "lastX", "", "lastY", "matrix", "Landroid/graphics/Matrix;", "scaleGestureDetector", "Landroid/view/ScaleGestureDetector;", "startTime", "", "startX", "startY", "touchSlop", "getTouchSlop", "()I", "touchSlop$delegate", "Lkotlin/Lazy;", "handleSwipeGesture", "", "deltaX", "initScaleGestureDetector", "initScaleTouchEvent", "view", "Landroid/view/View;", "loadCurrentImage", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onStart", "onViewCreated", "resetTouchState", "showNextImage", "showPreviousImage", "toggleButtons", "updateButtonStates", "Companion", "app_debug"})
public final class TpImageDecodeDialogFragment extends com.touptek.xcamview.util.BaseDialogFragment {
    private com.touptek.xcamview.databinding.ImageViewerBinding binding;
    private boolean buttonsVisible = false;
    private java.util.List<java.lang.String> imagePaths;
    private int currentPosition = 0;
    private float lastX = 0.0F;
    private float lastY = 0.0F;
    private final android.graphics.Matrix matrix = null;
    private android.view.ScaleGestureDetector scaleGestureDetector;
    private long startTime = 0L;
    private float startX = 0.0F;
    private float startY = 0.0F;
    private final int CLICK_DURATION = 300;
    private final int LONG_PRESS_DURATION = 200;
    private final int SWIPE_THRESHOLD = 50;
    private boolean isMoved = false;
    private boolean isLongPress = false;
    private boolean isSwipeGesture = false;
    private final kotlin.Lazy touchSlop$delegate = null;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment.Companion Companion = null;
    private static final java.lang.String ARG_IMAGE_PATHS = "image_paths";
    private static final java.lang.String ARG_CURRENT_POSITION = "current_position";
    
    public TpImageDecodeDialogFragment() {
        super();
    }
    
    private final int getTouchSlop() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    private final void loadCurrentImage() {
    }
    
    private final void showPreviousImage() {
    }
    
    private final void showNextImage() {
    }
    
    private final void updateButtonStates() {
    }
    
    private final void toggleButtons() {
    }
    
    private final void initScaleGestureDetector() {
    }
    
    private final void initScaleTouchEvent(android.view.View view) {
    }
    
    /**
     * 处理滑动手势翻页
     */
    private final void handleSwipeGesture(float deltaX) {
    }
    
    /**
     * 重置触摸状态
     */
    private final void resetTouchState() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\t2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment$Companion;", "", "()V", "ARG_CURRENT_POSITION", "", "ARG_IMAGE_PATHS", "newInstance", "Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment;", "imagePaths", "", "currentPosition", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment newInstance(@org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> imagePaths, int currentPosition) {
            return null;
        }
    }
}
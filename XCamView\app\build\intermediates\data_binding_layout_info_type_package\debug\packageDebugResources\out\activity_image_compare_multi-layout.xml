<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_image_compare_multi" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\activity_image_compare_multi.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_image_compare_multi_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="194" endOffset="14"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="45"/></Target><Target id="@+id/btn_sync_mode" view="ImageButton"><Expressions/><location startLine="35" startOffset="8" endLine="41" endOffset="47"/></Target><Target id="@+id/btn_reset" view="ImageButton"><Expressions/><location startLine="43" startOffset="8" endLine="49" endOffset="45"/></Target><Target id="@+id/image_view_1" view="com.touptek.measurerealize.TpImageView"><Expressions/><location startLine="67" startOffset="12" endLine="72" endOffset="46"/></Target><Target id="@+id/image_view_2" view="com.touptek.measurerealize.TpImageView"><Expressions/><location startLine="79" startOffset="12" endLine="84" endOffset="46"/></Target><Target id="@+id/image_view_3" view="com.touptek.measurerealize.TpImageView"><Expressions/><location startLine="101" startOffset="12" endLine="106" endOffset="46"/></Target><Target id="@+id/image_view_4" view="com.touptek.measurerealize.TpImageView"><Expressions/><location startLine="113" startOffset="12" endLine="118" endOffset="46"/></Target><Target id="@+id/tv_info_1" view="TextView"><Expressions/><location startLine="138" startOffset="12" endLine="147" endOffset="39"/></Target><Target id="@+id/tv_info_2" view="TextView"><Expressions/><location startLine="149" startOffset="12" endLine="158" endOffset="39"/></Target><Target id="@+id/tv_info_3" view="TextView"><Expressions/><location startLine="168" startOffset="12" endLine="177" endOffset="39"/></Target><Target id="@+id/tv_info_4" view="TextView"><Expressions/><location startLine="179" startOffset="12" endLine="188" endOffset="39"/></Target></Targets></Layout>
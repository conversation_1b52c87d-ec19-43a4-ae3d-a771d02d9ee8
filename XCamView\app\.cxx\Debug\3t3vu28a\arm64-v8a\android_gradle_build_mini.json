{"buildFiles": ["C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\hhx\\rk3588\\AndroidStudio\\XCamView\\app\\.cxx\\Debug\\3t3vu28a\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PopupConfigMenuBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout menuCompare;

  @NonNull
  public final LinearLayout menuCompareMulti;

  @NonNull
  public final LinearLayout menuContainer;

  @NonNull
  public final LinearLayout menuCopy;

  @NonNull
  public final LinearLayout menuCut;

  @NonNull
  public final LinearLayout menuDetails;

  @NonNull
  public final LinearLayout menuPaste;

  @NonNull
  public final TextView textCopy;

  private PopupConfigMenuBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout menuCompare,
      @NonNull LinearLayout menuCompareMulti, @NonNull LinearLayout menuContainer,
      @NonNull LinearLayout menuCopy, @NonNull LinearLayout menuCut,
      @NonNull LinearLayout menuDetails, @NonNull LinearLayout menuPaste,
      @NonNull TextView textCopy) {
    this.rootView = rootView;
    this.menuCompare = menuCompare;
    this.menuCompareMulti = menuCompareMulti;
    this.menuContainer = menuContainer;
    this.menuCopy = menuCopy;
    this.menuCut = menuCut;
    this.menuDetails = menuDetails;
    this.menuPaste = menuPaste;
    this.textCopy = textCopy;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PopupConfigMenuBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PopupConfigMenuBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.popup_config_menu, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PopupConfigMenuBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.menu_compare;
      LinearLayout menuCompare = ViewBindings.findChildViewById(rootView, id);
      if (menuCompare == null) {
        break missingId;
      }

      id = R.id.menu_compare_multi;
      LinearLayout menuCompareMulti = ViewBindings.findChildViewById(rootView, id);
      if (menuCompareMulti == null) {
        break missingId;
      }

      LinearLayout menuContainer = (LinearLayout) rootView;

      id = R.id.menu_copy;
      LinearLayout menuCopy = ViewBindings.findChildViewById(rootView, id);
      if (menuCopy == null) {
        break missingId;
      }

      id = R.id.menu_cut;
      LinearLayout menuCut = ViewBindings.findChildViewById(rootView, id);
      if (menuCut == null) {
        break missingId;
      }

      id = R.id.menu_details;
      LinearLayout menuDetails = ViewBindings.findChildViewById(rootView, id);
      if (menuDetails == null) {
        break missingId;
      }

      id = R.id.menu_paste;
      LinearLayout menuPaste = ViewBindings.findChildViewById(rootView, id);
      if (menuPaste == null) {
        break missingId;
      }

      id = R.id.text_copy;
      TextView textCopy = ViewBindings.findChildViewById(rootView, id);
      if (textCopy == null) {
        break missingId;
      }

      return new PopupConfigMenuBinding((LinearLayout) rootView, menuCompare, menuCompareMulti,
          menuContainer, menuCopy, menuCut, menuDetails, menuPaste, textCopy);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

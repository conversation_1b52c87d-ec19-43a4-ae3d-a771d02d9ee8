// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityImageCompareBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnReset;

  @NonNull
  public final ImageButton btnSwap;

  @NonNull
  public final LinearLayout compareContainer;

  @NonNull
  public final ImageView leftImage;

  @NonNull
  public final ImageView rightImage;

  @NonNull
  public final TextView tvLeftInfo;

  @NonNull
  public final TextView tvRightInfo;

  private ActivityImageCompareBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnBack,
      @NonNull ImageButton btnReset, @NonNull ImageButton btnSwap,
      @NonNull LinearLayout compareContainer, @NonNull ImageView leftImage,
      @NonNull ImageView rightImage, @NonNull TextView tvLeftInfo, @NonNull TextView tvRightInfo) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnReset = btnReset;
    this.btnSwap = btnSwap;
    this.compareContainer = compareContainer;
    this.leftImage = leftImage;
    this.rightImage = rightImage;
    this.tvLeftInfo = tvLeftInfo;
    this.tvRightInfo = tvRightInfo;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityImageCompareBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityImageCompareBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_image_compare, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityImageCompareBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_reset;
      ImageButton btnReset = ViewBindings.findChildViewById(rootView, id);
      if (btnReset == null) {
        break missingId;
      }

      id = R.id.btn_swap;
      ImageButton btnSwap = ViewBindings.findChildViewById(rootView, id);
      if (btnSwap == null) {
        break missingId;
      }

      id = R.id.compare_container;
      LinearLayout compareContainer = ViewBindings.findChildViewById(rootView, id);
      if (compareContainer == null) {
        break missingId;
      }

      id = R.id.left_image;
      ImageView leftImage = ViewBindings.findChildViewById(rootView, id);
      if (leftImage == null) {
        break missingId;
      }

      id = R.id.right_image;
      ImageView rightImage = ViewBindings.findChildViewById(rootView, id);
      if (rightImage == null) {
        break missingId;
      }

      id = R.id.tv_left_info;
      TextView tvLeftInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvLeftInfo == null) {
        break missingId;
      }

      id = R.id.tv_right_info;
      TextView tvRightInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvRightInfo == null) {
        break missingId;
      }

      return new ActivityImageCompareBinding((LinearLayout) rootView, btnBack, btnReset, btnSwap,
          compareContainer, leftImage, rightImage, tvLeftInfo, tvRightInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

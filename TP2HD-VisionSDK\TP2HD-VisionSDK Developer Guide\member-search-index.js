memberSearchIndex = [{"p":"com.touptek.video","c":"TpIspParam","l":"addOnDataChangedListener(TpIspParam.OnDataChangedListener)","u":"addOnDataChangedListener(com.touptek.video.TpIspParam.OnDataChangedListener)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"applyISPParamUpdate(int, int, int, int)","u":"applyISPParamUpdate(int,int,int,int)"},{"p":"com.touptek.video","c":"TpIspParam","l":"applyScene(String)","u":"applyScene(java.lang.String)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"applyTransform(Matrix)","u":"applyTransform(android.graphics.Matrix)"},{"p":"com.touptek.video","c":"TpVideoConfig.Builder","l":"build()"},{"p":"com.touptek.video","c":"TpVideoConfig.Builder","l":"Builder(int, int)","u":"%3Cinit%3E(int,int)"},{"p":"com.touptek.video","c":"TpVideoSystem.StreamType","l":"CAMERA"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"captureImage(String)","u":"captureImage(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoConfig.BitrateMode","l":"CBR"},{"p":"com.touptek.video","c":"TpVideoConfig.BitrateMode","l":"CQ"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"createDefault1080P()"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"createDefault4K()"},{"p":"com.touptek.utils","c":"TpFileManager","l":"createImagePath(Context)","u":"createImagePath(android.content.Context)"},{"p":"com.touptek.utils","c":"TpFileManager","l":"createImagePath(Context, String, boolean, String)","u":"createImagePath(android.content.Context,java.lang.String,boolean,java.lang.String)"},{"p":"com.touptek.utils","c":"TpFileManager","l":"createVideoPath(Context)","u":"createVideoPath(android.content.Context)"},{"p":"com.touptek.video","c":"TpIspParam.ParamData","l":"current"},{"p":"com.touptek.video","c":"TpIspParam.ParamData","l":"defaultValue"},{"p":"com.touptek.video","c":"TpIspParam","l":"deleteScene(String)","u":"deleteScene(java.lang.String)"},{"p":"com.touptek.video","c":"TpIspParam.SceneInfo","l":"description"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"fastForward(int)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"fastRewind(int)"},{"p":"com.touptek.video","c":"TpIspParam","l":"fromInt(int)"},{"p":"com.touptek.utils","c":"TpFileManager","l":"generateUniqueFileName(String)","u":"generateUniqueFileName(java.lang.String)"},{"p":"com.touptek.video","c":"TpIspParam","l":"getAllCurrentValues()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getAllParamInfo()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getAllSceneNames()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"getAvailableNetworkInterfaces()"},{"p":"com.touptek.utils","c":"TpFileManager","l":"getAvailableStorageSpace(String)","u":"getAvailableStorageSpace(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"getBitRate()"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"getBitrateMode()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getCameraManager()"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"getCodec()"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"getConnectionParams()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"getCurrentHotspotState()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"getCurrentPosition()"},{"p":"com.touptek.ui","c":"TpImageView","l":"getCurrentScale()"},{"p":"com.touptek.ui","c":"TpTextureView","l":"getCurrentScale()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getCurrentStreamType()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getCurrentValue(TpIspParam)","u":"getCurrentValue(com.touptek.video.TpIspParam)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getCurrentVideoPath()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getCurrentVideoPlaybackSpeed()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getCurrentVideoPosition()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"getCurrentWifiState()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getDefaultValue(TpIspParam)","u":"getDefaultValue(com.touptek.video.TpIspParam)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.NetworkInterfaceInfo","l":"getDescription()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"getDuration()"},{"p":"com.touptek.utils","c":"TpFileManager","l":"getExternalStoragePath(Context)","u":"getExternalStoragePath(android.content.Context)"},{"p":"com.touptek.utils","c":"TpFileManager","l":"getFileSystemType(String)","u":"getFileSystemType(java.lang.String)"},{"p":"com.touptek.ui","c":"TpImageView","l":"getFitScreenScale()"},{"p":"com.touptek.utils","c":"TpHdmiMonitor","l":"getFrameRate()"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"getFrameRate()"},{"p":"com.touptek.utils","c":"TpHdmiMonitor","l":"getHdmiResolution()"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"getHeight()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getImageCapture()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.HotspotInfo","l":"getInfo()"},{"p":"com.touptek.utils","c":"TpHdmiMonitor","l":"getInstance()"},{"p":"com.touptek.utils","c":"TpFileManager","l":"getInternalStoragePath(Context)","u":"getInternalStoragePath(android.content.Context)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.NetworkInterfaceInfo","l":"getIpAddress()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getIsDisableValue(TpIspParam)","u":"getIsDisableValue(com.touptek.video.TpIspParam)"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"getKeyFrameInterval()"},{"p":"com.touptek.ui","c":"TpImageView","l":"getMaxScale()"},{"p":"com.touptek.ui","c":"TpTextureView","l":"getMaxScale()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getMaxValue(TpIspParam)","u":"getMaxValue(com.touptek.video.TpIspParam)"},{"p":"com.touptek.video","c":"TpVideoConfig.VideoCodec","l":"getMimeType()"},{"p":"com.touptek.ui","c":"TpImageView","l":"getMinScale()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getMinValue(TpIspParam)","u":"getMinValue(com.touptek.video.TpIspParam)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.NetworkInterfaceInfo","l":"getName()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getParamByIndex(int)"},{"p":"com.touptek.video","c":"TpIspParam","l":"getParamId()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getParamInfo(TpIspParam)","u":"getParamInfo(com.touptek.video.TpIspParam)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"getPlaybackSpeed()"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"getRemoteDirectories(TpSambaClient.DirectoryListListener)","u":"getRemoteDirectories(com.touptek.utils.TpSambaClient.DirectoryListListener)"},{"p":"com.touptek.utils","c":"TpSambaClient.SMBConfig","l":"getRemotePath()"},{"p":"com.touptek.video","c":"TpIspParam","l":"getSceneInfo(String)","u":"getSceneInfo(java.lang.String)"},{"p":"com.touptek.utils","c":"TpSambaClient.SMBConfig","l":"getServerIp()"},{"p":"com.touptek.utils","c":"TpSambaClient.SMBConfig","l":"getShareName()"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"getSize()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.WifiConnectionInfo","l":"getSsid()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getStreamingService()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getStreamUrl()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"getTextureView()"},{"p":"com.touptek.ui","c":"TpTextureView","l":"getTouchEventHandler()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getTvPreviewHelper()"},{"p":"com.touptek.utils","c":"TpSambaClient.SMBConfig","l":"getUsername()"},{"p":"com.touptek.video","c":"TpVideoConfig.BitrateMode","l":"getValue()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getVideoConfig()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getVideoDecoder()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getVideoDuration()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"getVideoEncoder()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"getVideoPlayerListener()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"getVideoSystem()"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"getWidth()"},{"p":"com.touptek.video","c":"TpVideoConfig.VideoCodec","l":"H264"},{"p":"com.touptek.video","c":"TpVideoConfig.VideoCodec","l":"H265"},{"p":"com.touptek.video","c":"TpIspParam","l":"handleReceivedData(TpIspParam, long, boolean)","u":"handleReceivedData(com.touptek.video.TpIspParam,long,boolean)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.HotspotInfo","l":"HotspotInfo(boolean, String)","u":"%3Cinit%3E(boolean,java.lang.String)"},{"p":"com.touptek.utils","c":"TpHdmiMonitor","l":"init()"},{"p":"com.touptek.video","c":"TpIspParam","l":"init(Context)","u":"init(android.content.Context)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"initialize(Surface)","u":"initialize(android.view.Surface)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"isCameraStarted()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.WifiConnectionInfo","l":"isConnected()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"isControlsVisible()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"isCurrentVideoPlaybackCompleted()"},{"p":"com.touptek.video","c":"TpIspParam.ParamData","l":"isDisabled"},{"p":"com.touptek.ui","c":"TpImageView","l":"isDoubleTapEnabled()"},{"p":"com.touptek.ui","c":"TpTextureView","l":"isDoubleTapEnabled()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.HotspotInfo","l":"isEnabled()"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"isEnabled()"},{"p":"com.touptek.utils","c":"TpSambaClient.SMBConfig","l":"isEnabled()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"isEthernetConnected()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"isInitialized()"},{"p":"com.touptek.ui","c":"TpImageView","l":"isPanEnabled()"},{"p":"com.touptek.ui","c":"TpTextureView","l":"isPanEnabled()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"isPlaybackCompleted()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"isPlaying()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"isPreviewPaused()"},{"p":"com.touptek.video","c":"TpIspParam.SceneInfo","l":"isProtected"},{"p":"com.touptek.video","c":"TpIspParam","l":"isRangeReceived()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"isRecording()"},{"p":"com.touptek.ui","c":"TpRoiView","l":"isROIEnabled()"},{"p":"com.touptek.video","c":"TpIspParam","l":"isSerialConnected()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"isStreaming()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"isTvMode()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"isVideoPlaying()"},{"p":"com.touptek.ui","c":"TpImageView","l":"isZoomEnabled()"},{"p":"com.touptek.ui","c":"TpTextureView","l":"isZoomEnabled()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"loadFullImage(String, ImageView)","u":"loadFullImage(java.lang.String,android.widget.ImageView)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"loadThumbnail(String, ImageView)","u":"loadThumbnail(java.lang.String,android.widget.ImageView)"},{"p":"com.touptek.video","c":"TpIspParam.ParamData","l":"max"},{"p":"com.touptek.video","c":"TpIspParam.ParamData","l":"min"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.NetworkInterfaceInfo","l":"NetworkInterfaceInfo(String, String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onCameraStarted()"},{"p":"com.touptek.ui","c":"TpRoiView","l":"onDataChanged(TpIspParam, int)","u":"onDataChanged(com.touptek.video.TpIspParam,int)"},{"p":"com.touptek.video","c":"TpIspParam.OnDataChangedListener","l":"onDataChanged(TpIspParam, int)","u":"onDataChanged(com.touptek.video.TpIspParam,int)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"onDetachedFromWindow()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"onDetachedFromWindow()"},{"p":"com.touptek.utils","c":"TpSambaClient.DirectoryListListener","l":"onDirectoriesLoaded(List<String>)","u":"onDirectoriesLoaded(java.util.List)"},{"p":"com.touptek.utils","c":"TpSambaClient.DirectoryListListener","l":"onDirectoryLoadFailed(String)","u":"onDirectoryLoadFailed(java.lang.String)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"onDraw(Canvas)","u":"onDraw(android.graphics.Canvas)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemAdapter","l":"onError(String)","u":"onError(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onError(String)","u":"onError(java.lang.String)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.NetworkStateListener","l":"onEthernetStateChanged(boolean)"},{"p":"com.touptek.utils","c":"TpHdmiMonitor.HdmiListener","l":"onHdmiStatusChanged(boolean)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.NetworkStateListener","l":"onHotspotStateChanged(boolean, String)","u":"onHotspotStateChanged(boolean,java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onImageCaptured(String)","u":"onImageCaptured(java.lang.String)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"onLongDataChanged(TpIspParam, long)","u":"onLongDataChanged(com.touptek.video.TpIspParam,long)"},{"p":"com.touptek.video","c":"TpIspParam.OnDataChangedListener","l":"onLongDataChanged(TpIspParam, long)","u":"onLongDataChanged(com.touptek.video.TpIspParam,long)"},{"p":"com.touptek.ui","c":"TpTextureView.TouchEventHandler","l":"onLongPressDetected(MotionEvent)","u":"onLongPressDetected(android.view.MotionEvent)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView.VideoPlayerListener","l":"onNextVideo()"},{"p":"com.touptek.ui","c":"TpTextureView.TouchEventHandler","l":"onPanGestureDetected(MotionEvent)","u":"onPanGestureDetected(android.view.MotionEvent)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView.VideoPlayerListener","l":"onPreviousVideo()"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onRecordingStarted(String)","u":"onRecordingStarted(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onRecordingStopped(String)","u":"onRecordingStopped(java.lang.String)"},{"p":"com.touptek.ui","c":"TpTextureView.TouchEventHandler","l":"onScaleGestureDetected(MotionEvent)","u":"onScaleGestureDetected(android.view.MotionEvent)"},{"p":"com.touptek.video","c":"TpIspParam.OnSerialStateChangedListener","l":"onSerialStateChanged(boolean)"},{"p":"com.touptek.ui","c":"TpTextureView.TouchEventHandler","l":"onSingleTapDetected(MotionEvent)","u":"onSingleTapDetected(android.view.MotionEvent)"},{"p":"com.touptek.ui","c":"TpImageView","l":"onSizeChanged(int, int, int, int)","u":"onSizeChanged(int,int,int,int)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"onSizeChanged(int, int, int, int)","u":"onSizeChanged(int,int,int,int)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onStreamingStatusChanged(boolean, String)","u":"onStreamingStatusChanged(boolean,java.lang.String)"},{"p":"com.touptek.ui","c":"TpImageView","l":"onTouchEvent(MotionEvent)","u":"onTouchEvent(android.view.MotionEvent)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"onTouchEvent(MotionEvent)","u":"onTouchEvent(android.view.MotionEvent)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"onTouchEvent(MotionEvent)","u":"onTouchEvent(android.view.MotionEvent)"},{"p":"com.touptek.utils","c":"TpSambaClient.UploadListener","l":"onUploadFailed(String)","u":"onUploadFailed(java.lang.String)"},{"p":"com.touptek.utils","c":"TpSambaClient.UploadListener","l":"onUploadSuccess(String)","u":"onUploadSuccess(java.lang.String)"},{"p":"com.touptek.utils","c":"TpFileManager.StorageListener","l":"onUsbDriveConnected(String)","u":"onUsbDriveConnected(java.lang.String)"},{"p":"com.touptek.utils","c":"TpFileManager.StorageListener","l":"onUsbDriveDisconnected(String)","u":"onUsbDriveDisconnected(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onVideoPlaybackCompleted(String)","u":"onVideoPlaybackCompleted(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onVideoPlaybackStarted(String)","u":"onVideoPlaybackStarted(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemListener","l":"onVideoPlaybackStopped()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.NetworkStateListener","l":"onWifiStateChanged(boolean, String)","u":"onWifiStateChanged(boolean,java.lang.String)"},{"p":"com.touptek.ui","c":"TpImageView.OnZoomChangeListener","l":"onZoomChanged(float, float, float)","u":"onZoomChanged(float,float,float)"},{"p":"com.touptek.ui","c":"TpTextureView.OnZoomChangeListener","l":"onZoomChanged(float, float, float)","u":"onZoomChanged(float,float,float)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"openHotspotSettings()"},{"p":"com.touptek.video","c":"TpIspParam.ParamData","l":"ParamData(int, int, int, int, boolean)","u":"%3Cinit%3E(int,int,int,int,boolean)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"pause()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"pauseHotspotMonitoring()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"pausePreview()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"pauseVideo()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"play()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"playVideo(String, Surface)","u":"playVideo(java.lang.String,android.view.Surface)"},{"p":"com.touptek.video","c":"TpIspParam","l":"release()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"release()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"releaseVideo()"},{"p":"com.touptek.video","c":"TpIspParam","l":"removeOnDataChangedListener(TpIspParam.OnDataChangedListener)","u":"removeOnDataChangedListener(com.touptek.video.TpIspParam.OnDataChangedListener)"},{"p":"com.touptek.video","c":"TpIspParam","l":"requestAllParamRanges()"},{"p":"com.touptek.video","c":"TpIspParam","l":"requestAllParamRanges(boolean)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"resetCurrentVideoToStart()"},{"p":"com.touptek.ui","c":"TpImageView","l":"resetMatrix()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"resetToStart()"},{"p":"com.touptek.ui","c":"TpRoiView","l":"resetTransforms()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"resumeHotspotMonitoring()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"resumePreview()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"resumeVideo()"},{"p":"com.touptek.video","c":"TpIspParam","l":"saveAllDefaultValuesToLocal(boolean)"},{"p":"com.touptek.video","c":"TpIspParam","l":"saveCurrentAsScene(String)","u":"saveCurrentAsScene(java.lang.String)"},{"p":"com.touptek.video","c":"TpIspParam.SceneInfo","l":"SceneInfo(String, String, String, long, boolean, int)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,long,boolean,int)"},{"p":"com.touptek.video","c":"TpIspParam.SceneInfo","l":"sceneName"},{"p":"com.touptek.video","c":"TpIspParam.SceneInfo","l":"sceneType"},{"p":"com.touptek.video","c":"TpIspParam.SceneInfo","l":"sceneValue"},{"p":"com.touptek.video","c":"TpVideoSystem.StreamType","l":"SCREEN"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"seekCurrentVideoRelative(long)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"seekRelative(long)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"seekTo(long)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"seekVideoTo(long)"},{"p":"com.touptek.video","c":"TpVideoConfig.Builder","l":"setBitRate(int)"},{"p":"com.touptek.video","c":"TpVideoConfig.Builder","l":"setBitrateMode(TpVideoConfig.BitrateMode)","u":"setBitrateMode(com.touptek.video.TpVideoConfig.BitrateMode)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"setCameraResolution(int, int)","u":"setCameraResolution(int,int)"},{"p":"com.touptek.video","c":"TpVideoConfig.Builder","l":"setCodec(TpVideoConfig.VideoCodec)","u":"setCodec(com.touptek.video.TpVideoConfig.VideoCodec)"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"setConnectionParams(String, String, String, String, String, boolean)","u":"setConnectionParams(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"setConnectionParams(TpSambaClient.SMBConfig)","u":"setConnectionParams(com.touptek.utils.TpSambaClient.SMBConfig)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"setCurrentVideoPlaybackSpeed(float)"},{"p":"com.touptek.ui","c":"TpImageView","l":"setDoubleTapEnabled(boolean)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"setDoubleTapEnabled(boolean)"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"setEnabled(boolean)"},{"p":"com.touptek.utils","c":"TpHdmiMonitor","l":"setHdmiListener(TpHdmiMonitor.HdmiListener)","u":"setHdmiListener(com.touptek.utils.TpHdmiMonitor.HdmiListener)"},{"p":"com.touptek.ui","c":"TpImageView","l":"setImageDrawable(Drawable)","u":"setImageDrawable(android.graphics.drawable.Drawable)"},{"p":"com.touptek.video","c":"TpVideoConfig.Builder","l":"setKeyFrameInterval(int)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"setListener(TpVideoSystem.TpVideoSystemListener)","u":"setListener(com.touptek.video.TpVideoSystem.TpVideoSystemListener)"},{"p":"com.touptek.ui","c":"TpImageView","l":"setMaxScale(float)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"setMaxScale(float)"},{"p":"com.touptek.video","c":"TpIspParam","l":"setOnSerialStateChangedListener(TpIspParam.OnSerialStateChangedListener)","u":"setOnSerialStateChangedListener(com.touptek.video.TpIspParam.OnSerialStateChangedListener)"},{"p":"com.touptek.ui","c":"TpImageView","l":"setOnZoomChangeListener(TpImageView.OnZoomChangeListener)","u":"setOnZoomChangeListener(com.touptek.ui.TpImageView.OnZoomChangeListener)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"setOnZoomChangeListener(TpTextureView.OnZoomChangeListener)","u":"setOnZoomChangeListener(com.touptek.ui.TpTextureView.OnZoomChangeListener)"},{"p":"com.touptek.ui","c":"TpImageView","l":"setPanEnabled(boolean)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"setPanEnabled(boolean)"},{"p":"com.touptek.video","c":"TpIspParam","l":"setParamDefault(TpIspParam, int)","u":"setParamDefault(com.touptek.video.TpIspParam,int)"},{"p":"com.touptek.video","c":"TpIspParam","l":"setParamDisabled(TpIspParam, boolean)","u":"setParamDisabled(com.touptek.video.TpIspParam,boolean)"},{"p":"com.touptek.video","c":"TpIspParam","l":"setParamMaxValue(TpIspParam, int)","u":"setParamMaxValue(com.touptek.video.TpIspParam,int)"},{"p":"com.touptek.video","c":"TpIspParam","l":"setParamMinValue(TpIspParam, int)","u":"setParamMinValue(com.touptek.video.TpIspParam,int)"},{"p":"com.touptek.video","c":"TpIspParam","l":"setParamRange(TpIspParam, boolean, int, int, int)","u":"setParamRange(com.touptek.video.TpIspParam,boolean,int,int,int)"},{"p":"com.touptek.video","c":"TpIspParam","l":"setParamsRangeReceived(boolean)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"setPlaybackSpeed(float)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"setROIEnabled(boolean)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"setRoiView(TpRoiView)","u":"setRoiView(com.touptek.ui.TpRoiView)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"setShowControls(boolean)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"setStreamType(TpVideoSystem.StreamType)","u":"setStreamType(com.touptek.video.TpVideoSystem.StreamType)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"setTouchEventHandler(TpTextureView.TouchEventHandler)","u":"setTouchEventHandler(com.touptek.ui.TpTextureView.TouchEventHandler)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"setTvContainer(ViewGroup)","u":"setTvContainer(android.view.ViewGroup)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"setVideoPath(String)","u":"setVideoPath(java.lang.String)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"setVideoPlayerListener(TpVideoPlayerView.VideoPlayerListener)","u":"setVideoPlayerListener(com.touptek.ui.TpVideoPlayerView.VideoPlayerListener)"},{"p":"com.touptek.ui","c":"TpImageView","l":"setZoomEnabled(boolean)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"setZoomEnabled(boolean)"},{"p":"com.touptek.utils","c":"TpSambaClient.SMBConfig","l":"SMBConfig(String, String, String, String, String, boolean)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"startMonitoring()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"startRecording(String)","u":"startRecording(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"startStreaming()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"startStreaming(TpVideoSystem.StreamType)","u":"startStreaming(com.touptek.video.TpVideoSystem.StreamType)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"startStreaming(TpVideoSystem.StreamType, String)","u":"startStreaming(com.touptek.video.TpVideoSystem.StreamType,java.lang.String)"},{"p":"com.touptek.utils","c":"TpFileManager","l":"startUsbDriveMonitor(Context, TpFileManager.StorageListener)","u":"startUsbDriveMonitor(android.content.Context,com.touptek.utils.TpFileManager.StorageListener)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"stepCurrentVideoFrame()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"stepFrame()"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"stop()"},{"p":"com.touptek.utils","c":"TpHdmiMonitor","l":"stop()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"stopMonitoring()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"stopRecording()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"stopStreaming()"},{"p":"com.touptek.utils","c":"TpFileManager","l":"stopUsbDriveMonitor(Context)","u":"stopUsbDriveMonitor(android.content.Context)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"switchToCameraMode()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"switchToTvMode()"},{"p":"com.touptek.video","c":"TpIspParam","l":"syncAllCurrentValuesToDevice()"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"testConnection(TpSambaClient.UploadListener)","u":"testConnection(com.touptek.utils.TpSambaClient.UploadListener)"},{"p":"com.touptek.video","c":"TpIspParam.SceneInfo","l":"timestamp"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"togglePreviewPause()"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"toggleWifi(boolean)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.NetworkInterfaceInfo","l":"toString()"},{"p":"com.touptek.video","c":"TpIspParam.ParamData","l":"toString()"},{"p":"com.touptek.video","c":"TpIspParam.SceneInfo","l":"toString()"},{"p":"com.touptek.video","c":"TpVideoConfig","l":"toString()"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_BANDWIDTH"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_BRIGHTNESS"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_COLORORGRAY"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_COLORTONE"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_CONTRAST"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_CTBLUEGAIN"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_CTGREENGAIN"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_CTREDGAIN"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_DARKENHANCE"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_DENOISE"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_EXPOSURECHOICE"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_EXPOSURECOMPENSATION"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_EXPOSUREGAIN"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_EXPOSURETIME"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_FLIP"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_GAMMA"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_HUE"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_HZ"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_ISP_DEFAULT_TYPE"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_LDCRATIO"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_MIRROR"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_ROI_HEIGHT"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_ROI_LEFT"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_ROI_TOP"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_ROI_WIDTH"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_SATURATION"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_SHARPNESS"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_VERSION"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_WBBLUEGAIN"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_WBCHOICE"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_WBGREENGAIN"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_WBREDGAIN"},{"p":"com.touptek.video","c":"TpIspParam","l":"TOUPTEK_PARAM_WDREXPRATIO"},{"p":"com.touptek.utils","c":"TpFileManager","l":"TpFileManager()","u":"%3Cinit%3E()"},{"p":"com.touptek.utils","c":"TpHdmiMonitor","l":"TpHdmiMonitor()","u":"%3Cinit%3E()"},{"p":"com.touptek.ui","c":"TpImageView","l":"TpImageView(Context)","u":"%3Cinit%3E(android.content.Context)"},{"p":"com.touptek.ui","c":"TpImageView","l":"TpImageView(Context, AttributeSet)","u":"%3Cinit%3E(android.content.Context,android.util.AttributeSet)"},{"p":"com.touptek.ui","c":"TpImageView","l":"TpImageView(Context, AttributeSet, int)","u":"%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)"},{"p":"com.touptek.utils","c":"TpNetworkMonitor","l":"TpNetworkMonitor(Context, TpNetworkMonitor.NetworkStateListener, ActivityResultLauncher<Intent>)","u":"%3Cinit%3E(android.content.Context,com.touptek.utils.TpNetworkMonitor.NetworkStateListener,androidx.activity.result.ActivityResultLauncher)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"TpRoiView(Context)","u":"%3Cinit%3E(android.content.Context)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"TpRoiView(Context, AttributeSet)","u":"%3Cinit%3E(android.content.Context,android.util.AttributeSet)"},{"p":"com.touptek.ui","c":"TpRoiView","l":"TpRoiView(Context, AttributeSet, int)","u":"%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"TpSambaClient(Context)","u":"%3Cinit%3E(android.content.Context)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"TpTextureView(Context)","u":"%3Cinit%3E(android.content.Context)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"TpTextureView(Context, AttributeSet)","u":"%3Cinit%3E(android.content.Context,android.util.AttributeSet)"},{"p":"com.touptek.ui","c":"TpTextureView","l":"TpTextureView(Context, AttributeSet, int)","u":"%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"TpVideoPlayerView(Context)","u":"%3Cinit%3E(android.content.Context)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"TpVideoPlayerView(Context, AttributeSet)","u":"%3Cinit%3E(android.content.Context,android.util.AttributeSet)"},{"p":"com.touptek.ui","c":"TpVideoPlayerView","l":"TpVideoPlayerView(Context, AttributeSet, int)","u":"%3Cinit%3E(android.content.Context,android.util.AttributeSet,int)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"TpVideoSystem(AppCompatActivity)","u":"%3Cinit%3E(androidx.appcompat.app.AppCompatActivity)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"TpVideoSystem(AppCompatActivity, TpVideoConfig)","u":"%3Cinit%3E(androidx.appcompat.app.AppCompatActivity,com.touptek.video.TpVideoConfig)"},{"p":"com.touptek.video","c":"TpVideoSystem.TpVideoSystemAdapter","l":"TpVideoSystemAdapter()","u":"%3Cinit%3E()"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"updateBitRate(int)"},{"p":"com.touptek.video","c":"TpIspParam","l":"updateParam(TpIspParam, int)","u":"updateParam(com.touptek.video.TpIspParam,int)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"updateResolution(int, int)","u":"updateResolution(int,int)"},{"p":"com.touptek.video","c":"TpVideoSystem","l":"updateVideoConfig(TpVideoConfig)","u":"updateVideoConfig(com.touptek.video.TpVideoConfig)"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"uploadFile(String, String, TpSambaClient.UploadListener)","u":"uploadFile(java.lang.String,java.lang.String,com.touptek.utils.TpSambaClient.UploadListener)"},{"p":"com.touptek.utils","c":"TpSambaClient","l":"uploadFile(String, TpSambaClient.UploadListener)","u":"uploadFile(java.lang.String,com.touptek.utils.TpSambaClient.UploadListener)"},{"p":"com.touptek.video","c":"TpIspParam","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoConfig.BitrateMode","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoConfig.VideoCodec","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"com.touptek.video","c":"TpVideoSystem.StreamType","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"com.touptek.video","c":"TpIspParam","l":"values()"},{"p":"com.touptek.video","c":"TpVideoConfig.BitrateMode","l":"values()"},{"p":"com.touptek.video","c":"TpVideoConfig.VideoCodec","l":"values()"},{"p":"com.touptek.video","c":"TpVideoSystem.StreamType","l":"values()"},{"p":"com.touptek.video","c":"TpVideoConfig.BitrateMode","l":"VBR"},{"p":"com.touptek.utils","c":"TpNetworkMonitor.WifiConnectionInfo","l":"WifiConnectionInfo(boolean, String)","u":"%3Cinit%3E(boolean,java.lang.String)"}];updateSearchResults();